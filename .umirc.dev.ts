export default {
  define: {
    'process.env.MEDALENV': 'dev',
    // 'process.env.WEB_URL': 'http://localhost:8005',
    // 'process.env.BASE_URL': 'http://localhost:8005',
    // 'process.env.REDIRECT_URL': 'http://localhost:8005/callback.html',
    // 'process.env.IDENTITY': 'http://**************:8834/',

    'process.env.WEB_URL': 'http://localhost:8888',
    'process.env.BASE_URL': 'http://localhost:8888',
    'process.env.REDIRECT_URL': 'http://localhost:8888/callback.html',
    'process.env.IDENTITY': 'https://app.flowportalcloud.com:8831/',
    // 'process.env.IDENTITY': 'https://**************:8831/',
    'process.env.CLIENT_ID': '87987862-b622-4e1b-81eb-4f99c99916ff',
    'process.env.TENANTID': '44c24f42-d49b-4192-9336-5f2989b87356',
    'process.env.SCOPE': 'api://87987862-b622-4e1b-81eb-4f99c99916ff/default',
    'process.env.CONTRACT_URL': 'http://www.baidu.com',



    // 'process.env.WEB_URL': 'https://pto.lindemobile.cn',
    // 'process.env.BASE_URL': 'https://pto.lindemobile.cn',
    // 'process.env.REDIRECT_URL': 'https://pto.lindemobile.cn/callback.html',  //^ 注释
    // 'process.env.IDENTITY': 'https://lindepto-prd-auth.chinacloudsites.cn/',  //^ 注释
    // 'process.env.CLIENT_ID': 'aee73978-5c25-4979-8440-45dae63b929b',
    // 'process.env.TENANTID': '1562f007-09a4-4fcb-936b-e79246571fc7',
    // 'process.env.SCOPE': 'api://aee73978-5c25-4979-8440-45dae63b929b/TeamsAccess',


  },
};
