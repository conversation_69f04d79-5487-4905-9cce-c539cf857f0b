export default {
  define: {
    'process.env.MEDALENV': 'test',
    'process.env.WEB_URL': 'http://192.168.30.241:8832',
    'process.env.BASE_URL': 'http://192.168.30.241:8832',
    'process.env.REDIRECT_URL': 'http://192.168.30.241:8832/callback.html',
    'process.env.IDENTITY': 'https://app.flowportalcloud.com:8831/',
    // 'process.env.WEB_URL': 'http://10.10.10.151:9102',
    // 'process.env.BASE_URL': 'http://10.10.10.151:9102',
    // 'process.env.REDIRECT_URL': 'http://10.10.10.151:9102/callback.html',
    // 'process.env.IDENTITY': 'http://10.10.10.151:9100/',
    // 'process.env.WEB_URL': 'https://192.168.30.241:9001',
    // 'process.env.BASE_URL': 'https://192.168.30.241:9002',
    // 'process.env.REDIRECT_URL': 'https://192.168.30.241:9001',
    'process.env.CLIENT_ID': '87987862-b622-4e1b-81eb-4f99c99916ff',
    'process.env.TENANTID': '44c24f42-d49b-4192-9336-5f2989b87356',
    'process.env.SCOPE': 'api://87987862-b622-4e1b-81eb-4f99c99916ff/default',
  },
};
