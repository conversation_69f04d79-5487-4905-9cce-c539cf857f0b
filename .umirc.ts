import { defineConfig } from 'umi';
import { theme } from './src/app/config/theme';
export default defineConfig({
  hash: true,
  history: { type: 'hash' },
  nodeModulesTransform: {
    type: 'none',
  },
  publicPath: './',
  favicon: './images/favicon.ico',
  proxy: {
    '/api': {
      // target: 'https://pto.lindemobile.cn/',
      target: 'https://pto-test.lindemobile.cn/',
      //target: 'http://192.168.30.241:8832/',
      // pathRewrite: { '^/api': '' },
      changeOrigin: true,
      secure: false,
      pathRewrite: {
        '^/api': '/api',
      },
    },
  },
  title: false,
  locale: {
    default: 'zh-CN',
    antd: true,
    baseSeparator: '-',
    baseNavigator: true,
  },
  theme: theme,
  fastRefresh: {},
  extraBabelPlugins: ['babel-plugin-styled-components'],
});
