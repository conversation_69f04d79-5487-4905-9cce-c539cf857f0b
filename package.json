{"private": true, "scripts": {"start": "cross-env PORT=8888 UMI_ENV=dev umi dev", "uat": "cross-env UMI_ENV=uat umi build", "pro": "cross-env UMI_ENV=pro umi build", "test": "cross-env UMI_ENV=test umi build", "stage": "cross-env UMI_ENV=stage umi build", "build": "cross-env umi build", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test:coverage": "umi-test --coverage", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.6.2", "@ant-design/pro-form": "^1.28.1", "@ant-design/pro-layout": "^6.26.5", "@ant-design/pro-list": "^1.10.5", "@ant-design/pro-table": "^2.34.5", "@antv/data-set": "^0.11.8", "@antv/g2": "^4.2.8", "@azure/msal-browser": "^2.15.0", "@azure/msal-react": "^1.0.1", "@pnp/graph": "^2.3.0", "@pnp/sp": "^2.3.0", "ahooks": "^2.9.4", "antd": "4.15.2", "axios": "^0.21.1", "babel-polyfill": "^6.26.0", "base64url": "^3.0.1", "bizcharts": "4.x", "crypto-js": "^4.0.0", "dayjs": "^1.10.5", "file-saver": "^2.0.5", "i18next": "^19.9.2", "lodash": "^4.17.21", "moment": "^2.29.1", "msal": "^1.4.17", "oidc-client": "^1.11.5", "qrcode.react": "^1.0.1", "qs": "^6.10.1", "rc-notification": "^4.5.7", "react": "^17.0.0", "react-aad-msal": "^2.3.5", "react-azure-mp": "^1.0.5", "react-contexify": "^5.0.0", "react-dom": "^17.0.1", "react-draggable": "^4.4.5", "react-draggable-tags": "^1.0.5", "react-facebook-login": "^4.1.1", "react-facebook-login-component": "^0.9.2", "react-infinite-scroller": "^1.2.4", "react-json-view": "^1.21.3", "react-linkedin-login-oauth2": "^1.0.9", "react-pdf": "^5.2.0", "react-twitter-login": "^1.3.0", "styled-components": "^5.2.1", "umi-request": "^1.3.5", "viser-react": "^2.4.8", "wangeditor": "^4.6.7"}, "devDependencies": {"@babel/core": "^7.14.3", "@storybook/addon-actions": "^6.3.2", "@storybook/addon-essentials": "^6.3.2", "@storybook/addon-links": "^6.3.2", "@storybook/react": "^6.3.2", "@types/crypto-js": "^4.0.2", "@types/styled-components": "^5.1.7", "@types/webpack-env": "^1.16.0", "@umijs/plugin-locale": "^0.10.7", "@umijs/test": "^3.3.4", "babel-loader": "^8.2.2", "babel-plugin-styled-components": "^1.12.0", "cross-env": "^7.0.3", "css-loader": "^5.2.6", "html-webpack-harddisk-plugin": "^1.0.0", "html-webpack-plugin": "^4.5.2", "less": "^4.1.1", "less-loader": "5.0.0", "lint-staged": "^10.5.3", "prettier": "^2.2.1", "socket.io-client": "^4.1.2", "sp-rest-proxy": "^3.0.6", "style-loader": "^2.0.0", "typescript": "^4.3.5", "umi": "^3.5.3", "yorkie": "^2.0.0"}}