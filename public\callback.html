<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>Waiting...</title>
</head>
<style type="text/css">
  .welcome div img {
    height: 400px;
  }

  @font-face {
    font-family: "LindeDaxGlobal";
    src: url('./lindeglrg.ttf');
  }

  .welcome div .pto {
    font-family: 'LindeDaxGlobal';
    color: #005293;
    font-size: 48px;
    height: 200px;
    text-align: center;
  }

  .spinner {
    margin: 200px auto;
    width: 100px;
    height: 100px;
    position: relative;
  }

  .container1>div,
  .container2>div,
  .container3>div {
    width: 10px;
    height: 10px;
    background-color: #005293;

    border-radius: 100%;
    position: absolute;
    -webkit-animation: bouncedelay 1.2s infinite ease-in-out;
    animation: bouncedelay 1.2s infinite ease-in-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
  }

  .spinner .spinner-container {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .container2 {
    -webkit-transform: rotateZ(45deg);
    transform: rotateZ(45deg);
  }

  .container3 {
    -webkit-transform: rotateZ(90deg);
    transform: rotateZ(90deg);
  }

  .circle1 {
    top: 0;
    left: 0;
  }

  .circle2 {
    top: 0;
    right: 0;
  }

  .circle3 {
    right: 0;
    bottom: 0;
  }

  .circle4 {
    left: 0;
    bottom: 0;
  }

  .container2 .circle1 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s;
  }

  .container3 .circle1 {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s;
  }

  .container1 .circle2 {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s;
  }

  .container2 .circle2 {
    -webkit-animation-delay: -0.8s;
    animation-delay: -0.8s;
  }

  .container3 .circle2 {
    -webkit-animation-delay: -0.7s;
    animation-delay: -0.7s;
  }

  .container1 .circle3 {
    -webkit-animation-delay: -0.6s;
    animation-delay: -0.6s;
  }

  .container2 .circle3 {
    -webkit-animation-delay: -0.5s;
    animation-delay: -0.5s;
  }

  .container3 .circle3 {
    -webkit-animation-delay: -0.4s;
    animation-delay: -0.4s;
  }

  .container1 .circle4 {
    -webkit-animation-delay: -0.3s;
    animation-delay: -0.3s;
  }

  .container2 .circle4 {
    -webkit-animation-delay: -0.2s;
    animation-delay: -0.2s;
  }

  .container3 .circle4 {
    -webkit-animation-delay: -0.1s;
    animation-delay: -0.1s;
  }

  @-webkit-keyframes bouncedelay {

    0%,
    80%,
    100% {
      -webkit-transform: scale(0.0)
    }

    40% {
      -webkit-transform: scale(1.0)
    }
  }

  @keyframes bouncedelay {

    0%,
    80%,
    100% {
      transform: scale(0.0);
      -webkit-transform: scale(0.0);
    }

    40% {
      transform: scale(1.0);
      -webkit-transform: scale(1.0);
    }
  }
</style>

<body class="welcome">
  <div>
    <div class="spinner">
      <div class="spinner-container container1">
        <div class="circle1"></div>
        <div class="circle2"></div>
        <div class="circle3"></div>
        <div class="circle4"></div>
      </div>
      <div class="spinner-container container2">
        <div class="circle1"></div>
        <div class="circle2"></div>
        <div class="circle3"></div>
        <div class="circle4"></div>
      </div>
      <div class="spinner-container container3">
        <div class="circle1"></div>
        <div class="circle2"></div>
        <div class="circle3"></div>
        <div class="circle4"></div>
      </div>
    </div>
    <br />
    <p class="pto">Welcome to Linde PTO</p>
  </div>
  <script src="oidc-client.js"></script>
  <script>
    // console.log('callback.html')
    var mgr = new Oidc.UserManager({
      userStore: new Oidc.WebStorageStateStore({ store: window.sessionStorage }),
      loadUserInfo: true,
      filterProtocolClaims: true
    });
    let redirect = ""
    // console.log('up', sessionStorage.getItem('re'))
    if (sessionStorage.getItem('re')) {
      redirect = "/" + window.atob(sessionStorage.getItem('re'))
    }
    mgr.signinRedirectCallback().then(function (user) {
      console.log('redirect', redirect)
      // console.log('signinRedirectCallback')
      window.location.href = redirect || "/";
      // window.location.href = "/#/pto/supplier/initstatement";
    }).catch(function (err) {
      window.location.href = redirect || "/";
      // window.location.href = "/#/pto/supplier/initstatement";
    });

  </script>
</body>

</html>