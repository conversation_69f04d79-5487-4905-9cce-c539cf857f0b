* {
    margin: 0;
    padding: 0;
}

@font-face {
    font-family: "LindeDaxGlobal-Medium";
    src: url('../lib/lindeglmd.ttf');
}

@font-face {
    font-family: "LindeDaxGlobal-Regular";
    src: url('../lib/lindeglrg.ttf');
}

body,
html {
    width: 100%;
    height: 100%;
    background: url(../鑳屾櫙鍥�.png) no-repeat;
    background-size: 100% 100%;
    margin: 0px;
}

.header {
    width: 100%;
    height: 150px;
}

    .header img {
        float: left;
        margin-left: 75px;
        margin-top: 37px;
    }

.title {
    width: 100%;
    font-size: 68px;
    font-weight: 400;
    color: #fff;
    text-align: center;
    font-family: 'LindeDaxGlobal-Medium';
    margin-top: 0%;
}

.titlepto {
    border-bottom: 5px solid rgb(233, 152, 11);
}

.titlesy {
    margin-left: 60px;
}

.setting {
    width: 90%;
    height: 350px;
    background: rgba(69, 101, 139, 0.5);
    background-size: 100% 100%;
    margin: 0 auto;
    margin-top: 130px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: relative;
    border-radius: 25px;
    border: 1px solid rgb(86, 143, 198);
    box-shadow: 0 0 10px rgb(86, 143, 198);
    font-family: 'LindeDaxGlobal-Regular';
}

.supbox {
    width: 220px;
    height: 170px;
    background: url('../images/supplier.png');
    background-size: 100% 100%;
    border-radius: 15px;
    position: relative;
    cursor: pointer;
}

    .supbox img {
        position: absolute;
        left: 33%;
        top: 20%;
    }

.addbox {
    width: 220px;
    height: 170px;
    background: url('../images/supplier.png');
    background-size: 100% 100%;
    border-radius: 15px;
    position: relative;
    cursor: pointer;
}

    .addbox img {
        position: absolute;
        left: 33%;
        top: 20%;
    }

.txt {
    width: 220px;
    color: #fff;
    text-align: center;
    margin-top: 10px;
    font-size: 28px;
}
.foot {
    color: #fff;
    text-align: center;
    position: fixed;
    bottom: 10px;
    font-size: 12px;
     left: 50%;
  transform: translate(-50%, -50%);
}


.sup::before {
    content: "";
    width: 3px;
    height: 88px;
    position: absolute;
    top: 106px;
    left: 102px;
}
