<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <title>PTO SYSTEM</title>
    <script src="./lib/jquery.slim.min.js"></script>
    <script src="https://alcdn.msauth.net/browser/2.13.1/js/msal-browser.js"
        integrity="sha384-7hwr87O1w6buPsX92CwuRaz/wQzachgOEq+iLHv0ESavynv6rbYwKImSl7wUW3wV"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
        integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo"
        crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js"
        integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6"
        crossorigin="anonymous"></script>
    <link href="./css/newIndex.css" rel="stylesheet" />
    <link rel="shortcut icon" type="image/ico" href="./images/PTO.png" />
    <script src="./lib/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="./lib/authConfig.js"></script>
    <script type="text/javascript" src="./lib/graphConfig.js"></script>
    <script type="text/javascript" src="./lib/ui.js"></script>
    <script src="./lib/jsencrypt.js"></script>
    <script type="text/javascript" src="./lib/authPopup.js"></script>
    <script type="text/javascript" src="./lib/graph.js"></script>
    <style type="text/css">
        body {
            width: 100%;
            height: 100%;
            background: url(./images/ldbg.png) no-repeat;
            background-size: 100% 100%;
            margin: 0px;
        }
    </style>
    <script type="text/javascript">
        !function (T, l, y) { var S = T.location, k = "script", D = "instrumentationKey", C = "ingestionendpoint", I = "disableExceptionTracking", E = "ai.device.", b = "toLowerCase", w = "crossOrigin", N = "POST", e = "appInsightsSDK", t = y.name || "appInsights"; (y.name || T[e]) && (T[e] = t); var n = T[t] || function (d) { var g = !1, f = !1, m = { initialize: !0, queue: [], sv: "5", version: 2, config: d }; function v(e, t) { var n = {}, a = "Browser"; return n[E + "id"] = a[b](), n[E + "type"] = a, n["ai.operation.name"] = S && S.pathname || "_unknown_", n["ai.internal.sdkVersion"] = "javascript:snippet_" + (m.sv || m.version), { time: function () { var e = new Date; function t(e) { var t = "" + e; return 1 === t.length && (t = "0" + t), t } return e.getUTCFullYear() + "-" + t(1 + e.getUTCMonth()) + "-" + t(e.getUTCDate()) + "T" + t(e.getUTCHours()) + ":" + t(e.getUTCMinutes()) + ":" + t(e.getUTCSeconds()) + "." + ((e.getUTCMilliseconds() / 1e3).toFixed(3) + "").slice(2, 5) + "Z" }(), iKey: e, name: "Microsoft.ApplicationInsights." + e.replace(/-/g, "") + "." + t, sampleRate: 100, tags: n, data: { baseData: { ver: 2 } } } } var h = d.url || y.src; if (h) { function a(e) { var t, n, a, i, r, o, s, c, u, p, l; g = !0, m.queue = [], f || (f = !0, t = h, s = function () { var e = {}, t = d.connectionString; if (t) for (var n = t.split(";"), a = 0; a < n.length; a++) { var i = n[a].split("="); 2 === i.length && (e[i[0][b]()] = i[1]) } if (!e[C]) { var r = e.endpointsuffix, o = r ? e.location : null; e[C] = "https://" + (o ? o + "." : "") + "dc." + (r || "services.visualstudio.com") } return e }(), c = s[D] || d[D] || "", u = s[C], p = u ? u + "/v2/track" : d.endpointUrl, (l = []).push((n = "SDK LOAD Failure: Failed to load Application Insights SDK script (See stack for details)", a = t, i = p, (o = (r = v(c, "Exception")).data).baseType = "ExceptionData", o.baseData.exceptions = [{ typeName: "SDKLoadFailed", message: n.replace(/\./g, "-"), hasFullStack: !1, stack: n + "\nSnippet failed to load [" + a + "] -- Telemetry is disabled\nHelp Link: https://go.microsoft.com/fwlink/?linkid=2128109\nHost: " + (S && S.pathname || "_unknown_") + "\nEndpoint: " + i, parsedStack: [] }], r)), l.push(function (e, t, n, a) { var i = v(c, "Message"), r = i.data; r.baseType = "MessageData"; var o = r.baseData; return o.message = 'AI (Internal): 99 message:"' + ("SDK LOAD Failure: Failed to load Application Insights SDK script (See stack for details) (" + n + ")").replace(/\"/g, "") + '"', o.properties = { endpoint: a }, i }(0, 0, t, p)), function (e, t) { if (JSON) { var n = T.fetch; if (n && !y.useXhr) n(t, { method: N, body: JSON.stringify(e), mode: "cors" }); else if (XMLHttpRequest) { var a = new XMLHttpRequest; a.open(N, t), a.setRequestHeader("Content-type", "application/json"), a.send(JSON.stringify(e)) } } }(l, p)) } function i(e, t) { f || setTimeout(function () { !t && m.core || a() }, 500) } var e = function () { var n = l.createElement(k); n.src = h; var e = y[w]; return !e && "" !== e || "undefined" == n[w] || (n[w] = e), n.onload = i, n.onerror = a, n.onreadystatechange = function (e, t) { "loaded" !== n.readyState && "complete" !== n.readyState || i(0, t) }, n }(); y.ld < 0 ? l.getElementsByTagName("head")[0].appendChild(e) : setTimeout(function () { l.getElementsByTagName(k)[0].parentNode.appendChild(e) }, y.ld || 0) } try { m.cookie = l.cookie } catch (p) { } function t(e) { for (; e.length;)!function (t) { m[t] = function () { var e = arguments; g || m.queue.push(function () { m[t].apply(m, e) }) } }(e.pop()) } var n = "track", r = "TrackPage", o = "TrackEvent"; t([n + "Event", n + "PageView", n + "Exception", n + "Trace", n + "DependencyData", n + "Metric", n + "PageViewPerformance", "start" + r, "stop" + r, "start" + o, "stop" + o, "addTelemetryInitializer", "setAuthenticatedUserContext", "clearAuthenticatedUserContext", "flush"]), m.SeverityLevel = { Verbose: 0, Information: 1, Warning: 2, Error: 3, Critical: 4 }; var s = (d.extensionConfig || {}).ApplicationInsightsAnalytics || {}; if (!0 !== d[I] && !0 !== s[I]) { var c = "onerror"; t(["_" + c]); var u = T[c]; T[c] = function (e, t, n, a, i) { var r = u && u(e, t, n, a, i); return !0 !== r && m["_" + c]({ message: e, url: t, lineNumber: n, columnNumber: a, error: i }), r }, d.autoExceptionInstrumented = !0 } return m }(y.cfg); function a() { y.onInit && y.onInit(n) } (T[t] = n).queue && 0 === n.queue.length ? (n.queue.push(a), n.trackPageView({})) : a() }(window, document, {
            src: "https://js.monitor.azure.com/scripts/b/ai.2.min.js", // The SDK URL Source
            crossOrigin: "anonymous",
            cfg: { // Application Insights Configuration
                connectionString: 'InstrumentationKey=0ffe9001-07d4-cbca-81f4-750df418ed6e;EndpointSuffix=applicationinsights.azure.cn;IngestionEndpoint=https://chinaeast2-0.in.applicationinsights.azure.cn/'
            }
        });
    </script>
</head>

<body>
    <form action="" method="post">
        <div class="box">
            <div class="header">
                <img src="./images/ld_lg.png" />
            </div>
            <div class="title"><span class="titlepto">PTO</span><span class="titlesy">SYSTEM</span></div>
            <div class="setting">
                <div class="sup">
                    <div class="supbox"
                        onclick="window.location.href='https://lindepto-prd-auth.chinacloudsites.cn/Account/LoginPageExt'">
                        <img src="./images/ld_sp.png" />
                    </div>
                    <h2 class="txt">供应商 & EJV登录</h2>
                </div>
                <div class="add">
                    <div class="addbox" onkeydown="if (event.keyCode == 13) return false;"
                        onclick="window.location.href='https://lindepto-prd-auth.chinacloudsites.cn'">
                        <img src=" ./images/ld_aad.png" />
                    </div>
                    <h2 class="txt">AAD登录
                    </h2>
                </div>
            </div>
        </div>
        <div class="foot">
            <span>
                沪ICP备********号-2
            </span>
            <img src="./images/national_emblem.png" alt="" style="width:16px;">
            <span>沪公网安备**************号</span>
        </div>
        <span><input hidden class="txt" id="input_AT" autocomplete="off" type="text" name="AccessToken"
                value="" /></span>
        <span><input hidden class="txt" id="input_OT" autocomplete="off" type="text" name="OrgType" value="" /></span>
        <input hidden class="txt" id="ipt_key"
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        <button class="btn" hidden id="btn_auth" name="button" value="login">Inner Login</button>
        <input name="__RequestVerificationToken" type="hidden"
            value="CfDJ8F3ksuMpLXdDoQF1Xw4YfFA9rNNR04cquQkrwvpGTcksKA49A0wzx-5ywXLeAlc2dzycZ4Fe27jviZQoudJgcHjk4Q70_Y5Zb4qaohGbW7QQwX9SvoIpBRnb9vu0hPOfZu2vquEp2k526YBUks84oAw" />
    </form>
</body>
<script>
    function reloadCode() {
        $("#vcodeImg").attr("src", "/Identity/Account/VcodeImg?v=" + Math.ceil(Math.random() * 10));
    }
</script>

</html>