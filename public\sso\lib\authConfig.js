/**
 * Configuration object to be passed to MSAL instance on creation. 
 * For a full list of MSAL.js configuration parameters, visit:
 * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-browser/docs/configuration.md 
 */
const msalConfig = {
    auth: {
        //TEST👇
        clientId: "8e0dd5c3-05e2-4622-b3c8-59b651771033",
        authority: "https://login.microsoftonline.com/1562f007-09a4-4fcb-936b-e79246571fc7",
        redirectUri: "https://lindepto-tst-auth.chinacloudsites.cn/",
        tenantId: "1562f007-09a4-4fcb-936b-e79246571fc7",
        scope: "api://8e0dd5c3-05e2-4622-b3c8-59b651771033/TeamsAccess",
        postLogoutRedirectUri: "https://pto-test.lindemobile.cn/#/logout"

        // 生产👇
        // clientId: "aee73978-5c25-4979-8440-45dae63b929b",
        // authority: "https://login.microsoftonline.com/1562f007-09a4-4fcb-936b-e79246571fc7",
        // redirectUri: "https://lindepto-prd-auth.chinacloudsites.cn/",
        // tenantId: "1562f007-09a4-4fcb-936b-e79246571fc7",
        // scope: "api://aee73978-5c25-4979-8440-45dae63b929b/TeamsAccess",
        // postLogoutRedirectUri: "https://pto.lindemobile.cn/#/logout"


    },
    cache: {
        cacheLocation: "sessionStorage", // This configures where your cache will be stored
        storeAuthStateInCookie: false, // Set this to "true" if you are having issues on IE11 or Edge
    },
    system: {
        loggerOptions: {
            loggerCallback: (level, message, containsPii) => {
                if (containsPii) {
                    return;
                }
                switch (level) {
                    case msal.LogLevel.Error:
                        console.error(message);
                        return;
                    case msal.LogLevel.Info:
                        console.info(message);
                        return;
                    case msal.LogLevel.Verbose:
                        console.debug(message);
                        return;
                    case msal.LogLevel.Warning:
                        console.warn(message);
                        return;
                }
            }
        }
    }
};

/**
 * Scopes you add here will be prompted for user consent during sign-in.
 * By default, MSAL.js will add OIDC scopes (openid, profile, email) to any login request.
 * For more information about OIDC scopes, visit: 
 * https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-permissions-and-consent#openid-connect-scopes
 */
const loginRequest = {
    scopes: ["api://aee73978-5c25-4979-8440-45dae63b929b/TeamsAccess"]
};

/**
 * Add here the scopes to request when obtaining an access token for MS Graph API. For more information, see:
 * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-browser/docs/resources-and-scopes.md
 */
const tokenRequest = {
    scopes: ["api://aee73978-5c25-4979-8440-45dae63b929b/TeamsAccess"],
    forceRefresh: false // Set this to "true" to skip a cached token and go to the server to get a new token
};
