import { history } from 'umi';
import { useState } from 'react';
import { notification, Modal, message } from 'antd';
import '@/app/framework';
import Mgr from '@/services/SecurityService';
import { MsalAuthProvider, LoginType } from 'react-aad-msal';
import { queryLoginUser } from '@/app/request/requestApi';
import { BrowserCacheLocation, PublicClientApplication } from '@azure/msal-browser';

notification.config({
  placement: 'topRight',
  top: 60,
});

const options = {
  loginType: LoginType.Popup,
  tokenRefreshUri: window.location.origin + '/auth.html',
};
const config = {
  auth: {
    authority: 'https://login.microsoftonline.com/5dbf1add-202a-4b8d-815b-bf0fb024e033',
    clientId: process.env.CLIENT_ID,
    redirectUri: 'http://localhost:8834',
    postLogoutRedirectUri: process.env.WEB_URL + '/#/logout',
  },
  cache: {
    cacheLocation: BrowserCacheLocation.LocalStorage,
    storeAuthStateInCookie: true,
  },
};
const authenticationParameters = {
  scopes: [
    '<property (i.e. user.read)>',
    'https://<your-tenant-name>.onmicrosoft.com/<your-application-name>/<scope (i.e. demo.read)>',
  ],
};
const authProvider = new MsalAuthProvider(config, authenticationParameters, options);

//解析路由权限-区分供应商和内部用户
var userType = '';
var authRoutes = [];
const parseTree = (datas) => {
  for (var i in datas) {
    datas[i]['name'] = datas[i].authName;
    if (datas[i].path) {
      authRoutes.push(datas[i].path);
      authRoutes.push('/pto/supplier/details');
      authRoutes.push('/pto/internal/StatementDetails');
      authRoutes.push('/pto/internal/FormatpoDetails');
    }
    if (datas[i].subTree.length != 0) {
      parseTree(datas[i].subTree);
    }
  }
};

//获取登录用户信息和对应权限菜单-权限按钮
export async function render(oldRender) {
  console.log('src -> app.tsx -> render');
  const Mgrs = new Mgr();
  const result = await Mgrs.getSignedIn();
  console.log('result ==>>> 2', result)
  if (!result) {
    if (window.location.pathname === '/' && window.location.hash === '#/') {
      window.location.replace('/sso/index.html');
      return;
    } else if (location.hash.includes('pto')) {
      sessionStorage.setItem('re', window.btoa(location.hash));
      return;
    }


    // if (location.hash) {
    //   sessionStorage.setItem('re', window.btoa(location.hash));
    // }
    // return;
  }
  const infos = await Mgrs.getProfile();
  if (!infos) return;
  try {
    const usersInfo = await queryLoginUser();
    if (usersInfo?.success) {
      // console.log('第一次queryLoginUser')
      parseTree(usersInfo?.data?.authList);
      userType = usersInfo?.data?.userType;
      oldRender();
    } else {
      //await后接口报错则此代码不会执行
      // Modal.warning({
      //   title: 'Tips',
      //   centered: true,
      //   content: usersInfo.msg,
      //   okText: '确定登出',
      //   onOk: () => {
      //     const Mgrs = new Mgr();
      //     if (usersInfo?.data?.userType == 'inner') {
      //       authProvider.logout();
      //     } else {
      //       Mgrs.signOut();
      //     }
      //   },
      // });
      return;
    }
  } catch (error) {
    {
      error.response.data.msg
        ? Modal.warning({
          title: 'Tips',
          centered: true,
          content: error.response.data.msg,
          okText: '确定登出',
          onOk: () => {
            const Mgrs = new Mgr();
            if (error.response?.data?.userType == 'inner') {
              authProvider.logout();
            } else {
              Mgrs.signOut();
            }
          },
        })
        : message.error('服务器500');
    }
  }
}
//第一次渲染时执行一次
export function patchRoutes({ routes }) {
  console.log('src -> app.tsx-> patchRoutes');
  // console.log(JSON.parse(sessionStorage.getItem('authRoutes')));
  let authRoutes = JSON.parse(sessionStorage.getItem('authRoutes') ?? '[]');
  let newRoutes = routes[0].routes.map((sys) => {
    if (sys.path == '/CIM') {
      let newSys = JSON.parse(JSON.stringify(sys));
      let newSysRoutes = newSys.routes?.filter((route, index) => authRoutes.indexOf(route.path) != -1);
      newSys.routes = newSysRoutes;
      return newSys;
    }
    return sys;
  });
  return newRoutes;
}
//路由变化时也会执行
export async function onRouteChange({ location, routes, action }) {
  // console.log('src -> app.tsx-> onRouteChange', location.pathname);
  // console.log('authRoutes', authRoutes);
  // console.log('userType', userType);
  // console.log('---------------------------');
  if (location.pathname == '/') {
    userType == 'Outer' ? history.replace('/pto/supplierHome') : history.replace('/pto/internalHome');
  } else if (location.pathname == '/pto/supplier') {
    history.replace('/pto/supplier/initstatement');
  } else if (location.pathname == '/pto/internal') {
    history.replace('/pto/internal/Billingmanagement');
  } else if (location.pathname == '/pto/internalSetting') {
    history.replace('/pto/internalSetting/supplierInfo');
  } else if (location.pathname == '/logout') {
    const Mgrs = new Mgr();
    Mgrs.signOut();
    return;
  } else if (
    // JSON.parse(sessionStorage.getItem('authRoutes') ?? '[]').indexOf(location.pathname) == -1 &&
    authRoutes?.indexOf(location.pathname) == -1 &&
    location.pathname != '/404' &&
    location.pathname != '/logout'
  ) {
    console.log('跳转404');
    history.replace('/404');
  }
}
