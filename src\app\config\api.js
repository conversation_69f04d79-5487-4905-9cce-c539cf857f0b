export const Api = {
  ReflushToken: '/Api/User/Authentication/ReflushToken', // 刷新token
  Login: '/Api/User/Authentication/Login', // 登录
  SocialLogin: '/Api/User/Authentication/ThirdPartyLogin', //第三方社交媒体登录登录
  AuthCodesByAccount: '/Api/Metadata/Feature/CurrentFeature', //获取账号对应的功能权限
  QueryLoginUser: '/api/System/QueryLoginUser', // 获取用户权限

  // #region
  ImportBilling: '/api/Billing/ImportBilling',
  QueryBillingDetailById: '/api/Billing/QueryBillingDetailById',
  // QueryBillingDraftPageInfo: '/api/Billing/QueryBillingDraftPageInfo',
  InsertBillingEntry: '/api/Billing/InsertBillingEntry',
  DelBillingEntry: '/api/Billing/DelBillingEntry',
  SaveBillingDraft: '/api/Billing/SaveBillingDraft',
  SubmitBillingInfo: '/api/Billing/SubmitBillingInfo',
  // #endregion

  // 通用
  QueryAllLabels: '/api/System/QueryAllLabels', // 所有标签
  // 系统设置
  QueryManLabelPageInfo: '/api/System/QueryManLabelPageInfo', // 客户动态标签管理-列表
  InsertManLabel: '/api/System/InsertManLabel', // 客户动态标签管理-新增
  ModifyManLabel: '/api/System/ModifyManLabel', // 客户动态标签管理-修改
  QueryManLabelDetail: '/api/System/QueryManLabelDetail', // 客户动态标签管理-详情
  DeleManLable: '/api/System/DeleManLable', // 客户动态标签管理-删除
  QueryUserPageInfo: '/api/System/QueryUserPageInfo', // 组织架构管理（人员管理）-列表
  InsertUserInfo: '/api/System/InsertUserInfo', // 组织架构管理（人员管理）-新增
  ModifyUserInfo: '/api/System/ModifyUserInfo', // 组织架构管理（人员管理）-修改
  DeleUser: '/api/System/DeleUser', // 组织架构管理（人员管理）-禁用
  RecoverUser: '/api/System/RecoverUser', // 组织架构管理（人员管理）-启用
  UserRoleQuery: '/api/System/UserRoleQuery', // 组织架构管理（人员管理）-用户角色
  UpdateUserAccess: '/api/System/UpdateUserAccess', // 组织架构管理（人员管理）-修改用户角色
  QueryLogPageInfo: '/api/System/QueryLogPageInfo', // 日志查看-列表
  QueryRolePageInfo: '/api/System/QueryRolePageInfo', // 角色管理-列表
  InsertRole: '/api/System/InsertRole', // 角色管理-新增
  ModifyRole: '/api/System/ModifyRole', // 角色管理-修改
  QueryRoleMapUsers: '/api/System/QueryRoleMapUsers', // 角色管理-人员维护已选列表
  RoleBingUser: '/api/System/RoleBingUser', // 角色管理-角色绑定人员
  RoleBingLabel: '/api/System/RoleBingLabel', // 角色管理-角色绑定标签
  QueryRoleMapLabels: '/api/System/QueryRoleMapLabels', // 角色管理-标签授权已选列表
  QueryRoleMapAuthTrees: '/api/System/QueryRoleMapAuthTrees', // 角色管理-功能授权树
  RoleBingAuth: '/api/System/RoleBingAuth', // 角色管理-功能授权
  // 客户管理
  QueryCustomerPageInfo: '/api/Customer/QueryCustomerPageInfo', // 客户信息-列表
  QueryCustomerDetail: '/api/Customer/QueryCustomerDetail', // 客户信息-修改详情获取
  QueryCustomerInfo: '/api/Customer/QueryCustomerInfo', // 客户信息-查看详情获取
  ModifyCustomerInfo: '/api/Customer/ModifyCustomerInfo', // 客户信息-修改
  QueryAuthLabels: '/api/Customer/QueryAuthLabels', // 客户信息-全部标签
  BatchModifyCustomer: '/api/Customer/BatchModifyCustomer', // 客户信息-批量修改
  ModifyUserConfig: '/api/System/ModifyUserConfig', // 客户信息-自定义设置
  QueryReportParams: '/api/Customer/QueryReportParams', // 客户信息-获取所有
  SyncQCCData: '/api/Customer/SyncQCCData', //客户信息-手动同步QCC
  SyncAzureData: '/api/Customer/SyncAzureData', // 客户信息-SuperAdmin
  //对账单1

  checkSubmitValid: '/api/Billing/CheckSubmitValid',
  QueryReturnEntryPageInfo: '/api/Billing/QueryReturnEntryPageInfo', //被退回的条目
  QueryToCompareEntryPageInfo: '/api/Billing/QueryToCompareEntryPageInfo', //待对比条目查询
  QuerySettlEntryPageInfo: '/api/Billing/QuerySettlEntryPageInfo', //已结算条目查询接口
  QueryToBeSettlEntryPageInfo: '/api/Billing/QueryToBeSettlEntryPageInfo', //待结算条目查询接口
  QueryBillingReturnPageInfo: '/api/Billing/QueryBillingReturnPageInfo', //查询被退回的对账单
  QueryBillingDraftPageInfo: '/api/Billing/QueryBillingDraftPageInfo', //查询暂存的草稿
  ExportReturnEntryList: '/api/Billing/ExportReturnEntryList', //导出被退回的条目
  ExportToCompareEntryData: '/api/Billing/ExportToCompareEntryList', //导出待核对的条目
  ExportSettlEntryList: '/api/Billing/ExportSettlEntryList', //导出已结算的条目
  ExportBillingDraftData: '/api/Billing/ExportBillingDraftData', //导出草稿
  ExportToBeSettlEntryList: '/api/Billing/ExportToBeSettlEntryList', //导出待结算的条目
  EditReturnEntryInfo: '/api/Billing/EditReturnEntryInfo', //编辑被退回条目
  delReturnEntryInfo: '/api/Billing/DelReturnEntryInfo', //删除被退回条目
  delBillingInfoById: '/api/Billing/DelBillingInfoById', //删除被退回的对账单 删除暂存的草稿
  queryEntryDetail: '/api/Billing/QueryEntryDetail', //待核对 待结算 已结算 条目详情查询

  //对账单2
  queryInnerHomeList: '/api/InnerHome/QueryInnerHomeList', //首页列表
  queryInnerHomeEntryList: '/api/InnerHome/QueryInnerHomeEntryList', //首页对账单
  opearteEntryStatus: '/api/InnerHome/OpearteEntryStatus', //退回，数量比对通过，价格比对通过
  queryInnerHomeFinList: '/api/InnerHome/QueryInnerHomeFinList', //查询页面mtop 待结算 已结算
  queryInnerHomeBillingById: '/api/InnerHome/QueryInnerHomeBillingById', //根据id查询对账单详情
  queryInnerHomeStatementById: '/api/InnerHome/QueryInnerHomeStatementById', //根据id查看结算单详情
  generateFormatPo: '/api/InnerHome/GenerateFormatPoDraft', //生成格式PO
  SubmitPoInfo: '/api/InnerHome/SubmitPoInfo', //回写PO号
  getFormatPoDraftList: '/api/InnerHome/GetFormatPoDraftList', //获取格式po预览合集
  SubmitFormatPo: '/api/InnerHome/SubmitFormatPo', //提交生成格式PO
  CancelFormatPo: '/api/InnerHome/CancelFormatPo', //格式PO生成取消
  submitUseSettlementQuantity: '/api/InnerHome/SubmitUseSettlementQuantity', //提交数量结算方式
  querySupplierNameInfo: '/api/System/QuerySupplierNameInfo', //获取供应商名称
  querySupplierTypeInfo: '/api/System/QuerySupplierTypeInfo', //获取供应商类型
  queryClearingInfo: '/api/System/QueryClearingInfo', //获取结算公司
  queryProductInfo: '/api/System/QueryProductInfo', //获取产品信息名称
  getOperateList: '/api/InnerHome/GetOperateList', // 获取操作集合
  importPoList: '/api/InnerHome/ImportPoList', //批量回写PO号
  downLoadToBeSettl: '/api/InnerHome/DownLoadToBeSettl', //下载
  exportInnerHomeEntryList: '/api/InnerHome/ExportInnerHomeEntryList', //首页查看对账单详情 导出
  exportInnerHomeFinList: '/api/InnerHome/ExportInnerHomeFinList', // 查看结算详情 导出
  queryInnerHomeSpecialEntryList: '/api/InnerHome/QueryInnerHomeSpecialEntryList', //主页特殊条目列表
  queryOrderNumberList: '/api/Billing/QueryOrderNumberList', //订单号查询
  exportOrderNumberData: '/api/Billing/ExportOrderNumberData', // 订单号导出
  queryOrderDetail: '/api/Billing/QueryOrderDetail', //查询订单号详情

  queryInnerBillingList: '/api/InnerBilling/QueryInnerBillingList', //查询对账单（内部对账）
  queryInnerBillingPoList: '/api/InnerBilling/QueryInnerBillingPoList', //查询结算单（内部对账）
  queryAttachmentPageList: '/api/InnerBilling/QueryAttachmentPageList', // 附件分页查询
  insertAttachmentDetail: '/api/InnerBilling/InsertAttachmentDetail', // 新增附件记录
  uploadAttachment: '/api/InnerBilling/UploadAttachment', //上传附件
  deleteAttachmentDetail: '/api/InnerBilling/DeleteAttachmentDetail', //删除
  downloadAttachment: '/api/InnerBilling/DownloadAttachment', //下载附件
  queryClearningCompanyDetail: '/api/Billing/QueryClearningCompanyDetail', //开票信息
  queryProvisionReportFormPage: '/api/ReportForm/QueryProvisionReportFormPage', //计提报表列表查询
  deleteProvisionData: '/api/ReportForm/DeleteProvisionData', //删除计提报表数据
  updateProvisionData: '/api/ReportForm/UpdateProvisionData', //更新计提报表
  exportProvisionData: '/api/ReportForm/ExportProvisionData ', //导出计提报表数据
  queryProductGroup: '/api/ReportForm/QueryProductGroup', //查询产品组
  submitSettlPoInfo: '/api/InnerHome/SubmitSettlPoInfo', //已结算回填订单号 只要订单号
  queryClearCompanyBySupplier: '/api/ReportForm/QueryClearCompanyBySupplier', //查询供应商结算公司列表
  ImportPoReport: '/api/InnerBilling/ImportPoReport', //POReport导入
  exportBillingDetailListById: '/api/InnerHome/ExportBillingDetailListById', //根据id导出对账单详情
  exportFinBillingDetailListById: '/api/InnerHome/ExportFinBillingDetailListById', //根据id导出结算单
  dataVerification: '/api/ReportForm/DataVerification', //计提提交
  exportSupplierList: '/api/System/ExportSupplierList', //导出供应商
  exportUserList: '/api/System/ExportUserList', //导出用户信息
  exportProductList: '/api/System/ExportProductList', //导出产品信息
  exportClearingList: '/api/System/ExportClearingList', //导出结算公司
  exportEmailEJVList: '/api/System/ExportEmailEJVList', // 导出PM邮箱信息
  exportEmailOSList: '/api/System/ExportEmailOSList', // 导出采购
  exportFleetEmailSelList: '/api/System/ExportFleetEmailSelList', //导出车队邮箱自提
  exportFleetEmailDeliveryList: '/api/System/ExportFleetEmailDeliveryList', // 导出车队邮箱地址送货
  exportSourceManageList: '/api/System/ExportSourceManageList', //多货源点管理导出
  exportSettlementList: '/api/System/ExportSettlementList', // 不在系统结算项
  deleteReturnEntryList: '/api/Billing/DeleteReturnEntryList', //批量删除被退回的条目
  delBillingInfoByIdList: '/api/Billing/DelBillingInfoByIdList', //批量删除对账单
  exportInnerBillingList: '/api/InnerHome/ExportInnerBillingList', //导出对账单
  exportInnerBillingPoList: '/api/InnerHome/ExportInnerBillingPoList', //导出结算单
  EmailToFleet: '/api/InnerHome/EmailToFleet', //邮件至车队
  EmailToPurchase: '/api/InnerHome/EmailToPurchase', //邮件至采购

  //二期
  querySalesVenderList: '/api/System/QuerySalesVenderList', //供应商映射管理列表
  delSalesVender: '/api/System/DelSalesVender', //供应商列表删除
  submitSalesVender: '/api/System/SubmitSalesVender', //新增/编辑供应商列表
  querySalesSapBuCodeList: '/api/System/QuerySalesSapBuCodeList', //sap映射管理
  delSalesSapBuCode: '/api/System/DelSalesSapBuCode', //删除sap映射管理
  submitSalesSapBuCode: '/api/System/SubmitSalesSapBuCode', //新增/修改sap映射管理
  ExportAllDataStatusData: '/api/ReportForm/ExportAllDataStatusData', //导出整体数据状态分析表格

  QueryAllDataQualityAlyList: '/api/ReportForm/QueryAllDataQualityAlyList', //整体数据质量分析表格统计
  QueryPurchaseAnalysisList: '/api/ReportForm/QueryPurchaseAnalysisList', //PM/采购数量表格统计数据
  QuerySupplierDataAnalysisList: '/api/ReportForm/QuerySupplierDataAnalysisList', //供应商数据分析
  QueryFleetAnalysisList: '/api/ReportForm/QueryFleetAnalysisList', //车队质量分析
  QueryAllDataStatusAlyList: '/api/ReportForm/QueryAllDataStatusAlyList', //整体数据状态分析

  QueryAllDataStatusAlyChartsList: '/api/ReportForm/QueryAllDataStatusAlyChartsList', //整体数据状态分析柱状图
  QueryFleetChartsList: '/api/ReportForm/QueryFleetChartsList', //车队图表统计
  QueryPurchaseChartsList: '/api/ReportForm/QueryPurchaseChartsList', //PM/采购数量柱状图统计数据
  QuerySupplierDataChartsList: '/api/ReportForm/QuerySupplierDataChartsList', //供应商柱状统计数据
  QueryFleetLeaderList: '/api/ReportForm/QueryFleetLeaderList', //车队负责人
  QueryPurchaseLeaderList: '/api/ReportForm/QueryPurchaseLeaderList', //pm采购负责人
  ExportAllDataQualityData: '/api/ReportForm/ExportAllDataQualityData', //导出整体数据
  ExportFleetAnalysisData: '/api/ReportForm/ExportFleetAnalysisData', //导出车队数据
  ExportPurchaseAnalysisData: '/api/ReportForm/ExportPurchaseAnalysisData', //导出PM/采购信息
  QueryQuantityCompareFailedDetail: '/api/ReportForm/QueryQuantityCompareFailedDetail', //数量比对失败明细
  QueryPriceCompareFailedDetail: '/api/ReportForm/QueryPriceCompareFailedDetail', //价格比对失败明细
  BillingReturnDetailDo: '/api/ReportForm/BillingReturnDetailDo', //数量退回明细
  ExportSupplierAnalysisData: '/api/ReportForm/ExportSupplierAnalysisData', //导出供应商
  // GetFormatMergePoPreview: '/api/System/GetFormatMergePoPreview', //合并PO生成FO
  GetFormatMergePoPreview: '/api/System/GetFormatMergePoPreview', //获取合并PO预览合集
  CancelFormatMergePo: '/api/System/CancelFormatMergePo', //PO生成取消
  SubmitFormatMergePo: '/api/System/SubmitFormatMergePo', //提交生成格式PO
  SubmitDocumentNumber: '/api/Billing/SubmitDocumentNumber', //更新DN
  QueryVendorUpAmountList: '/api/System/QueryVendorUpAmountList', // 分页检索最大限额配置
  DelVendorUpAmount: '/api/System/DelVendorUpAmount', // 删除最大限额配置
  SubmitVendorUpAmount: '/api/System/SubmitVendorUpAmount', // 提交最大限额配置信息
  UpdatePgrPostingDate: '/api/System/UpdatePgrPostingDate', //

  // 内部平台-系统设置
  InsertUserInfo: '/api/System/InsertUserInfo',
  ModifyUserInfo: '/api/System/ModifyUserInfo',
  QueryUserFindInfo: '/api/System/QueryUserFindInfo',
  ResetPass: '/api/System/ResetPass',
  DeleUser: '/api/System/DeleUser',
  RecoverUser: '/api/System/RecoverUser',
  QueryUserInfo: '/api/System/QueryUserInfo',
  DeleteUser: '/api/System/DeleteUser',
  QueryUserPageInfo: '/api/System/QueryUserPageInfo',
  QueryRolePageInfo: '/api/System/QueryRolePageInfo',
  InsertRole: '/api/System/InsertRole',
  ModifyRole: '/api/System/ModifyRole',
  DeleteRole: '/api/System/DeleteRole',
  QueryRoleFind: '/api/System/QueryRoleFind',
  AuthTreeJoin: '/api/System/AuthTreeJoin',
  RoleBingUser: '/api/System/RoleBingUser',
  QueryRoleMapUsers: '/api/System/QueryRoleMapUsers',
  QueryRoleInfo: '/api/System/QueryRoleInfo',

  InsertQuerySupplier: '/api/System/InsertQuerySupplier',
  QueryCustomerOfCurrentUser: '/api/System/QueryCustomerOfCurrentUser',
  QuerySupplierNameInfo: '/api/System/QuerySupplierNameInfo',
  QueryEJVSupplierNameInfo: '/api/System/QueryEJVSupplierNameInfo',
  QuerySupplierTypeInfo: '/api/System/QuerySupplierTypeInfo',
  QuerySupplierList: '/api/System/QuerySupplierList',
  DeleSupplier: '/api/System/DeleSupplier',
  RecoverSupplier: '/api/System/RecoverSupplier',
  DeleteSupplier: '/api/System/DeleteSupplier',
  QuerySupplierFind: '/api/System/QuerySupplierFind',
  ModifySupplierInfo: '/api/System/ModifySupplierInfo',

  QueryClearingInfo: '/api/System/QueryClearingInfo',
  QueryClearCompanyOfSupplier: '/api/System/QueryClearCompanyOfSupplier',
  QuerySourceSiteOfSupplier: '/api/System/QuerySourceSiteOfSupplier',
  InsertClearingInfo: '/api/System/InsertClearingInfo',
  QueryClearingList: '/api/System/QueryClearingList',
  DeleClearing: '/api/System/DeleClearing',
  RecoverClearing: '/api/System/RecoverClearing',
  DeleteClearing: '/api/System/DeleteClearing',
  ClearingFind: '/api/System/ClearingFind',
  ModifyClearingInfo: '/api/System/ModifyClearingInfo',

  QueryProductInfo: '/api/System/QueryProductInfo',
  QueryProductList: '/api/System/QueryProductList',
  InsertProductInfo: '/api/System/InsertProductInfo',
  ProductFind: '/api/System/ProductFind',
  DeleProduct: '/api/System/DeleProduct',
  RecoverProduct: '/api/System/RecoverProduct',
  DeleteProduct: '/api/System/DeleteProduct',
  ModifyProductInfo: '/api/System/ModifyProductInfo',

  QueryEmailEJVList: '/api/System/QueryEmailEJVList',
  InsertEmailEJVInfo: '/api/System/InsertEmailEJVInfo',
  DeleteEmailEJV: '/api/System/DeleteEmailEJV',
  EmailEJVFind: '/api/System/EmailEJVFind',
  ModifyEmailEJVInfo: '/api/System/ModifyEmailEJVInfo',
  QueryAreaInfo: '/api/System/QueryAreaInfo',
  QueryEmailOSList: '/api/System/QueryEmailOSList',
  InsertEmailOSInfo: '/api/System/InsertEmailOSInfo',
  DeleteEmailOS: '/api/System/DeleteEmailOS',

  EmailOSFind: '/api/System/EmailOSFind',
  ModifyEmailOSInfo: '/api/System/ModifyEmailOSInfo',
  QueryFleetEmailSelfList: '/api/System/QueryFleetEmailSelfList',
  InsertFleetEmailSelfInfo: '/api/System/InsertFleetEmailSelfInfo',
  DeleteFleetEmailSelf: '/api/System/DeleteFleetEmailSelf',
  ModifyEmailSelfInfo: '/api/System/ModifyEmailSelfInfo',
  EmailSelfFind: '/api/System/EmailSelfFind',
  QueryFleetEmailDeliveryList: '/api/System/QueryFleetEmailDeliveryList',
  InsertFleetEmailDeliveryInfo: '/api/System/InsertFleetEmailDeliveryInfo',
  DeleteFleetEmailDelivery: '/api/System/DeleteFleetEmailDelivery',
  CitySubNodeJoin: '/api/System/CitySubNodeJoin',
  ModifyEmailDeliveryInfo: '/api/System/ModifyEmailDeliveryInfo',
  EmailDeliveryFind: '/api/System/EmailDeliveryFind',

  QueryNoGoldList: '/api/System/QueryNoGoldList',
  SaveNoGoldInfo: '/api/System/SaveNoGoldInfo',
  InsertGoldCustomerInfo: '/api/System/InsertGoldCustomerInfo',

  InsertSourceManageInfo: '/api/System/InsertSourceManageInfo',
  QuerySourceManageList: '/api/System/QuerySourceManageList',
  ModifySourceManageInfo: '/api/System/ModifySourceManageInfo',
  DeleteSourceManage: '/api/System/DeleteSourceManage',

  InsertSettlementInfo: '/api/System/InsertSettlementInfo',
  QuerySettlementList: '/api/System/QuerySettlementList',
  DeleteSettlement: '/api/System/DeleteSettlement',
  ModifySettlementInfo: '/api/System/ModifySettlementInfo',

  QueryParamList: '/api/System/QueryParamList',
  ModifyParamInfo: '/api/System/ModifyParamInfo',
  InsertParamAddInfo: '/api/System/InsertParamAddInfo',

  QueryRoleMapAuthTrees: '/api/System/QueryRoleMapAuthTrees',
  RoleBingAuth: '/api/System/RoleBingAuth',
  UserRoleQuery: '/api/System/UserRoleQuery',
  UpdateUserAccess: '/api/System/UpdateUserAccess',
  QueryLogPageInfo: '/api/System/QueryLogPageInfo',
  CreateToken: '/api/System/CreateToken',

  //供应商首页报表
  GetCurrentSupplierHomeSummary: '/api/Billing/GetCurrentSupplierHomeSummary',

  //系统设置车队导入邮箱
  ImportUserEmailZt: '/api/Billing/ImportUserEmailZt',
  ImportUserEmailSh: '/api/Billing/ImportUserEmailSh',
};
