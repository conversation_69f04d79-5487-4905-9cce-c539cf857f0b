import commonTheme from './common';

// 主题样式，会被注入到顶层样式provider中，可通过styleComponents读取，同时会被.umirc.ts读取应用到antd中
export const theme = {
  '@primary-color': '#035393',
  '@link-color': '#035393',
  // '@border-color-base': '#035393',
  '@border-radius-base': '5px',
  '@btn-default-border': '#035393',
  '@btn-border-radius-base': '5px',
  '@card-radius': '10px',
  // '@label-color': '#cccccc',
};

export type Theme = typeof theme;
