import { useGet, usePost } from '@/app/request';
import Config from '@/app/config';

//查询当前登录用户信息
export const queryLoginUser = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryLoginUser}`, {
    autoLoading: true,
  });
};
//导入对账单
export const importBilling = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ImportBilling}`, formData, {
    autoLoading: true,
  });
};
//查询对账单
export const queryBillingDetailById = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryBillingDetailById}?Id=${id}`, {
    autoLoading: true,
  });
};
//查询被退回账单
export const queryBillingReturnPageInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryBillingReturnPageInfo}`, formData, {
    autoLoading: true,
  });
};
//查询草稿对账单
export const queryBillingDraftPageInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryBillingDraftPageInfo}`, formData, {
    autoLoading: true,
  });
};
//新增对账单
export const insertBillingEntry = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertBillingEntry}`, formData, {
    autoLoading: true,
  });
};
//删除对账单
export const delBillingEntry = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.DelBillingEntry}?Id=${id}`, {
    autoLoading: true,
  });
};
//暂存对账单
export const saveBillingDraft = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.SaveBillingDraft}`, formData, {
    autoLoading: true,
  });
};
//提交对账单
export const submitBillingInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.SubmitBillingInfo}`, formData, {
    autoLoading: true,
  });
};
//确定账单是否有效
export const checkSubmitValid = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.checkSubmitValid}`, formData, {
    autoLoading: true,
  });
};

//供应商首页报表
export const getCurrentSupplierHomeSummary = (date: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.GetCurrentSupplierHomeSummary}?date=${date}`,
    {},
    {
      autoLoading: true,
    },
  );
};
