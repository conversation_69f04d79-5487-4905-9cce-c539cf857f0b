import { useDelete, useGet, usePost } from '@/app/request';
import Config from '@/app/config';

export const insertUserInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertUserInfo}`, formData, {
    autoLoading: true,
  });
};
export const modifyUserInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyUserInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryUserFindInfo = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryUserFindInfo}?Id=${id}`, {
    autoLoading: true,
  });
};
export const resetPass = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ResetPass}`, formData, {
    autoLoading: true,
  });
};
export const deleUser = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.DeleUser}`, formData, {
    autoLoading: true,
  });
};
export const recoverUser = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.RecoverUser}`, formData, {
    autoLoading: true,
  });
};
export const queryUserInfo = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryUserInfo}`, {
    autoLoading: true,
  });
};
export const deleteUser = (id: string) => {
  return useDelete(`${Config.Api.Base}${Config.Api.DeleteUser}?Id=${id}`, {
    autoLoading: true,
  });
};
export const queryUserPageInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryUserPageInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryRolePageInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryRolePageInfo}`, formData, {
    autoLoading: true,
  });
};
export const insertRole = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertRole}`, formData, {
    autoLoading: true,
  });
};
export const modifyRole = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyRole}`, formData, {
    autoLoading: true,
  });
};
export const deleteRole = (id: string) => {
  return useDelete(`${Config.Api.Base}${Config.Api.DeleteRole}?Id=${id}`, {
    autoLoading: true,
  });
};
export const queryRoleFind = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryRoleFind}?Id=${id}`, {
    autoLoading: true,
  });
};
export const authTreeJoin = () => {
  return useGet(`${Config.Api.Base}${Config.Api.AuthTreeJoin}`, {
    autoLoading: true,
  });
};
export const roleBingUser = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.RoleBingUser}`, formData, {
    autoLoading: true,
  });
};
export const queryRoleMapUsers = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryRoleMapUsers}`, {
    autoLoading: true,
  });
};
export const queryRoleInfo = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryRoleInfo}`, {
    autoLoading: true,
  });
};
export const insertQuerySupplier = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertQuerySupplier}`, formData, {
    autoLoading: true,
  });
};
export const queryCustomerOfCurrentUser = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryCustomerOfCurrentUser}?key=${id}`, {
    autoLoading: false,
  });
};
export const querySupplierNameInfo = (keyword?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QuerySupplierNameInfo}${keyword ? `?KeyWord=${keyword}` : ''} `, {
    autoLoading: false,
  });
};
export const queryEJVSupplierNameInfo = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryEJVSupplierNameInfo}`, {
    autoLoading: true,
  });
};
export const querySupplierTypeInfo = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QuerySupplierTypeInfo}`, {
    autoLoading: true,
  });
};
export const querySupplierList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QuerySupplierList}`, formData, {
    autoLoading: true,
  });
};
export const deleSupplier = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.DeleSupplier}?Id=${id}`, {
    autoLoading: true,
  });
};
export const recoverSupplier = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.RecoverSupplier}?Id=${id}`, {
    autoLoading: true,
  });
};
export const deleteSupplier = (id: string) => {
  return useDelete(`${Config.Api.Base}${Config.Api.DeleteSupplier}?Id=${id}`, {
    autoLoading: true,
  });
};
export const querySupplierFind = (id: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.QuerySupplierFind}?SupplierId=${id}`,
    {},
    {
      autoLoading: true,
    },
  );
};
export const modifySupplierInfo = (formData?: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifySupplierInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryClearingInfo = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryClearingInfo}?keyword=${id}`, {
    autoLoading: true,
  });
};
export const queryClearCompanyOfSupplier = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryClearCompanyOfSupplier}?keyword=${id}`, {
    autoLoading: true,
  });
};
export const querySourceSiteOfSupplier = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QuerySourceSiteOfSupplier}?keyword=${id}`, {
    autoLoading: true,
  });
};
export const insertClearingInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertClearingInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryClearingList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryClearingList}`, formData, {
    autoLoading: true,
  });
};

export const deleClearing = (id?: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.DeleClearing}?Id=${id}`,
    {},
    {
      autoLoading: true,
    },
  );
};
export const recoverClearing = (id?: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.RecoverClearing}?Id=${id}`,
    {},
    {
      autoLoading: true,
    },
  );
};
export const deleteClearing = (id: string) => {
  return useDelete(`${Config.Api.Base}${Config.Api.DeleteClearing}?Id=${id}`, {
    autoLoading: true,
  });
};
export const clearingFind = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.ClearingFind}?Id=${id}`, {
    autoLoading: true,
  });
};
export const modifyClearingInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyClearingInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryProductInfo = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryProductInfo}?keyword=${id}`, {
    autoLoading: true,
  });
};
export const queryProductList = (formData?: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryProductList}`, formData, {
    autoLoading: true,
  });
};
export const insertProductInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertProductInfo}`, formData, {
    autoLoading: true,
  });
};
export const productFind = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.ProductFind}?Id=${id}`, {
    autoLoading: true,
  });
};
export const deleProduct = (id: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.DeleProduct}?Id=${id}`,
    {},
    {
      autoLoading: true,
    },
  );
};
export const recoverProduct = (id: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.RecoverProduct}?Id=${id}`,
    {},
    {
      autoLoading: true,
    },
  );
};
export const deleteProduct = (id: string) => {
  return useDelete(`${Config.Api.Base}${Config.Api.DeleteProduct}?Id=${id}`, {
    autoLoading: true,
  });
};
export const modifyProductInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyProductInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryEmailEJVList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryEmailEJVList}`, formData, {
    autoLoading: true,
  });
};
export const insertEmailEJVInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertEmailEJVInfo}`, formData, {
    autoLoading: true,
  });
};
export const deleteEmailEJV = (id: string) => {
  return useDelete(`${Config.Api.Base}${Config.Api.DeleteEmailEJV}?Id=${id}`, {
    autoLoading: true,
  });
};
export const emailEJVFind = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.EmailEJVFind}?Id=${id}`, {
    autoLoading: true,
  });
};
export const modifyEmailEJVInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyEmailEJVInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryAreaInfo = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryAreaInfo}`, {
    autoLoading: true,
  });
};
export const queryEmailOSList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryEmailOSList}`, formData, {
    autoLoading: true,
  });
};
export const insertEmailOSInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertEmailOSInfo}`, formData, {
    autoLoading: true,
  });
};
export const deleteEmailOS = (id: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.DeleteEmailOS}?Id=${id}`,
    {},
    {
      autoLoading: true,
    },
  );
};
export const emailOSFind = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.EmailOSFind}?Id=${id}`, {
    autoLoading: true,
  });
};
export const modifyEmailOSInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyEmailOSInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryFleetEmailSelfList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryFleetEmailSelfList}`, formData, {
    autoLoading: true,
  });
};
export const insertFleetEmailSelfInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertFleetEmailSelfInfo}`, formData, {
    autoLoading: true,
  });
};
export const deleteFleetEmailSelf = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.DeleteFleetEmailSelf}?Id=${id}`, {
    autoLoading: true,
  });
};
export const modifyEmailSelfInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyEmailSelfInfo}`, formData, {
    autoLoading: true,
  });
};
export const emailSelfFind = (id: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.EmailSelfFind}?Id=${id}`,
    {},
    {
      autoLoading: true,
    },
  );
};
//导入车队
export const importUserEmailZt = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ImportUserEmailZt}`, formData, {
    autoLoading: true,
  });
};
export const queryFleetEmailDeliveryList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryFleetEmailDeliveryList}`, formData, {
    autoLoading: true,
  });
};
export const insertFleetEmailDeliveryInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertFleetEmailDeliveryInfo}`, formData, {
    autoLoading: true,
  });
};
export const deleteFleetEmailDelivery = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.DeleteFleetEmailDelivery}?Id=${id}`, {
    autoLoading: true,
  });
};
export const citySubNodeJoin = (formData?: any) => {
  return usePost(`${Config.Api.Base}${Config.Api.CitySubNodeJoin}`, formData, {
    autoLoading: true,
  });
};
export const modifyEmailDeliveryInfo = (formData?: any) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyEmailDeliveryInfo}`, formData, {
    autoLoading: true,
  });
};
export const emailDeliveryFind = (id: string) => {
  return usePost(
    `${Config.Api.Base}${Config.Api.EmailDeliveryFind}?Id=${id}`,
    {},
    {
      autoLoading: true,
    },
  );
};
//导入车队送货
export const importUserEmailSh = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ImportUserEmailSh}`, formData, {
    autoLoading: true,
  });
};
export const queryNoGoldList = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryNoGoldList}`, {
    autoLoading: true,
  });
};
export const saveNoGoldInfo = (formData: any[]) => {
  return usePost(`${Config.Api.Base}${Config.Api.SaveNoGoldInfo}`, formData, {
    autoLoading: true,
  });
};
export const insertGoldCustomerInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertGoldCustomerInfo}`, formData, {
    autoLoading: true,
  });
};
export const insertSourceManageInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertSourceManageInfo}`, formData, {
    autoLoading: true,
  });
};
export const querySourceManageList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QuerySourceManageList}`, formData, {
    autoLoading: true,
  });
};
export const modifySourceManageInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifySourceManageInfo}`, formData, {
    autoLoading: true,
  });
};
export const deleteSourceManage = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.DeleteSourceManage}?Id=${id}`, {
    autoLoading: true,
  });
};

export const insertSettlementInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertSettlementInfo}`, formData, {
    autoLoading: true,
  });
};
export const querySettlementList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QuerySettlementList}`, formData, {
    autoLoading: true,
  });
};
export const deleteSettlement = (id: string) => {
  return useDelete(`${Config.Api.Base}${Config.Api.DeleteSettlement}?Id=${id}`, {
    autoLoading: true,
  });
};
export const modifySettlementInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifySettlementInfo}`, formData, {
    autoLoading: true,
  });
};
export const queryParamList = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryParamList}`, {
    autoLoading: true,
  });
};
export const modifyParamInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ModifyParamInfo}`, formData, {
    autoLoading: true,
  });
};
export const insertParamAddInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.InsertParamAddInfo}`, formData, {
    autoLoading: true,
  });
};

export const queryRoleMapAuthTrees = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryRoleMapAuthTrees}`, {
    autoLoading: true,
  });
};
export const roleBingAuth = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.RoleBingAuth}`, formData, {
    autoLoading: true,
  });
};
export const userRoleQuery = () => {
  return useGet(`${Config.Api.Base}${Config.Api.UserRoleQuery}`, {
    autoLoading: true,
  });
};
export const updateUserAccess = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.UpdateUserAccess}`, formData, {
    autoLoading: true,
  });
};
export const queryLogPageInfo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryLogPageInfo}`, formData, {
    autoLoading: true,
  });
};
export const createToken = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.CreateToken}`, formData, {
    autoLoading: true,
  });
};
export const exportSupplierList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportSupplierList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportUserList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportUserList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportProductList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportProductList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportClearingList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportClearingList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportEmailEJVList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportEmailEJVList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportEmailOSList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportEmailOSList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportFleetEmailSelList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportFleetEmailSelList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportFleetEmailDeliveryList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportFleetEmailDeliveryList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportSourceManageList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportSourceManageList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const exportSettlementList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportSettlementList}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const querySalesVenderList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.querySalesVenderList}`, formData, {
    autoLoading: true,
  });
};
export const delSalesVender = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.delSalesVender}?Id=${id}`, {
    autoLoading: true,
  });
};
export const submitSalesVender = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.submitSalesVender}`, formData, {
    autoLoading: true,
  });
};
export const querySalesSapBuCodeList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.querySalesSapBuCodeList}`, formData, {
    autoLoading: true,
  });
};
export const delSalesSapBuCode = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.delSalesSapBuCode}?Id=${id}`, {
    autoLoading: true,
  });
};
export const submitSalesSapBuCode = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.submitSalesSapBuCode}`, formData, {
    autoLoading: true,
  });
};
