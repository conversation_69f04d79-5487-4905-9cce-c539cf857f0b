import { useGet, usePost, useDelete } from '@/app/request';
import Config from '@/app/config';

//  获取用户权限
export const queryLoginUser = () => {
  return useGet(`${Config.Api.Base}${Config.Api.QueryLoginUser}`, {
    autoLoading: true,
  });
};
// ////////////////////////////////////
//被退回的条目查询
export const QueryReturnEntryPageInfo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryReturnEntryPageInfo}`, data, {
    autoLoading: true,
  });
};
//待核对条目查询
export const QueryToCompareEntryPageInfo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryToCompareEntryPageInfo}`, data, {
    autoLoading: true,
  });
};
//已结算条目查询接口
export const QuerySettlEntryPageInfo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QuerySettlEntryPageInfo}`, data, {
    autoLoading: true,
  });
};
//待结算条目查询接口
export const QueryToBeSettlEntryPageInfo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryToBeSettlEntryPageInfo}`, data, {
    autoLoading: true,
  });
};
//查询被退回的对账单
export const QueryBillingReturnPageInfo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryBillingReturnPageInfo}`, data, {
    autoLoading: true,
  });
};
//导出被退回的条目账单
export const exportPtoProductExcel = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportReturnEntryList}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//导出待核对的条目
export const exportToCompareEntryData = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportToCompareEntryData}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//导出草稿
export const exportBillingDraftData = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportBillingDraftData}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//导出待结算的条目
export const exportToBeSettlEntryList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportToBeSettlEntryList}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//导出已结算的条目
export const exportSettlEntryList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportSettlEntryList}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//被退回条目编辑
export const editReturnEntryInfo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.EditReturnEntryInfo}`, data, {
    autoLoading: true,
  });
};
//删除被退回条目
export const delReturnEntryInfo = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.delReturnEntryInfo}?id=${id}`, {
    autoLoading: true,
  });
};
//删除被退回账单接口 删除暂存的草稿
export const delBillingInfoById = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.delBillingInfoById}?id=${id}`, {
    autoLoading: true,
  });
};
// 待核对 待结算 已结算 条目详情查询
export const queryEntryDetail = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.queryEntryDetail}?id=${id}`, {
    autoLoading: true,
  });
};
//查询草稿
export const QueryBillingDraftPageInfo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryBillingDraftPageInfo}`, data, {
    autoLoading: true,
  });
};
//首页列表查询
export const queryInnerHomeList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryInnerHomeList}`, data, {
    autoLoading: true,
  });
};
//首页对账单查询
export const queryInnerHomeEntryList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryInnerHomeEntryList}`, data, {
    autoLoading: true,
  });
};
//退回，数量比对通过，价格比对通过
export const opearteEntryStatus = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.opearteEntryStatus}`, data, {
    autoLoading: true,
  });
};
//MTOP待确认 待结算 已结算
export const queryInnerHomeFinList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryInnerHomeFinList}`, data, {
    autoLoading: true,
  });
};
//根据id查询对账单详情
export const queryInnerHomeBillingById = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.queryInnerHomeBillingById}?id=${id}`, {
    autoLoading: true,
  });
};
//根据id查询结算单详情
export const queryInnerHomeStatementById = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.queryInnerHomeStatementById}?id=${id}`, {
    autoLoading: true,
  });
};
//生成格式PO
export const generateFormatPo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.generateFormatPo}`, data, {
    autoLoading: true,
  });
};
//回写PO号
export const SubmitPoInfo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.SubmitPoInfo}`, data, {
    autoLoading: true,
  });
};
//查询对账单号（内部对账）
export const queryInnerBillingList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryInnerBillingList}`, data, {
    autoLoading: true,
  });
};
//查询结算单（内部对账）
export const queryInnerBillingPoList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryInnerBillingPoList}`, data, {
    autoLoading: true,
  });
};
//获取格式PO预览合集
export const getFormatPoDraftList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.getFormatPoDraftList}`, data, {
    autoLoading: true,
  });
};
//提交生成格式PO
export const SubmitFormatPo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.SubmitFormatPo}`, data, {
    autoLoading: true,
  });
};
//待生成格式PO取消
export const CancelFormatPo = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.CancelFormatPo}`, data, {
    autoLoading: true,
  });
};
//提交数量结算方式
export const submitUseSettlementQuantity = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.submitUseSettlementQuantity}`, data, {
    autoLoading: true,
  });
};

//获取供应商名称
export const querySupplierNameInfo = (keyword?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.querySupplierNameInfo}?KeyWord=${keyword}`, {
    autoLoading: true,
  });
};
//获取供应商类型
export const querySupplierTypeInfo = () => {
  return useGet(`${Config.Api.Base}${Config.Api.querySupplierTypeInfo}`, {
    autoLoading: true,
  });
};
//获取结算公司名称
export const queryClearingInfo = () => {
  return useGet(`${Config.Api.Base}${Config.Api.queryClearingInfo}`, {
    autoLoading: true,
  });
};
//获取产品信息名称
export const queryProductInfo = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.queryProductInfo}?keyword=${id}`, {
    autoLoading: true,
  });
};
//获取操作集合
export const getOperateList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.getOperateList}`, data, {
    autoLoading: true,
  });
};
//批量回传PO号
export const importPoList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.importPoList}`, data, {
    autoLoading: true,
  });
};
//下载
export const downLoadToBeSettl = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.downLoadToBeSettl}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//导出首页对账单详情接口
export const exportInnerHomeEntryList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportInnerHomeEntryList}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};

//导出查看结算详情
export const exportInnerHomeFinList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportInnerHomeFinList}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//特殊条目
export const queryInnerHomeSpecialEntryList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryInnerHomeSpecialEntryList}`, data, {
    autoLoading: true,
  });
};
//订单号查询
export const queryOrderNumberList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryOrderNumberList}`, data, {
    autoLoading: true,
  });
};
//订单号导出
export const exportOrderNumberData = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportOrderNumberData}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//查询订单号详情
export const queryOrderDetail = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.queryOrderDetail}?id=${id}`, {
    autoLoading: true,
  });
};
//账单附件管理
export const queryAttachmentPageList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryAttachmentPageList}`, data, {
    autoLoading: true,
  });
};
//新增附件记录
export const insertAttachmentDetail = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.insertAttachmentDetail}`, data, {
    autoLoading: true,
  });
};
//删除
export const deleteAttachmentDetail = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.deleteAttachmentDetail}?id=${id}`, {
    autoLoading: true,
  });
};
//下载附件
export const downloadAttachment = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.downloadAttachment}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//上传获取参数
export const uploadAttachment = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.uploadAttachment}`, data, {
    autoLoading: true,
  });
};
//开票信息
export const queryClearningCompanyDetail = (value?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.queryClearningCompanyDetail}?value=${value}`, {
    autoLoading: true,
  });
};
//计提报表查询
export const queryProvisionReportFormPage = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryProvisionReportFormPage}`, data, {
    autoLoading: true,
  });
};
//查询产品组
export const queryProductGroup = (value?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.queryProductGroup}?value=${value}`, {
    autoLoading: true,
  });
};
//更新报表
export const updateProvisionData = (data: any[]) => {
  return usePost(`${Config.Api.Base}${Config.Api.updateProvisionData}`, data, {
    autoLoading: true,
  });
};

//删除报表
export const deleteProvisionData = (id?: string) => {
  return useDelete(`${Config.Api.Base}${Config.Api.deleteProvisionData}?id=${id}`, {
    autoLoading: true,
  });
};
//报表导出
export const exportProvisionData = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportProvisionData}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
export const submitSettlPoInfo = (data: any[]) => {
  return usePost(`${Config.Api.Base}${Config.Api.submitSettlPoInfo}`, data, {
    autoLoading: true,
  });
};
export const queryClearCompanyBySupplier = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.queryClearCompanyBySupplier}`, data, {
    autoLoading: true,
  });
};
//导入
export const ImportPoReport = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ImportPoReport}`, data, {
    autoLoading: true,
  });
};
//导出对账单详情
export const exportBillingDetailListById = (id: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.exportBillingDetailListById}?id=${id}`, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//导出结算单详情
export const exportFinBillingDetailListByNumber = (id?: string) => {
  return useGet(`${Config.Api.Base}${Config.Api.exportFinBillingDetailListById}?id=${id}`, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//计提报告二次确认
export const dataVerification = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.dataVerification}`, data, {
    autoLoading: true,
  });
};
//批量删除
export const deleteReturnEntryList = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.deleteReturnEntryList}`, data, {
    autoLoading: true,
  });
};
export const delBillingInfoByIdList = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.delBillingInfoByIdList}`, data, {
    autoLoading: true,
  });
};
//内部对账单
export const exportInnerBillingList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportInnerBillingList}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//内部结算单导出
export const exportInnerBillingPoList = (data: Object) => {
  return usePost(`${Config.Api.Base}${Config.Api.exportInnerBillingPoList}`, data, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//邮件至车队
export const EmailToFleet = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.EmailToFleet}`, formData, {
    autoLoading: true,
  });
};
//邮件至采购
export const EmailToPurchase = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.EmailToPurchase}`, formData, {
    autoLoading: true,
  });
};
//整体数据质量分析表格统计
export const QueryAllDataQualityAlyList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryAllDataQualityAlyList}`, formData, {
    autoLoading: true,
  });
};
//PM采购数量表格统计数据
export const QueryPurchaseAnalysisList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryPurchaseAnalysisList}`, formData, {
    autoLoading: true,
  });
};
//供应商数据分析
export const QuerySupplierDataAnalysisList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QuerySupplierDataAnalysisList}`, formData, {
    autoLoading: true,
  });
};
//车队质量分析
export const QueryFleetAnalysisList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryFleetAnalysisList}`, formData, {
    autoLoading: true,
  });
};
//整体状态  QueryAllDataStatusAlyList
export const QueryAllDataStatusAlyList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryAllDataStatusAlyList}`, formData, {
    autoLoading: true,
  });
};
// 车队图表统计
export const QueryFleetChartsList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryFleetChartsList}`, formData, {
    autoLoading: true,
  });
};
//整体数据状态分析柱状图
export const QueryAllDataStatusAlyChartsList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryAllDataStatusAlyChartsList}`, formData, {
    autoLoading: true,
  });
};
//PM/采购数量柱状图统计数据
export const QueryPurchaseChartsList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryPurchaseChartsList}`, formData, {
    autoLoading: true,
  });
};
//供应商柱状统计数据
export const QuerySupplierDataChartsList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QuerySupplierDataChartsList}`, formData, {
    autoLoading: true,
  });
};
// 车队负责人
export const QueryFleetLeaderList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryFleetLeaderList}`, formData, {
    autoLoading: true,
  });
};
// pm采购负责人
export const QueryPurchaseLeaderList = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryPurchaseLeaderList}`, formData, {
    autoLoading: true,
  });
};
//导出整体数据
export const ExportAllDataQualityData = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportAllDataQualityData}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//导出车队数据
export const ExportFleetAnalysisData = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportFleetAnalysisData}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//导出PM采购
export const ExportPurchaseAnalysisData = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportPurchaseAnalysisData}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//数量比对失败明细
export const QueryQuantityCompareFailedDetail = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryQuantityCompareFailedDetail}`, formData, {
    autoLoading: true,
  });
};
//价格比对失败明细
export const QueryPriceCompareFailedDetail = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryPriceCompareFailedDetail}`, formData, {
    autoLoading: true,
  });
};
//数量退回明细
export const BillingReturnDetailDo = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.BillingReturnDetailDo}`, formData, {
    autoLoading: true,
  });
};
//导出供应商
export const ExportSupplierAnalysisData = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportSupplierAnalysisData}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
// 导出数据状态
export const ExportAllDataStatusData = (formData: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.ExportAllDataStatusData}`, formData, {
    autoLoading: true,
    responseType: 'blob',
  });
};
//合并po生成
export const MergeFormatPo = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.MergeFormatPo}`, data, {
    autoLoading: true,
  });
};
//获取合并PO预览合集
export const GetFormatMergePoPreview = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.GetFormatMergePoPreview}`, data, {
    autoLoading: true,
  });
};
// PO生成取消
export const CancelFormatMergePo = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.CancelFormatMergePo}`, data, {
    autoLoading: true,
  });
};
//提交生成格式po
export const SubmitFormatMergePo = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.SubmitFormatMergePo}`, data, {
    autoLoading: true,
  });
};
//更新DN
export const SubmitDocumentNumber = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.SubmitDocumentNumber}`, data, {
    autoLoading: true,
  });
};
// 额度管理分页
export const QueryVendorUpAmountList = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.QueryVendorUpAmountList}`, data, {
    autoLoading: true,
  });
};
// 删除额度管理
export const DelVendorUpAmount = (id: string) => {
  return usePost(`${Config.Api.Base}${Config.Api.DelVendorUpAmount}?Id=${id}`, {
    autoLoading: true,
  });
};
// 提交额度管理
export const SubmitVendorUpAmount = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.SubmitVendorUpAmount}`, data, {
    autoLoading: true,
  });
};
//
export const UpdatePgrPostingDate = (data: object) => {
  return usePost(`${Config.Api.Base}${Config.Api.UpdatePgrPostingDate}`, data, {
    autoLoading: true,
  });
};
