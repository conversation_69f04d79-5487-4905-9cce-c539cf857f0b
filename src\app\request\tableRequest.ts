import axios from 'axios';

export interface PropTableResponse<T> {
  total?: number;
  success: boolean;
  data: T | [];
}

interface IProTablePromiseArgs {
  url: string;
  params?: any;
  headers?: any;
}
interface IProTablePromiseAction {
  successCallback?: Function;
  failureCallback?: Function;
}

export async function useProTablePromiseByPost<T>(
  args: IProTablePromiseArgs,
  aciton: IProTablePromiseAction,
): Promise<PropTableResponse<T>> {
  let request = await axios.post(args.url, args.params, args.headers);
  if (request && request.data && request.data.success) {
    aciton.successCallback && aciton.successCallback(request);
  } else {
    aciton.failureCallback && aciton.failureCallback(request);
  }

  return {
    success: request?.data?.success ?? true,
    data: request?.data?.data ?? [],
    total: request?.data?.totalCount ?? 0,
  };
}

export async function useProTablePromiseByGet<T>(
  args: IProTablePromiseArgs,
  aciton: IProTablePromiseAction,
): Promise<PropTableResponse<T>> {
  let request = await axios.get(args.url, args.headers);
  if (request && request.data && request.data.success) {
    aciton.successCallback && aciton.successCallback(request);
  } else {
    aciton.failureCallback && aciton.failureCallback(request);
  }

  return {
    success: request?.data?.success ?? true,
    data: request?.data?.data ?? [],
    total: request?.data?.totalCount ?? 0,
  };
}
