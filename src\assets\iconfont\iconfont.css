@font-face {
  font-family: "iconfont"; /* Project id 2985545 */
  src: url('./iconfont.woff2?t=1638517889543') format('woff2'),
       url('./iconfont.woff?t=1638517889543') format('woff'),
       url('./iconfont.ttf?t=1638517889543') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-setting:before {
  content: "\e600";
}

.icon-custom:before {
  content: "\e658";
}

