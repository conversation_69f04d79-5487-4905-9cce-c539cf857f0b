import styled from 'styled-components';

export const ContainerDiv = styled.div`
  background: url(${() => require(`@/assets/images/content_bg.png`)}) no-repeat;
  background-size: 100%;
  background-color: #ececec;
  padding: 20px;
  height: 100%;
`;
export const WrapperDiv = styled.div`
  background-color: #fff;
  border-radius: 10px;
`;
export const SearchDiv = styled.div`
  padding: 30px 0px 0px;
  border-radius: 10px;
  background-color: #fff;
  border-bottom: 1px solid #eaf4f6;
`;
export const TableWrapDiv = styled.div`
  margin-top: 20px;
  border-radius: 10px;
  background: #fff;
  .stripe,
  .stripe .ant-table-cell-fix-right,
  .stripe .ant-table-cell-fix-left {
    background-color: #f9f9f9;
  }

  .ant-table {
    color: #666;
  }
  .ant-table-pagination {
    justify-content: center;
  }
  .ant-table-tbody > tr > td {
    border: none;
  }
`;
export const TableTopDiv = styled.div`
  overflow: hidden;
  margin-bottom: 10px;
`;
export const TableTitleDiv = styled.div`
  color: #005293;
  font-size: 18px;
  font-weight: 700;
  padding-top: 5px;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-between;
  /* justify-content: sp; */
  /* margin-bottom: 15px; */
`;
export const TableTitleSpanDiv = styled.div`
  height: 8px;
  background: #9ed0dd;
  margin-top: -9px;
`;
export const TaleTitleIconDiv = styled.div`
  margin-right: 10px;
  display: inline-block;
  span {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 3px;
    vertical-align: middle;
    background-color: #005293;
  }
  /* span:nth-child(1) {
    background-color: #fbe0cc;
  }
  span:nth-child(2) {
    background-color: #f4a367;
  }
  span:nth-child(3) {
    background-color: #ec6602;
  } */
`;
export const TableBtnDiv = styled.div`
  float: right;
`;
export const BtnGreenWrap = styled.span`
  float: right;
  .ant-btn-link,
  .ant-btn-link:hover,
  .ant-btn-link:focus {
    color: #389e0d !important;
  }
`;
export const BtnOrgWrap = styled.span`
  .ant-btn-link,
  .ant-btn-link:hover,
  .ant-btn-link:focus {
    color: #ec6602 !important;
  }
`;
export const BtnBlaWrap = styled.span`
  .ant-btn-link,
  .ant-btn-link:hover,
  .ant-btn-link:focus {
    color: #333 !important;
  }
`;
export const BtnBlueWrap = styled.span`
  .ant-btn {
    background: rgb(64, 163, 189);
    color: #fff;
    border-color: rgb(64, 163, 189);
  }
  .ant-btn:hover,
  .ant-btn:focus {
    border-color: rgb(64, 163, 189);
  }
`;
export const OperDiv = styled.div`
  text-align: center;
  .ant-btn-link {
    color: rgb(64, 163, 189);
  }
  .ant-btn-link span {
    text-decoration: underline;
  }
  .ant-btn-dangerous.ant-btn-link {
    color: #ff4d4f;
  }
`;
export const ButtonFooter = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 10px;
`;
