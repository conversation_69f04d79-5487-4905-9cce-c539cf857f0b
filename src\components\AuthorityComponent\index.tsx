import { ControlOutlined } from '@ant-design/icons';
import React, { useEffect, useState, useCallback, useMemo } from 'react';

const AuthorityComponent = (props) => {
  const [authCodes, setAuthCodes] = useState<any>();
  useEffect(() => {
    let _authBtns = JSON.parse(sessionStorage.getItem('authCodes'));
    setAuthCodes(_authBtns);
  }, []);

  return authCodes?.includes(props.type) ? props.children : '';
};

export default AuthorityComponent;
