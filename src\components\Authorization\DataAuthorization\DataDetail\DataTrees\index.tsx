import styled from 'styled-components';
import React, { useContext } from 'react';
import { <PERSON>ton, Form, Spin } from 'antd';
import ProForm from '@ant-design/pro-form';

import { SpinWrapper } from '@/components/Authorization/style';
import AuthTree from '@/components/Authorization/components/AuthTree';
import useMultiAuthTreeService, {
  MultiAuthTreeService,
} from '@/components/Authorization/components/AuthTree/useMultiAuthTreeService';
import { AuthorizationService } from '@/components/Authorization/useAuthorizationService';
import useDataTreesService from '@/components/Authorization/DataAuthorization/DataDetail/DataTrees/useDataTreesService';
import { RolesTreeService } from '@/components/Authorization/DataAuthorization/DataDetail/RolesTree/useRolesTreeService';

export type IFromPorps = {
  dataSource: any;
};

const DataTreeForm = styled(ProForm)`
  height: 100%;
  .ant-form-item {
    padding: 10px;
    width: 100%;
    margin: 0;
    display: flex;
    flex-direction: row;
    .ant-form-item-control {
      overflow-y: auto;
      width: 100%;
      height: calc(100% - 30px);
      display: block;
    }
  }
`;
const BussinessTreeFormItem = styled.div`
  display: flex;
  height: calc(100% - 32px);
`;

const DataTrees = (props: IFromPorps) => {
  const multiAuthTreeService = useMultiAuthTreeService();
  const { treesData } = multiAuthTreeService;
  const { formatMessage } = useContext(AuthorizationService);
  const { loading: TreeLoading } = useContext(RolesTreeService);
  const { loading, form, onCreate, onUpdate, onRemove, renderBtns } =
    useDataTreesService({
      ...props,
      multiAuthTreeService: multiAuthTreeService,
    });
  return (
    <MultiAuthTreeService.Provider value={multiAuthTreeService}>
      <SpinWrapper>
        <Spin spinning={loading || TreeLoading}>
          <DataTreeForm
            form={form}
            submitter={{
              render: (renderProps) => {
                const createBtn = (
                  <Button
                    type="primary"
                    key="create"
                    onClick={() => {
                      onCreate(renderProps.form);
                    }}
                  >
                    {formatMessage('创建')}
                  </Button>
                );
                const updateBtn = (
                  <Button
                    type="primary"
                    key="update"
                    onClick={() => {
                      onUpdate(renderProps.form);
                    }}
                  >
                    {formatMessage('更新')}
                  </Button>
                );
                const removeBtn = (
                  <Button
                    key="remove"
                    onClick={() => {
                      onRemove(renderProps.form);
                    }}
                  >
                    {formatMessage('移除')}
                  </Button>
                );
                return renderBtns([createBtn, updateBtn, removeBtn]);
              },
            }}
          >
            <BussinessTreeFormItem>
              {treesData?.map((tree) => {
                return (
                  <Form.Item
                    name={tree.name}
                    label={tree.label}
                    valuePropName={'checkedKeys'}
                  >
                    <AuthTree treeData={tree.treeData} />
                  </Form.Item>
                );
              })}
            </BussinessTreeFormItem>
          </DataTreeForm>
        </Spin>
      </SpinWrapper>
    </MultiAuthTreeService.Provider>
  );
};
export default DataTrees;
