import { useRequest } from 'ahooks';
import { IFromPorps } from './index';
import { Modal, message } from 'antd';
import { usePost, Response, useDelete, usePut } from '@/app/request';
import React, { useContext, useEffect, useState } from 'react';

import Loading from '@/components/Loading';
import { useForm } from 'antd/es/form/Form';
import useFormService from '@/components/Form/useFormService';
import { IMultiAuthTreeService } from '@/components/Authorization/components/AuthTree/useMultiAuthTreeService';
import { RolesTreeService } from '@/components/Authorization/DataAuthorization/DataDetail/RolesTree/useRolesTreeService';
import { AuthorizationService } from '@/components/Authorization/useAuthorizationService';
import Api from '@/components/Authorization/api';

export interface IBusinessTreeResponse {
  businessTreeName: string;
  businessTreeTructures: IBusinessTree[];
}
export interface IBusinessTree {
  nodeId: string;
  nodeName: string;
  parentNodeId: string;
  isHas: boolean;
  type: 0;
  childNodeList: IBusinessTree[];
}
type IStatus = 'Edit' | 'Blank' | 'Preview';
export default function useDataTreesService(
  props: IFromPorps & { multiAuthTreeService: IMultiAuthTreeService },
) {
  const [form] = useForm();
  const [status, setStatus] = useState<IStatus>();
  const { formatMessage, baseApi } = useContext(AuthorizationService);
  const { getRolesTree, treeKeys, treeData, selectNode } =
    useContext(RolesTreeService);
  const { validateFields } = useFormService();
  const { formatAddKeysList, formatRemoveKeysList } =
    props.multiAuthTreeService;

  /**
   * 请求详情数据
   */
  const {
    data,
    loading,
    run: getDetail,
  } = useRequest<
    Response<IBusinessTreeResponse[]>,
    any,
    Record<string, []>,
    any
  >(
    (status) => {
      return {
        method: 'get',
        url:
          status === 'Blank'
            ? `${baseApi}${Api.DataAuthBlankTree}`
            : `${baseApi}${Api.DataAuthTreeDetail}?roleId=${selectNode.roleId}`,
      };
    },
    {
      manual: true,
      formatResult: (res) => {
        // 格式化数据
        return props.multiAuthTreeService.initTrees(res.data);
      },
      onSuccess: (data) => {},
    },
  );

  useEffect(() => {
    if (!treeKeys) return;
    let status: IStatus = treeData.filter((t) => t.key === treeKeys[0])?.[0]
      .isSetted
      ? 'Edit'
      : 'Blank';
    getDetail(status);
    setStatus(status);
  }, [treeKeys]);

  useEffect(() => {
    form.setFieldsValue({ ...data });
  }, [data]);

  const formatBusinessTreeVo = (keysList) => {
    return Object.keys(keysList).map((k) => {
      return { type: Number(k), businessTreeId: keysList[k] };
    });
  };
  /**
   * 创建/更新
   */
  const onManage = async (form, status: 'CREATE' | 'UPDATE') => {
    if (await validateFields(form)) {
      Modal.confirm({
        centered: true,
        cancelText: formatMessage('取消'),
        okText: formatMessage('确认'),
        title: formatMessage('提示'),
        content:
          status === 'CREATE'
            ? formatMessage('你确认要创建数据吗')
            : formatMessage('你确认要更新数据吗'),
        onCancel: (close) => {
          close();
        },
        onOk: async () => {
          const value = form.getFieldsValue();
          console.log('创建/更新', value);
          var params;

          if (status === 'CREATE') {
            params = {
              globeRoleId: treeKeys[0],
              accountId: props.dataSource.id,
              addBusinessTreeVo: formatBusinessTreeVo(formatAddKeysList(value)),
            };
          } else {
            params = {
              roleId: selectNode.roleId,
              addBusinessTreeVo: formatBusinessTreeVo(formatAddKeysList(value)),
              removeBusinessTreeVo: formatBusinessTreeVo(
                formatRemoveKeysList(value),
              ),
            };
          }

          console.log(1111111, params);
          debugger;
          Loading.show();

          let request =
            status === 'CREATE'
              ? await usePost(`${baseApi}${Api.DataAuthCreate}`, params)
              : await usePut(`${baseApi}${Api.DataAuthUpdate}`, params);
          Loading.hide();
          console.log('DataManage', request);
          if (request.success) {
            getRolesTree();
            message.success(
              status === 'CREATE'
                ? formatMessage('创建成功')
                : formatMessage('更新成功'),
            );
          } else {
            message.warning(
              status === 'CREATE'
                ? formatMessage('创建失败')
                : formatMessage('更新失败'),
            );
          }
        },
      });
    }
  };

  /**创建人员名下的角色 */
  const onCreate = async (form) => {
    await onManage(form, 'CREATE');
  };

  /**更新人员挂载的角色 */
  const onUpdate = async (form) => {
    await onManage(form, 'UPDATE');
  };

  /** 移除人员挂载的角色 */
  const onRemove = async (form) => {
    Modal.confirm({
      centered: true,
      cancelText: formatMessage('取消'),
      okText: formatMessage('确认'),
      title: formatMessage('提示'),
      content: formatMessage('你确认要移除该角色吗'),
      onCancel: (close) => {
        close();
      },
      onOk: async () => {
        Loading.show();
        let request = await useDelete(`${baseApi}${Api.DataAuthRemove}`, {
          params: { rolesId: selectNode.roleId },
        });
        Loading.hide();
        if (request.success) {
          getRolesTree();
          message.success(formatMessage('移除成功'));
        } else {
          message.warning(formatMessage('移除失败'));
        }
      },
    });
  };
  /**
   * 根据页面状态渲染课程基本信息按钮
   * @param btns
   */
  const renderBtns = (btns: Array<React.ReactNode>) => {
    const [createBtn, updateBtn, removeBtn] = btns;
    switch (status) {
      case 'Blank':
        return [createBtn];
      case 'Edit':
        return [updateBtn, removeBtn];
      default:
        break;
    }
  };

  return { loading, form, renderBtns, onCreate, onUpdate, onRemove };
}
