import { Spin, Tree, Input } from 'antd';
import React, { useContext } from 'react';
import { RolesTreeService } from './useRolesTreeService';
import { SpinWrapper } from '@/components/Authorization/style';
import { RoleTree } from '../style';

const { Search } = Input;

const RolesList = () => {
  const {
    treeData,
    treeKeys,
    loading,
    onSelectTreeKey,
    titleSearch,
    setTitleSearch,
  } = useContext(RolesTreeService);

  return (
    <SpinWrapper>
      <Spin spinning={loading}>
        <Search
          style={{ marginBottom: 8 }}
          allowClear
          placeholder="请输入"
          onSearch={(v) => {
            setTitleSearch(v);
          }}
        />
        <RoleTree
          showIcon
          treeData={
            titleSearch
              ? treeData.filter((data) => data.title.includes(titleSearch))
              : treeData
          }
          defaultExpandAll
          selectedKeys={treeKeys}
          onSelect={onSelectTreeKey}
        ></RoleTree>
      </Spin>
    </SpinWrapper>
  );
};
export default RolesList;
