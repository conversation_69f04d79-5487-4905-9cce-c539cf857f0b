import { Response } from '@/app/request';
import { useMount, useRequest } from 'ahooks';
import { Key, ReactNode, useState, useContext } from 'react';

import { AuthorizationService } from '@/components/Authorization/useAuthorizationService';
import Api from '@/components/Authorization/api';
import React from 'react';

interface IGlobalRolesList {
  globeRoleId: string;
  roleId: string;
  name: string;
}
interface IGlobalRolesTree {
  title: string;
  key: string;
  icon: ReactNode;
  isSetted: boolean;
  roleId: string;
}

export type IFromPorps = {
  dataSource: any;
  icon: ReactNode | ((props) => ReactNode);
};

// 这个服务将被注册至全局
export const RolesTreeService =
  React.createContext<ReturnType<typeof useRolesTreeService>>(undefined);

export default function useRolesTreeService(props: IFromPorps) {
  const [treeKeys, setTreeKeys] = useState<Key[]>();
  const [titleSearch, setTitleSearch] = useState<string>();
  const [selectNode, setSelectNode] = useState<IGlobalRolesTree>();
  const { baseApi } = useContext(AuthorizationService);
  /**
   * 请求通用角色数据
   */
  const {
    data,
    loading,
    run: getRolesTree,
  } = useRequest<
    Response<IGlobalRolesList[]>,
    any,
    IGlobalRolesTree[],
    IGlobalRolesTree[]
  >(
    () => {
      return {
        method: 'get',
        url: `${baseApi}${Api.RolesByAccount}?accountId=${props.dataSource.id}`,
      };
    },
    {
      manual: true,
      formatResult: (res) => {
        return res.data?.sort(sortFunc)?.map((role) => {
          return {
            title: role.name,
            key: role.globeRoleId,
            icon: props.icon?.(role.roleId),
            isSetted: Boolean(role.roleId),
            roleId: role.roleId,
          };
        });
      },
      onSuccess: (data) => {
        data?.length &&
          setSelectNode({
            ...(data?.filter((item) => item.key === treeKeys?.[0])?.[0] ??
              data[0]),
          });
        data?.length && setTreeKeys([...(treeKeys ?? [data[0].key])]);
      },
    },
  );
  useMount(() => {
    getRolesTree();
  });

  const sortFunc = (a, b) => {
    //排序函数
    if (Boolean(a.roleId) > Boolean(b.roleId)) {
      return -1;
    } else {
      return 1;
    }
  };

  const onSelectTreeKey = (
    selectedKeys,
    e: { selected: boolean; selectedNodes; node; event },
  ) => {
    if (e?.selected === false) {
      return;
    }
    setTreeKeys(selectedKeys);
    setSelectNode(e.node);
  };

  return {
    getRolesTree,
    loading,
    treeData: data,
    treeKeys,
    onSelectTreeKey,
    titleSearch,
    setTitleSearch,
    selectNode,
  };
}
