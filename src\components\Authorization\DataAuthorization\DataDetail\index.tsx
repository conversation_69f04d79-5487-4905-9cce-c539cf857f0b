import React, { ReactNode, useContext } from 'react';
import type { ActionType } from '@ant-design/pro-table';
import { ModalFormProps } from '@ant-design/pro-form/lib';
import { SafetyCertificateOutlined } from '@ant-design/icons';
import { DataDetailModal, LeftWrapper, RightWrapper } from './style';

import useRolesTreeService, {
  RolesTreeService,
} from './RolesTree/useRolesTreeService';
import DataTrees from '@/components/Authorization/DataAuthorization/DataDetail/DataTrees';
import RolesTree from '@/components/Authorization/DataAuthorization/DataDetail/RolesTree';
import { AuthorizationService } from '../../useAuthorizationService';

export const NAME = 'codeTree';
export type IFromPorps = ModalFormProps & {
  dataSource: any;
  setVisible: Function;
  visible: boolean;
  status: 'Blank' | 'Edit' | 'Preview';
  tableRef: React.MutableRefObject<ActionType>;
  renderIcon?: ReactNode | ((props) => ReactNode);
};

const DataDetail = (props: IFromPorps) => {
  const rolesTreeService = useRolesTreeService({
    dataSource: props.dataSource,
    icon:
      props?.renderIcon ??
      ((hasRole) => {
        return hasRole ? <SafetyCertificateOutlined /> : null;
      }),
  });

  const { formatMessage } = useContext(AuthorizationService);

  return props.visible ? (
    <RolesTreeService.Provider value={rolesTreeService}>
      <DataDetailModal
        title={`${formatMessage('数据授权')} - ${props.dataSource?.name}`}
        visible={props.visible}
        footer={null}
        maskClosable={false}
        centered={true}
        onCancel={() => {
          props.setVisible((prev) => {
            return { ...prev, visible: false };
          });
        }}
      >
        <LeftWrapper>
          <RolesTree />
        </LeftWrapper>
        <RightWrapper>
          <DataTrees dataSource={props.dataSource} />
        </RightWrapper>
      </DataDetailModal>
    </RolesTreeService.Provider>
  ) : null;
};
export default DataDetail;
