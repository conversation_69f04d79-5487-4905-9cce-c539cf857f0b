import styled from 'styled-components';
import { But<PERSON>, Form, Modal, Tree } from 'antd';

export const DataDetailModal = styled(Modal)`
  width: ${(props) => props.theme?.['modal-width'] ?? '84vw'}!important;
  .ant-modal-body {
    height: ${(props) => props.theme?.['modal-height'] ?? '76vh'};
    background-color: ${(props) =>
      props.theme['system-background'] ?? '#efefef'};
    display: flex;
  }
`;

const Wrapper = styled.div`
  background: #fff;
  padding: 10px;
  border-radius: 6px;
  margin-right: 16px;
  height: 100%;
`;
export const LeftWrapper = styled(Wrapper)`
  overflow-y: auto;
  width: 12%;
`;
export const RightWrapper = styled(Wrapper)`
  width: calc(100% - 12%);
  margin: 0;
`;

export const RoleTree = styled(Tree)`
  .ant-tree-treenode {
    width: 100%;
    white-space: nowrap;

    span.ant-tree-node-content-wrapper {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
`;
