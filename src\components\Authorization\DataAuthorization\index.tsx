import { Button } from 'antd';
import React from 'react';

import MedalsoftTable from '@/components/ProTable';
import { TextAlign, TableWrapper } from '@/components/ProTable/style';
import Detail from '@/components/Authorization/DataAuthorization/DataDetail';
import useDataAuthService from '@/components/Authorization/DataAuthorization/useDataAuthService';
import { AuthorizationService, IBaseProps } from '../useAuthorizationService';

/**
 * 数据授权公共组件，该组件在配置完成基地址及自定义的表单请求函数后，将生成基本的数据授权页面，页面支持查看系统用户列表，可在其中查看/授予通用角色，并为该账号下配置的该角色授予系统数据权限。
 * @author: Phoebe.Lv
 */
export function MedalsoftDataAuth<T, U>(props: IBaseProps<T, U>) {
  const { actionRef, promiseFunction, modal, setModal, onEdit, formatMessage } =
    useDataAuthService(props);

  const nameColums = {
    title: formatMessage('人员'),
    dataIndex: 'name',
    ellipsis: true,
    width: '50%',
  };

  const operateColums = {
    title: <TextAlign>{formatMessage('操作')}</TextAlign>,
    valueType: 'option',
    width: '50%',
    render: (text, record, _, action) => {
      const EditBtn = (
        <Button
          type="link"
          onClick={() => {
            console.log('record');
            onEdit(record);
          }}
          size={'small'}
        >
          {formatMessage('数据授权')}
        </Button>
      );

      return <TextAlign>{EditBtn}</TextAlign>;
    },
  };

  return (
    <AuthorizationService.Provider
      value={{
        formatMessage,
        baseApi: props.baseAPi,
      }}
    >
      <TableWrapper>
        <MedalsoftTable
          actionRef={actionRef}
          columns={[nameColums, props.columns, operateColums].filter((i) => i)}
          request={(params) => promiseFunction(params)}
          {...props.tableProps}
        ></MedalsoftTable>

        {modal?.visible ? (
          <Detail
            dataSource={modal.dataSource}
            visible={modal.visible}
            setVisible={setModal}
            status={modal.status}
            tableRef={actionRef}
          />
        ) : null}
      </TableWrapper>
    </AuthorizationService.Provider>
  );
}
export default MedalsoftDataAuth;
