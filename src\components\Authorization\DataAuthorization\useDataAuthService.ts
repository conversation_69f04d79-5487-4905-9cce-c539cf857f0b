import { message } from 'antd';
import Api from '@/components/Authorization/api';
import { EnumConditionalType, formatConditionService } from '@/app/request';

import useTableServices from '@/components/Table/useTableWithModalService';
import useAuthorizationService, {
  IBaseProps,
} from '../useAuthorizationService';

export interface IList {
  id: any;
  name: string;
  Created: string;
  updatedTime: string;
  updatedUser: string;
  createdUser: string;
}

export default function useFuncAuthService(props: IBaseProps) {
  const { formatCondition } = formatConditionService();
  const { formatMessage } = useAuthorizationService({
    formatMessage: props.formatMessage,
    messagePrefix: props.messagePrefix,
  });

  const {
    dataSource,
    setDataSource,
    actionRef,
    modal,
    setModal,
    onAdd,
    onEdit,
  } = useTableServices({ formatMessage });

  /**
   * 列表数据请求
   */
  const promiseFunction = (params?) => {
    let Conditions = [];
    Conditions.push(
      formatCondition('Name', params.name, EnumConditionalType.Like),
    );

    return props.useProTablePromiseByPost<IList[]>(
      {
        url: `${props.baseAPi}${Api.DataAuthPageList}`,
        params: {
          PageIndex: params.current,
          PageSize: params.pageSize,
          OrderField: 'Created',
          OrderType: 'desc',
          Conditions: Conditions.filter((item) => item),
        },
      },
      {
        successCallback: (res) => {
          console.log('successCallback', res);
          setDataSource(res.data?.data);
        },
      },
    );
  };

  return {
    dataSource,
    actionRef,
    modal,
    setModal,
    promiseFunction,
    onAdd,
    onEdit,
    formatMessage,
  };
}
