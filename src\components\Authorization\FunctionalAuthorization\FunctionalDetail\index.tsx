import { Button, Form } from 'antd';
import React, { useContext } from 'react';
import { ProFormText } from '@ant-design/pro-form';

import { MedalsoftModalFrom } from '@/components/Form/style';

import AuthTree from '@/components/Authorization/components/AuthTree';
import { AuthorizationService } from '@/components/Authorization/useAuthorizationService';
import useFunctionalDetailService, {
  IFromPorps,
} from '@/components/Authorization/FunctionalAuthorization/FunctionalDetail/useFuncDetailService';
import useAuthTreeService, {
  AuthTreeService,
} from '@/components/Authorization/components/AuthTree/useSingleAuthTreeService';

export const NAME = 'codeTree';

const FunctionalDetail = (props: IFromPorps) => {
  const { formatMessage } = useContext(AuthorizationService);

  const authTreeService = useAuthTreeService();
  const { loading, onCreate, onUpdate, form, renderBtn } =
    useFunctionalDetailService({
      ...props,
      authTreeService: authTreeService,
    });

  console.log('FunctionalDetail', props, form.getFieldsValue());

  return props.visible && !loading ? (
    <AuthTreeService.Provider value={authTreeService}>
      <MedalsoftModalFrom
        form={form}
        layout={'horizontal'}
        title={formatMessage('功能授权')}
        visible={props.visible}
        modalProps={{
          maskClosable: false,
          centered: true,
          onCancel: () => {
            props.setVisible((prev) => {
              return { ...prev, visible: false };
            });
          },
        }}
        submitter={{
          render: (renderProps) => {
            const updateBtn = (
              <Button
                type="primary"
                key="Update"
                onClick={() => {
                  onUpdate(renderProps.form);
                }}
              >
                {formatMessage('更新')}
              </Button>
            );
            const createBtn = (
              <Button
                key="Create"
                type="primary"
                onClick={() => {
                  onCreate(renderProps.form);
                }}
              >
                {formatMessage('创建')}
              </Button>
            );
            return renderBtn([createBtn, updateBtn]);
          },
        }}
      >
        <ProFormText
          width="md"
          name="name"
          label={formatMessage('角色名')}
          readonly={props.status === 'Preview'}
          fieldProps={{ maxLength: 200 }}
          rules={[{ required: true, message: formatMessage('请输入') }]}
        />
        <Form.Item
          name={NAME}
          label={formatMessage('功能权限')}
          valuePropName="checkedKeys"
        >
          <AuthTree treeData={authTreeService.treeData} />
        </Form.Item>
      </MedalsoftModalFrom>
    </AuthTreeService.Provider>
  ) : null;
};
export default FunctionalDetail;
