import { useRequest } from 'ahooks';
import { Modal, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ModalFormProps } from '@ant-design/pro-form/lib';
import type { ActionType } from '@ant-design/pro-table';
import React, { useContext, useEffect, useState } from 'react';

import Loading from '@/components/Loading';
import { usePost, Response, usePut } from '@/app/request';
import useFormService from '@/components/Form/useFormService';

import {
  IAuthTreeService,
  ICodeTree,
} from '../../components/AuthTree/useSingleAuthTreeService';
import { NAME } from '.';
import { ITreeData } from '../../components/AuthTree/useAuthTreeService';
import { AuthorizationService } from '../../useAuthorizationService';
import Api from '@/components/Authorization/api';

export type IFromPorps = ModalFormProps & {
  dataSource: any;
  setVisible: Function;
  visible: boolean;
  status: 'Blank' | 'Edit' | 'Preview';
  tableRef: React.MutableRefObject<ActionType>;
};

interface IFuncDetail {
  id: any;
  name: string;
  [NAME]: ITreeData[];
}

export default function useFuncDetailServices(
  props: IFromPorps & { authTreeService: IAuthTreeService },
) {
  const [form] = useForm();
  const [dataSource, setDataSource] = useState<IFuncDetail>();

  const { formatMessage, baseApi } = useContext(AuthorizationService);
  const { validateFields } = useFormService();

  /**
   * 请求详情数据
   */
  const {
    data,
    loading,
    run: getDetail,
  } = useRequest<Response<ICodeTree[]>, any, IFuncDetail, any>(
    () => {
      return {
        method: 'get',
        url:
          props.status === 'Blank'
            ? `${baseApi}${Api.FuncAuthBlankTree}`
            : `${baseApi}${Api.FuncAuthDetail}?globeRoleId=${props.dataSource.id}`,
      };
    },
    {
      manual: true,
      formatResult: (res) => {
        const checkedKeys = props.authTreeService.initTree(res.data);
        return {
          ...props?.dataSource,
          [NAME]: checkedKeys,
        };
      },
      onSuccess: (data) => {
        setDataSource(data);
      },
    },
  );

  useEffect(() => {
    props.visible && getDetail();
  }, [props.visible]);

  useEffect(() => {
    form.setFieldsValue(data);
  }, [data]);

  loading ? Loading.show() : Loading.hideAll();

  /**
   * 创建/更新
   */
  const onManage = async (form, status: 'CREATE' | 'UPDATE') => {
    if (await validateFields(form)) {
      Modal.confirm({
        centered: true,
        cancelText: formatMessage('取消'),
        okText: formatMessage('确认'),
        title: formatMessage('提示'),
        content:
          status === 'CREATE'
            ? formatMessage('你确认要创建数据吗')
            : formatMessage('你确认要更新数据吗'),
        onCancel: (close) => {
          close();
        },
        onOk: async () => {
          const value = form.getFieldsValue();
          console.log('创建/更新', value);
          var params;

          if (status === 'CREATE') {
            params = {
              name: value?.name,
              addFunctionPointIds: props.authTreeService.formatAddKeysList(
                value?.[NAME],
              ),
            };
          } else {
            params = {
              globeRoleId: props.dataSource.id,
              // 若没有更新通用角色名称则不传参
              ...(props.dataSource.name === value?.name
                ? null
                : { globeRoleName: value?.name }),
              addFunctionPointIds: props.authTreeService.formatAddKeysList(
                value?.[NAME],
              ),
              removeFunctionPointIds:
                props.authTreeService.formatRemoveKeysList(value?.[NAME]),
            };
          }
          console.log(1111111, params);
          debugger;
          Loading.show();

          let request =
            status === 'CREATE'
              ? await usePost(`${baseApi}${Api.FuncAuthCreate}`, params)
              : await usePut(`${baseApi}${Api.FuncAuthUpdate}`, params);
          Loading.hide();
          console.log('FunctionalManage', request);
          if (request.success) {
            props.tableRef.current.reload();
            props.setVisible(false);
            message.success(
              status === 'CREATE'
                ? formatMessage('创建成功')
                : formatMessage('更新成功'),
            );
          } else {
            message.warning(
              status === 'CREATE'
                ? formatMessage('创建失败')
                : formatMessage('更新失败'),
            );
          }
        },
      });
    }
  };

  const onCreate = async (form) => {
    await onManage(form, 'CREATE');
  };
  const onUpdate = async (form) => {
    await onManage(form, 'UPDATE');
  };

  const renderBtn = (
    btns: [createBtn: JSX.Element, updateBtn: JSX.Element],
  ) => {
    const [createBtn, updateBtn] = btns;
    switch (props.status) {
      case 'Blank':
        return [createBtn];
      case 'Edit':
        return [updateBtn];
      case 'Preview':
      default:
        break;
    }
  };

  return {
    dataSource,
    loading,
    onCreate,
    onUpdate,
    form,
    renderBtn,
  };
}
