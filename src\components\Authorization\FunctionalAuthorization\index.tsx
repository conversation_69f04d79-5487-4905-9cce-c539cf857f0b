import React from 'react';
import MedalsoftTable from '@/components/ProTable';
import { TextAlign, Wrapper } from '../style';
import useFuncAuthService from './useFuncAuthService';
import Detail from './FunctionalDetail';
import { Button } from 'antd';
import { AuthorizationService, IBaseProps } from '../useAuthorizationService';

/**
 * 功能授权公共组件，该组件在配置完成基地址及自定义的表单请求函数后，将生成基本的功能授权页面，页面支持查看通用角色列表，可在其中新增通用角色，并为该通用角色授予功能权限
 * @author: Phoebe.Lv
 */
export function MedalsoftFuncAuth<T, U>(props: IBaseProps<T, U>) {
  const {
    actionRef,
    promiseFunction,
    modal,
    setModal,
    onAdd,
    onEdit,
    formatMessage,
    onDelete,
  } = useFuncAuthService(props);

  const nameColums = {
    title: formatMessage('角色'),
    dataIndex: 'name',
    ellipsis: true,
    width: '50%',
  };

  const operateColums = {
    title: <TextAlign>{formatMessage('操作')}</TextAlign>,
    valueType: 'option',
    width: '50%',
    render: (text, record, _, action) => {
      const EditBtn = (
        <Button
          type="link"
          onClick={() => {
            console.log('record');
            onEdit(record);
          }}
          size={'small'}
        >
          {formatMessage('编辑')}
        </Button>
      );

      const Delete = (
        <Button
          type="link"
          danger
          onClick={() => {
            console.log('record');
            onDelete([record.id]);
          }}
          size={'small'}
        >
          {formatMessage('删除')}
        </Button>
      );

      return (
        <TextAlign>
          {EditBtn} {Delete}
        </TextAlign>
      );
    },
  };

  return (
    <AuthorizationService.Provider
      value={{
        formatMessage,
        baseApi: props.baseAPi,
        maxTreeLevel: props.maxTreeLevel,
      }}
    >
      <Wrapper>
        <MedalsoftTable
          actionRef={actionRef}
          columns={[nameColums, props.columns, operateColums].filter((i) => i)}
          request={(params) => promiseFunction(params)}
          toolBarRender={() => {
            return [
              <Button
                key="create-button"
                type="primary"
                onClick={() => onAdd()}
              >
                {formatMessage('创建')}
              </Button>,
            ];
          }}
          {...props.tableProps}
        ></MedalsoftTable>

        {modal?.visible ? (
          <Detail
            dataSource={modal.dataSource}
            visible={modal.visible}
            setVisible={setModal}
            status={modal.status}
            tableRef={actionRef}
          />
        ) : null}
      </Wrapper>
    </AuthorizationService.Provider>
  );
}
export default MedalsoftFuncAuth;
