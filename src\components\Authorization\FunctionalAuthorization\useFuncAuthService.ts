import useTableServices from '@/components/Table/useTableWithModalService';
import {
  EnumConditionalType,
  formatConditionService,
  useDelete,
} from '@/app/request';
import useAuthorizationService, {
  IBaseProps,
} from '../useAuthorizationService';
import Api from '@/components/Authorization/api';

export interface IList {
  id: any;
  name: string;
  Created: string;
  updatedTime: string;
  updatedUser: string;
  createdUser: string;
}

export default function useFuncAuthService<T, U>(props: IBaseProps<T, U>) {
  const { formatCondition } = formatConditionService();
  const { formatMessage } = useAuthorizationService({
    formatMessage: props.formatMessage,
    messagePrefix: props.messagePrefix,
  });

  const {
    dataSource,
    setDataSource,
    actionRef,
    modal,
    setModal,
    onAdd,
    onEdit,
    onDelete,
  } = useTableServices({ formatMessage: formatMessage });

  /**
   * 列表数据请求
   */
  const promiseFunction = (params?) => {
    let Conditions = [];
    Conditions.push(
      formatCondition('Name', params.name, EnumConditionalType.Like),
    );

    return props.useProTablePromiseByPost<IList[]>(
      {
        url: `${props.baseAPi}${Api.FuncAuthPageList}`,
        params: {
          PageIndex: params.current,
          PageSize: params.pageSize,
          OrderField: 'Created',
          OrderType: 'desc',
          Conditions: Conditions.filter((item) => item),
        },
      },
      {
        successCallback: (res) => {
          console.log('successCallback', res);
          setDataSource(res.data?.data);
        },
      },
    );
  };
  const onFuncAuthDelete = (ids: string[]) =>
    onDelete(ids, () =>
      useDelete(`${props.baseAPi}${Api.FuncAuthDelete}`, {
        data: JSON.stringify(ids),
        headers: {
          'content-type': 'application/json',
        },
      }),
    );

  return {
    dataSource,
    actionRef,
    modal,
    setModal,
    promiseFunction,
    onAdd,
    onEdit,
    onDelete: onFuncAuthDelete,
    formatMessage,
  };
}
