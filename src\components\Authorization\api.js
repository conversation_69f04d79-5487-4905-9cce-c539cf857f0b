export const Api = {
  // 权限
  RolesByAccount: '/Api/Admin/Permission/GetUserRoles', // 获取某角色名下的通用角色内容
  // AuthCodesByAccount: '/Api/Admin/Permission/GetAllFunctionPointCodeWithUser', // 获取账号名下的功能列表
  // GetAllFunctionPointsIntoCache: '/Debug/GetAllFunctionPointsIntoCache', // 获取账号名下的功能列表(加载缓存)

  // 功能权限
  FuncAuthPageList: '/Api/Admin/Permission/GlobeRolePageList', // 角色分页
  FuncAuthBlankTree: '/Api/Admin/Metadata/Feature/AllFeature', // 获取全部空白未勾选的功能点（新增用）
  FuncAuthDetail: '/Api/Admin/Permission/GetFunctionPointsWithGlobeRole', // 获取某角色名下的功能点（编辑用）
  FuncAuthCreate: '/Api/Admin/Permission/GlobeRole', // 新增角色及功能权限
  FuncAuthUpdate: '/Api/Admin/Permission/GlobeRole', // 编辑角色及功能权限
  FuncAuthDelete: '/Api/Admin/Permission/GlobeRole', // 删除角色及功能权限

  // 数据权限
  DataAuthPageList: '/Api/User/BasicUserList', // 数据权限分页-获取系统人员名单 1
  DataAuthBlankTree: '/Api/Admin/Permission/GetAllBusinessTree', // 获取全部空业务树的数据
  DataAuthTreeDetail: '/Api/Admin/Permission/GetRoleBusinessTree', // 获取当前角色跟业务树的关系
  DataAuthCreate: '/Api/Admin/Permission/Role', // 新增某角色名下的通用角色内容
  DataAuthUpdate: '/Api/Admin/Permission/Role', // 更新某角色名下的通用角色内容
  DataAuthRemove: '/Api/Admin/Permission/Role', // 删除一个角色
};

export default Api;
