import React from 'react';
import { Tree } from 'antd';

export interface IProps {
  value?: any;
  onChange?: (value) => void;
  treeData: any;
}

const AuthTree = (props: IProps) => {
  return (
    <Tree
      checkable
      defaultExpandAll
      treeData={props.treeData}
      onCheck={(checkedKeys, e) => {
        props?.onChange?.(checkedKeys);
      }}
      {...props}
    />
  );
};
export default AuthTree;
