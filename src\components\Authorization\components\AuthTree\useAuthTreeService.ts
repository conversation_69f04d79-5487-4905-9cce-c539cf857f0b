export type IAuthTreeService = ReturnType<typeof useAuthTreeService>;

export interface ITreeData {
  title: string;
  key: string;
  parentId: string;
  hasChildren: boolean;
  type?: number;
  children: ITreeData[];
}

export default function useAuthTreeService() {
  /** 过滤选中子树的所有子节点 */
  const filterSonTreeChildrenKeys = (
    checkedKeys: Array<React.Key>,
    parentKeys: React.Key[],
    treeData: ITreeData[],
  ): Array<React.Key> => {
    // 选取需要被过滤子级的父节点
    const filterParentKeys = checkedKeys?.filter((key) =>
      parentKeys?.includes(key),
    );
    // 选取需要被过滤的子节点
    var filterChildrenKeys = [];
    filterParentKeys?.map((parentKey) => {
      getParentChildrenKeys(parentKey, treeData, filterChildrenKeys);
    });
    //过滤子节点
    return checkedKeys?.filter((key) => !filterChildrenKeys.includes(key));
  };

  /**选取属于父节点名下的子节点 */
  const getParentChildrenKeys = (
    parentKey,
    treeData: ITreeData[],
    filterKeys,
  ) => {
    treeData?.length &&
      treeData?.map((data) => {
        if (data.key === parentKey) {
          data.children?.length &&
            getParentChildrenKeys(parentKey, data.children, filterKeys);
        } else if (data.parentId === parentKey) {
          filterKeys?.push(data.key);
          data.children?.length &&
            getParentChildrenKeys(data.key, data.children, filterKeys);
        } else {
          data.children?.length &&
            getParentChildrenKeys(parentKey, data.children, filterKeys);
        }
      });
  };

  return { filterSonTreeChildrenKeys };
}
