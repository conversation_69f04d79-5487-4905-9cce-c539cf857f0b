import React, { useState } from 'react';

import {
  IBusinessTree,
  IBusinessTreeResponse,
} from '../../DataAuthorization/DataDetail/DataTrees/useDataTreesService';
import useAuthTreeService, { ITreeData } from './useAuthTreeService';

export type IMultiAuthTreeService = ReturnType<typeof useMultiAuthTreeService>;

type ITreesDetail = {
  label: string;
  name: React.Key;
  treeData: ITreeData[];
};

type IMultiKeysObj = Record<string, string[]>;

// 这个服务将被注册至全局
export const MultiAuthTreeService =
  React.createContext<ReturnType<typeof useMultiAuthTreeService>>(undefined);

export default function useMultiAuthTreeService() {
  const [checkedKeysObjInDB, setCheckedKeysObjInDB] = useState<IMultiKeysObj>();
  const [childrenKeysObj, setChildrenKeysObj] = useState<IMultiKeysObj>();
  const [parentKeysObj, setParentKeysObj] = useState<IMultiKeysObj>();
  const [treesData, setTreesData] = useState<ITreesDetail[]>();
  var tempCheckedKeys = {};
  var tempChildrenKeys = {};
  var tempParentKeys = {};
  const { filterSonTreeChildrenKeys } = useAuthTreeService();

  const initTrees = (data) => {
    tempChildrenKeys = {};
    let trees: ITreesDetail[] = getTreesData(data);
    setTreesData(trees);
    setChildrenKeysObj(tempChildrenKeys);
    setParentKeysObj(tempParentKeys);
    setCheckedKeysObjInDB(tempCheckedKeys);
    return tempCheckedKeys;
  };

  const getTreesData = (trees: IBusinessTreeResponse[]): ITreesDetail[] => {
    if (trees?.length) {
      return trees.map((businessTree) => {
        if (businessTree?.businessTreeTructures?.length) {
          return {
            label: businessTree.businessTreeName,
            treeData: getTreeData(
              businessTree.businessTreeTructures,
              businessTree.businessTreeTructures[0].type,
            ),
            name: businessTree.businessTreeTructures[0].type,
          };
        }
      });
    }
  };

  const getTreeData = (
    businessTree: IBusinessTree[],
    name: string | number,
  ): ITreeData[] => {
    if (!tempCheckedKeys[name]) {
      tempCheckedKeys[name] = [];
    }
    if (!tempChildrenKeys[name]) {
      tempChildrenKeys[name] = [];
    }
    if (!tempParentKeys[name]) {
      tempParentKeys[name] = [];
    }
    return businessTree.map((node) => {
      // 抽取checkedKeys
      node.isHas && tempCheckedKeys[name].push(node.nodeId);
      !node.childNodeList?.length && tempChildrenKeys[name].push(node.nodeId);
      node.childNodeList?.length && tempParentKeys[name].push(node.nodeId);
      return {
        title: node.nodeName,
        key: node.nodeId,
        parentId: node.parentNodeId,
        hasChildren: node.childNodeList?.length > 0,
        type: node.type,
        children:
          node.childNodeList?.length && getTreeData(node.childNodeList, name),
      };
    });
  };

  /*  过滤非子节点的key */
  const getChildrenCheckKeys = (keysList: IMultiKeysObj): IMultiKeysObj => {
    let obj = {};
    Object.keys(keysList)?.map((treeKey) => {
      obj[treeKey] = keysList?.[treeKey]?.filter((keys) => {
        return childrenKeysObj[treeKey].includes(keys);
      });
    });
    return obj;
  };

  /* 过滤选中子树的所有子节点 */
  const filterMultiSonTreeChildrenKeys = (
    checkedKeysList: IMultiKeysObj,
  ): IMultiKeysObj => {
    var obj = {};
    Object.keys(checkedKeysList)?.map((treekey) => {
      let checkedKeys = checkedKeysList[treekey];
      let treeData = treesData?.filter((data) => data.name == treekey)?.[0]
        ?.treeData;
      let parentKeys = parentKeysObj[treekey];

      // 过滤选中子树的所有子节点
      let keysList = filterSonTreeChildrenKeys(
        checkedKeys,
        parentKeys,
        treeData,
      );
      obj[treekey] = keysList;
    });

    return obj;
  };

  const formatAddKeysList = (checkedKeysList: IMultiKeysObj): IMultiKeysObj => {
    // 过滤非子节点的key
    // let childrenKeysList = getChildrenCheckKeys(checkedKeysList);
    let childrenKeysList = filterMultiSonTreeChildrenKeys(checkedKeysList);

    let obj = {};
    // 对比数据库的key，查出新增的key
    Object.keys(childrenKeysList)?.map((treeKey) => {
      obj[treeKey] = childrenKeysList?.[treeKey]?.filter((keys) => {
        return !checkedKeysObjInDB[treeKey]?.includes(keys);
      });
    });
    return obj;
  };

  const formatRemoveKeysList = (checkedKeysList: IMultiKeysObj) => {
    // 过滤非子节点的key
    // let childrenKeysList = getChildrenCheckKeys(checkedKeysList);
    let childrenKeysList = filterMultiSonTreeChildrenKeys(checkedKeysList);
    let obj = {};
    // 对比数据库的key，查出移除的key
    Object.keys(childrenKeysList)?.map((treeKey) => {
      obj[treeKey] = checkedKeysObjInDB?.[treeKey]?.filter((keys) => {
        return !childrenKeysList[treeKey]?.includes(keys);
      });
    });
    return obj;
  };

  return {
    initTrees,
    treesData,
    formatAddKeysList,
    formatRemoveKeysList,
  };
}
