import React, { useContext, useState } from 'react';
import useAuthTreeService, { ITreeData } from './useAuthTreeService';
import {
  AuthorizationService,
  IFormatMessage,
} from '@/components/Authorization/useAuthorizationService';
export type IAuthTreeService = ReturnType<typeof useSingleAuthTreeService>;

export interface ICodeTree {
  id: string;
  Created: string;
  updatedTime: string;
  updatedUser: string;
  createdUser: string;
  parentId: string;
  name: string;
  url: string; // 可调用的接口
  code: string; // 预留位置，暂无用
  isHas: boolean; // 仅最细节点会有状态变化，父级节点全为false
  childNodeList: ICodeTree[];
}

// 这个服务将被注册至全局
export const AuthTreeService =
  React.createContext<ReturnType<typeof useSingleAuthTreeService>>(undefined);

export default function useSingleAuthTreeService() {
  const [checkedKeysInDB, setCheckedKeysInDB] = useState<React.Key[]>();
  const [childrenKeys, setChildrenKeys] = useState<React.Key[]>([]);
  const [parentKeys, setParentKeys] = useState<React.Key[]>([]);
  const [treeData, setTreeData] = useState<ITreeData[]>([]);
  var tempCheckedKeys = [];
  var tempChildrenKeys = [];
  var tempParentKeys = [];
  const { formatMessage, maxTreeLevel } = useContext(AuthorizationService);
  const { filterSonTreeChildrenKeys } = useAuthTreeService();

  const initTree = (data) => {
    tempChildrenKeys = [];
    let tree: ITreeData[] = getTreeData(data, 1);

    setTreeData(tree);
    setChildrenKeys(tempChildrenKeys);
    setCheckedKeysInDB(tempCheckedKeys);
    setParentKeys(tempParentKeys);
    return tempCheckedKeys;
  };
  const getTreeData = (item: ICodeTree[], treeLevel): ITreeData[] => {
    if (!maxTreeLevel || maxTreeLevel >= treeLevel) {
      if (item?.length) {
        return item.map((node) => {
          // 抽取checkedKeys
          node.isHas && tempCheckedKeys.push(node.id);
          // 抽取叶子节点
          !node.childNodeList?.length && tempChildrenKeys.push(node.id);
          // 抽取父级节点
          node.childNodeList?.length && tempParentKeys.push(node.id);

          return {
            title: formatMessage(node.name),
            key: node.id,
            parentId: node.parentId,
            level: treeLevel,
            hasChildren: node.childNodeList?.length > 0,
            children:
              node.childNodeList?.length &&
              getTreeData(node.childNodeList, treeLevel + 1),
          };
        });
      }
    }
  };

  /* 过滤非子节点的key */
  const getChildrenCheckKeys = (
    checkedKeys: Array<React.Key>,
  ): Array<React.Key> => {
    return checkedKeys?.filter((key) => childrenKeys.includes(key));
  };

  // /** 过滤选中子树的所有子节点 */
  // const filterSonTreeChildrenKeys = (
  //   checkedKeys: Array<React.Key>,
  // ): Array<React.Key> => {
  //   // 选取需要被过滤子级的父节点
  //   const filterParentKeys = checkedKeys?.filter((key) =>
  //     parentKeys.includes(key),
  //   );
  //   // 选取需要被过滤的子节点
  //   var filterChildrenKeys = [];
  //   filterParentKeys.map((parentKey) => {
  //     getParentChildrenKeys(parentKey, treeData, filterChildrenKeys);
  //   });
  //   //过滤子节点
  //   return checkedKeys.filter((key) => !filterChildrenKeys.includes(key));
  // };

  // /**选取属于父节点名下的子节点 */
  // const getParentChildrenKeys = (
  //   parentKey,
  //   treeData: ITreeData[],
  //   filterKeys,
  // ) => {
  //   treeData?.length &&
  //     treeData?.map((data) => {
  //       if (data.key === parentKey) {
  //         data.children?.length &&
  //           getParentChildrenKeys(data.key, data.children, filterKeys);
  //       } else if (data.parentId === parentKey) {
  //         filterKeys.push(data.key);
  //         data.children?.length &&
  //           getParentChildrenKeys(data.key, data.children, filterKeys);
  //       }
  //     });
  // };

  const formatAddKeysList = (
    checkedKeysList: Array<React.Key>,
  ): Array<React.Key> => {
    // // 过滤非子节点的key
    // let childrenKeysList = getChildrenCheckKeys(checkedKeysList);

    // 过滤选中子树的所有子节点
    let keysList = filterSonTreeChildrenKeys(
      checkedKeysList,
      parentKeys,
      treeData,
    );
    // 对比数据库的key，查出新增的key
    return keysList?.filter((keys) => {
      return !checkedKeysInDB?.includes(keys);
    });
  };

  const formatRemoveKeysList = (
    checkedKeysList: Array<React.Key>,
  ): Array<React.Key> => {
    // // 过滤非子节点的key
    // let childrenKeysList = getChildrenCheckKeys(checkedKeysList);

    // 过滤选中子树的所有子节点
    let keysList = filterSonTreeChildrenKeys(
      checkedKeysList,
      parentKeys,
      treeData,
    );
    // 对比数据库的key，查出移除的key
    return checkedKeysInDB?.filter((keys) => {
      return !keysList?.includes(keys);
    });
  };

  return { initTree, treeData, formatAddKeysList, formatRemoveKeysList };
}
