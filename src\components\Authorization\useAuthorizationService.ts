import React from 'react';
import { MedalsoftTableProps } from '@/components/ProTable';
import { IUsePorTablePromise } from '../ProTable/useProTableService';
export type IFormatMessage = (
  key: string,
  attr?: Record<string, any>,
) => string;

export interface IBaseProps<T, U> {
  /** 请求基地址 */
  baseAPi: string;
  /** 额外的业务列*/
  columns?: any[];
  /**多语言翻译函数 */
  formatMessage?: IFormatMessage;
  /**使用多语言翻译的自定义前缀，未重写将默认使用ImageFormItem_前缀，无需前缀传空字符串*/
  messagePrefix?: string;
  /**树的最大显示层级（仅功能权限树） */
  maxTreeLevel?: number;
  /**antd proTable的额外属性注入 */
  tableProps?: MedalsoftTableProps<T, U>;
  /**自定义ProTable所需的Post请求 */
  useProTablePromiseByPost: IUsePorTablePromise;
  /**自定义ProTable所需的Get请求 */
  useProTablePromiseByGet: IUsePorTablePromise;
}

type IProps = {
  formatMessage: IFormatMessage;
  messagePrefix: string;
};

export const DEFAULT_PREFIX = 'Authorization_';

export default function useAuthorizationService(props: IProps) {
  /** 重写多语言翻译函数 */
  const formatMessage: IFormatMessage = (key, attr) => {
    const prefix =
      props.messagePrefix !== undefined ? props.messagePrefix : DEFAULT_PREFIX;
    const formatKey = `${prefix}${key}`;
    return props.formatMessage ? props.formatMessage(formatKey, attr) : key;
  };
  return { formatMessage };
}

type IAuthorizationService = {
  formatMessage: IFormatMessage;
  baseApi: String;
  maxTreeLevel?: number; // 树的最大显示层级（仅功能权限树）
};

export const AuthorizationService =
  React.createContext<IAuthorizationService>(undefined);
