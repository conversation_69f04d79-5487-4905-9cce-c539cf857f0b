import styled from 'styled-components';
import { ModalForm } from '@ant-design/pro-form';

export const MedalsoftModalFrom = styled(ModalForm)`
  .ant-modal {
    width: ${(props) => props.theme.modalWidth} !important;
    .ant-modal-body {
      max-height: ${(props) => props.theme.modalHeight};
      overflow-y: auto;
      .ant-form-item-label {
        min-width: 160px;
        max-width: 160px;
        > label {
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          display: block !important;
          white-space: nowrap;
          line-height: 32px;
          padding: 0 8px 0 0;
          &::after {
            content: ':';
            position: absolute;
            right: -5px;
          }
        }
      }
    }
  }
`;
