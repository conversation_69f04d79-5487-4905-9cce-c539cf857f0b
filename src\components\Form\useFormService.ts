import type { FormInstance } from 'antd/lib/form';
export default function useFormService() {
  /**
   * 触发表单校验，校验表单是否有非法信息，若有字段校验非法，滚动到对应字段处
   * @param from
   * @return 表单是否校验通过
   */
  const validateFields = async (form: FormInstance<any>) => {
    try {
      await form.validateFields();
      return true;
    } catch (e) {
      let errorArr = e.errorFields.filter((item) => {
        return item.errors?.length > 0;
      });
      errorArr?.length && form.scrollToField(errorArr[0].name);
      return false;
    }
  };

  return { validateFields };
}
