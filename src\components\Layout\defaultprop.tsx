import { IRoute } from '.';
import React from 'react';
import { SettingOutlined, SolutionOutlined } from '@ant-design/icons';
/**默认参数 */
export const defaultRoute: IRoute = {
  path: '/',
  routes: [
    {
      name: '主页',
      path: '/home',
    },
    {
      path: '/cusutom',
      name: '客户管理',
      icon: <SolutionOutlined />,
      // redirect: '/custom',
      routes: [
        {
          name: '客户信息',
          path: '/custom/info/list',
        },
      ],
    },
    {
      path: '/setting',
      name: '系统设置',
      icon: <SettingOutlined />,
      // redirect: '/setting',
      routes: [
        {
          name: '客户动态标签管理',
          path: '/setting/tag/list',
        },
        {
          name: '角色管理',
          path: '/setting/role/list',
        },
        {
          name: '组织架构管理',
          path: '/setting/organization/list',
        },
        {
          name: '日志查看',
          path: '/setting/log/list',
        },
      ],
    },
  ],
};

export default defaultRoute;
