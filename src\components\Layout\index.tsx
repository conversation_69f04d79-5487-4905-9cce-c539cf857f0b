import ProLayout, { BasicLayoutProps, PageContainer } from '@ant-design/pro-layout';
import { HeaderViewProps } from '@ant-design/pro-layout/lib/Header';
import { MenuDataItem, Route } from '@ant-design/pro-layout/lib/typings';
import 'antd/dist/antd.less';
import '@ant-design/flowchart/dist/index.css';
import Input from 'antd/lib/input';
import React, { useEffect, useState, createContext } from 'react';
import { Link, useHistory } from 'umi';
import { defaultRoute } from './defaultprop';
import logo from '@/assets/images/logo.png';
import MedalsoftMenuItem, { IMenuItemNotice } from './MenuItem';
import { queryLoginUser } from '@/app/request/requestApi';
import PersonalDropdown from '@/components/PersonalDropdown';
import { RightContentDiv, RightIcon, LayoutDiv, LogoImg, LogoImgWrap, LogoTitleDiv, Control, Fold } from './style';
import useLayoutServices from './useLayoutService';
import { ConfigProvider, Modal } from 'antd';
import { useLocation } from 'umi';
import { CaretLeftFilled, CaretRightFilled } from '@ant-design/icons';
import Loading from '@/components/Loading';
import '@/assets/iconfont/iconfont.css';
import backentry from '@/assets/images/backentry.png';
import initstatement from '@/assets/images/initstatement.png';
import backstatement from '@/assets/images/backstatement.png';
import draft from '@/assets/images/draft.png';
import tobesettle from '@/assets/images/tobesettle.png';
import settled from '@/assets/images/settled.png';
import tobecheck from '@/assets/images/tobecheck.png';
import ordernumber from '@/assets/images/ordernumber.png';
import supplierInfo from '@/assets/images/supplierInfo.png';
import userAccount from '@/assets/images/userAccount.png';
import productInfo from '@/assets/images/productInfo.png';
import settlement from '@/assets/images/settlement.png';
import pmMail from '@/assets/images/pmMail.png';
import purchaseMail from '@/assets/images/purchaseMail.png';
import fleetPick from '@/assets/images/fleetPick.png';
import fleetDeliver from '@/assets/images/ordernumber.png';
import noGold from '@/assets/images/noGold.png';
import mutiSupply from '@/assets/images/mutiSupply.png';
import noCompare from '@/assets/images/noCompare.png';
import sysParam from '@/assets/images/sysParam.png';
import rolePermission from '@/assets/images/rolePermission.png';
import billingmanagement from '@/assets/images/Billingmanagement.png';
import statement from '@/assets/images/Statement.png';
import statementQuery from '@/assets/images/StatementQuery.png';
import supplierManagement from '@/assets/images/ordernumber.png';
import sapMapping from '@/assets/images/initstatement.png';
import hm from '@/assets/images/hm.png';
import sj from '@/assets/images/sj.png';
import enUS from 'antd/lib/locale/en_US';
import zhCN from 'antd/lib/locale/zh_CN';

export type IMedalsoftLayoutProps = {
  component?: string;
  /** 菜单数据 */
  route: IRoute;
  /** 菜单搜索 - 即时的前端搜索，对标antd树控件搜索UE，默认开启搜索功能 */
  search?: boolean;
  /** 重写rightContentRender，套接默认间距等样式 */
  rightContentRender?: (props: HeaderViewProps) => React.ReactNode[];
} & Omit<
  BasicLayoutProps,
  'location' | 'menuItemRender' | 'menuExtraRender' | 'menuDataRender' | 'postMenuData' | 'rightContentRender' | 'route'
>;

export type IRoute = {
  /** children */
  routes?: IRoute[];
  /** 子菜单提醒 */
  notice?: IMenuItemNotice;
} & Route;

const iconObj = {
  backentry,
  initstatement,
  backstatement,
  draft,
  tobesettle,
  settled,
  tobecheck,
  ordernumber,
  supplierInfo,
  userAccount,
  productInfo,
  settlement,
  pmMail,
  purchaseMail,
  fleetPick,
  fleetDeliver,
  noGold,
  mutiSupply,
  noCompare,
  sysParam,
  rolePermission,
  billingmanagement,
  statement,
  statementQuery,
  supplierManagement,
  sapMapping,
};

/** 支持多级菜单附增icon的dom */
const IconMenu = (icon, name) => (
  <>
    {/* <span role="img" className="anticon">
      <img src={backentry}></img>
    </span> */}
    {icon && (
      <span role="img" className="anticon">
        <img src={iconObj[icon]}></img>
      </span>
    )}
    <span>{name}</span>
  </>
);

/** 支持菜单notice的dom */
const NoticeMenu = (menuItemProps: IRoute, collapsed: boolean, menuDom: React.ReactNode) =>
  menuItemProps.notice?.path === menuItemProps.path && menuItemProps.notice?.request ? (
    <MedalsoftMenuItem menu={menuItemProps} defaultdom={menuDom} collapsed={collapsed}></MedalsoftMenuItem>
  ) : (
    menuDom
  );

export const MedalsoftLayout = (props: IMedalsoftLayoutProps) => {
  const location = useLocation<any>();
  const { getMenuDataBySearch, keyword, collapsed, setCollapsed, openkeys, onSearch } = useLayoutServices(props);
  const { search = true } = props;
  const history = useHistory();
  const [locale, setLocale] = useState<any>();
  const [userName, setUserName] = useState('');
  const [routes, setRoutes] = useState(Object);
  const [openKeys, setOpenKeys] = useState(Array<any>());
  const [rootSubmenuKeys, setRootSubmenuKeys] = useState(Array<any>());
  const [control, setControl] = useState(false);
  let authRoutes = []; // 所有有权限的路由list
  let authBtns = []; // 所有有权限的按钮
  let menuTree = { path: '/', routes: [] }; //封装菜单格式tree
  let detailRoutes = ['/CIM/custom/info/view', '/CIM/custom/info/detail']; // 所有详情路由
  //控制菜单栏折叠
  const toggleCollapsed = () => {
    setControl(!control);
  };
  const parseTree = (datas) => {
    for (var i in datas) {
      datas[i]['name'] = datas[i].authName;
      // if (datas[i].path && datas[i].parentId) {
      if (datas[i].path) {
        authRoutes.push(datas[i].path);
        authRoutes.push('/pto/supplier/details');
      }
      if (datas[i].subTree.length != 0) {
        parseTree(datas[i].subTree);
      }
      if (datas[i].subTree.length == 0 && datas[i].id != '22') {
        authBtns.push(datas[i].authCode);
        authBtns.push('details');
      }
    }
  };
  const handleMenuData = (data) => {
    let menuKeys = [];
    let arr = data.map((item) => {
      menuKeys.push(item.path);
      return {
        path: item.path,
        name: item.authName,
        routes: item.subTree,
        icon: item.icon,
      };
    });
    setOpenKeys(menuKeys);
    setRootSubmenuKeys(menuKeys);
    return arr;
  };
  const getUserInfo = () => {
    queryLoginUser().then((res) => {
      Loading.hide();
      if (res.success) {
        // console.log('第二次queryLoginUser');
        parseTree(res.data?.authList);
        setUserName(res.data?.realName);
        menuTree.routes = handleMenuData(res.data?.authList);
        sessionStorage.setItem('authRoutes', JSON.stringify(authRoutes));
        sessionStorage.setItem('authBtns', JSON.stringify(authBtns));
        sessionStorage.setItem('configList', JSON.stringify(res.data?.configList));
        // zz
        sessionStorage.setItem('authCodes', JSON.stringify(res.data?.authCodes));
        sessionStorage.setItem('supplierType', res.data?.supplierType);
        sessionStorage.setItem('supplierName', res.data?.supplierName);
        sessionStorage.setItem('isSap', res.data?.isSap);
        sessionStorage.setItem('userType', res.data?.userType);
        // zz
        let _routes = menuTree;
        setRoutes(_routes);
      } else {
        Modal.error({
          title: 'Tips',
          content: res.msg ?? res.Value.msg,
          okText: () => {},
          cancelText: '',
          centered: true,
          keyboard: false,
        });
      }
    });
  };
  useEffect(() => {
    getUserInfo();
    setLocale(zhCN);
  }, []);
  // console.log('林德routes', routes);
  return (
    <ConfigProvider locale={locale}>
      <LayoutDiv>
        <div style={{ position: 'fixed', top: 0, zIndex: 0, width: '100%' }}>
          <img
            src={hm}
            style={{
              display: history.location.pathname.indexOf('initstatement') != -1 ? 'block' : 'none',
              width: '100%',
            }}
          />
          <img
            src={sj}
            style={{
              display: history.location.pathname.indexOf('initstatement') == -1 ? 'block' : 'none',
              width: '100%',
            }}
          />
        </div>
        <ProLayout
          style={{ color: '#fff' }}
          logo={false}
          collapsedButtonRender={false}
          collapsed={
            location.pathname == '/pto/internalHome' || location.pathname == '/pto/supplierHome' ? false : control
          }
          fixedHeader={true}
          fixSiderbar={true}
          siderWidth={260}
          route={routes}
          navTheme="light"
          headerTheme="light"
          location={{ pathname: history.location.pathname }}
          disableMobile={true}
          headerTitleRender={() => {
            return (
              <div style={{ display: 'flex' }}>
                <LogoImgWrap>
                  <img src={logo} height="60" width="180" onClick={() => history.push('/pto/supplier/home')} />
                </LogoImgWrap>
                <LogoTitleDiv>{sessionStorage.getItem('userType') == 'Outer' ? '供应商平台' : '林德平台'}</LogoTitleDiv>
              </div>
            );
          }}
          // openKeys={openKeys}
          // onOpenChange={(keys: any) => {
          //   let latestOpenKey = keys.find((key) => rootSubmenuKeys.indexOf(key) === -1);
          //   if (!latestOpenKey && detailRoutes.indexOf(history.location.pathname) == -1) {
          //     setOpenKeys(keys);
          //   }
          // }}
          rightContentRender={(defalutprops) => (
            <RightContentDiv>
              <RightIcon mode={props.layout} key="name">
                <PersonalDropdown userName={userName} />
              </RightIcon>
            </RightContentDiv>
          )}
          menuProps={{
            onSelect: ({ item, key, keyPath, selectedKeys, domEvent }) => {
              if (!selectedKeys) {
                return false;
              }
              if ((key as string)?.startsWith('http://') || (key as string)?.startsWith('https://')) {
                return false;
              }
              let _routes = routes.routes;
              let _redirect: string;
              for (let i = 0; i < _routes.length; i++) {
                if (_routes[i].path == selectedKeys[0]) {
                  if (_routes[i].routes && _routes[i].routes[0]) {
                    _redirect = _routes[i].routes[0].path;
                    if (_routes[i].routes[0].routes && _routes[i].routes[0].routes[0]) {
                      _redirect = _routes[i].routes[0].routes[0].path;
                    }
                  }
                  if (_redirect) {
                    history.replace(_redirect);
                  } else {
                    history.replace(_routes[i].path);
                  }
                  break;
                }
              }
            },
          }}
          splitMenus={true}
          layout="mix"
          pageTitleRender={() => '供应商对账平台'}
          contentStyle={{ margin: '0' }}
          menuItemRender={(menuItemProps, defaultDom) => {
            /** 支持多级菜单附增icon的dom */
            const iconMenu = IconMenu(menuItemProps.icon, menuItemProps.name);
            /** 绑定外链进行跳转 */
            // if (
            //   menuItemProps.path?.startsWith('http://') ||
            //   menuItemProps.path?.startsWith('https://')
            // ) {
            //   return NoticeMenu(menuItemProps, collapsed, iconMenu);
            // }
            /** 使用path，视为使用本地路由进行跳转，path使用优先级大于component */
            if (menuItemProps.path && location.pathname !== menuItemProps.path) {
              return (
                <Fold>
                  <Link to={menuItemProps.path} target={menuItemProps.target}>
                    {iconMenu}
                  </Link>
                </Fold>
              );
            }
            /** 使用component进行跳转，path使用优先级大于component */
            return iconMenu;
          }}
          subMenuItemRender={(subMenuProps) => (
            <div>
              {subMenuProps.icon ? (
                <span className="anticon menuicon">
                  <span
                    style={{ fontWeight: 'normal' }}
                    className={`iconfont icon-${subMenuProps.icon as string}`}
                  ></span>
                </span>
              ) : (
                ''
              )}
              <span>{subMenuProps.name}</span>
            </div>
          )}
        >
          <Control>
            {location.pathname == '/pto/internalHome' || location.pathname == '/pto/supplierHome' ? (
              <div></div>
            ) : (
              <div onClick={toggleCollapsed}>{control ? <CaretRightFilled /> : <CaretLeftFilled />}</div>
            )}
          </Control>
          <PageContainer header={{ title: '', breadcrumb: {} }}>{props.children}</PageContainer>
        </ProLayout>
      </LayoutDiv>
    </ConfigProvider>
  );
};

const defaultProp: IMedalsoftLayoutProps = {
  route: {},
  search: true,
  title: '',
};
MedalsoftLayout.defaultProps = defaultProp;
export default MedalsoftLayout;
