import styled from 'styled-components';
import font from './font/YouSheBiaoTiHei.ttf';

export const LayoutDiv = styled.div`
  color:#fff !important @font-face {
    font-family: YouShe;
    src: url('${font}') format('TrueType');
  }
  /* a,
  a > * {
    color: rgba(255, 255, 255, 0.65);
    :hover {
      color: white !important;
    }
  } */
  height: 100vh;
  .ant-pro-sider-logo {
    display: flex;
    justify-content: center;
    height: 48px;
  }
  .ant-pro-sider-extra {
    margin-top: 20px;
  }
  .menuicon .iconfont {
    font-size: 23px;
    vertical-align: inherit;
  }
  .iconBtns {
    padding: 4px 10px;
    .anticon {
      font-size: 16px;
      vertical-align: middle;
    }
  }
  // #region
  .ant-pro-top-nav-header-menu li.ant-menu-item.ant-menu-item-only-child {
    width: 100px;
    height: 60px;
    line-height: 60px;
    font-size: 20px;
  }
  .ant-pro-sider-menu li.ant-menu-item.ant-menu-item-only-child a {
    display: flex;
    align-items: center;
    /* justify-content: center; */
  }
  .ant-pro-top-nav-header-menu .ant-menu.ant-menu-horizontal {
    /* margin-left: 20%; */
    margin-right: 15%;
    text-align: center;
  }
  ul.ant-menu-overflow.ant-menu.ant-menu-root.ant-menu-horizontal.ant-menu-light.top-nav-menu li {
    margin-right: 10%;
  }
  aside.ant-pro-sider-layout-mix {
    margin-top: 90px;
    padding-top: 0 !important;
    border-radius: 5px;
    .ant-menu-sub {
      background-color: #fff;
    }
  }
  aside.ant-pro-sider-layout-mix .ant-menu-sub {
    background-color: #015692;
  }
  .ant-layout-sider.ant-layout-sider-light.ant-pro-sider.ant-pro-sider-fixed.ant-pro-sider-layout-mix.ant-pro-sider-light {
    padding-top: 0 !important;
  }

  .ant-layout {
    background-color: transparent;
  }
  .ant-pro-page-container-children-content {
    padding: 30px 20px 15px 20px;
  }
  .ant-layout-sider-children {
    background-color: #015692;
  }
  .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-color: #0281bd;
    border-radius: 5px;
  }
  .ant-pro-sider.ant-layout-sider-light .ant-menu-item a,
  .ant-menu-submenu-title,
  .ant-menu-submenu .ant-menu-submenu-arrow {
    color: white;
  }

  .ant-menu-item,
  .ant-menu-item-active,
  .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
  .ant-menu-submenu-active,
  .ant-menu-submenu-title,
  .ant-menu-submenu-selected,
  .ant-menu-submenu > .ant-menu-submenu-title > .ant-menu-submenu-expand-icon,
  .ant-menu-submenu > .ant-menu-submenu-title > .ant-menu-submenu-arrow {
    color: white;
  }
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td,
  .ant-table tfoot > tr > th,
  .ant-table tfoot > tr > td {
    padding: 8px;
  }
  // #endregion
`;

export const RightContentDiv = styled.div`
  display: flex;
`;
export const RightIcon = styled.div<{ mode: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 60px;
  &:hover {
    background: #fff;
    cursor: pointer;
  }
  img {
    margin-right: 8px;
  }
`;

export const LogoImg = styled.img`
  width: 32px;
  object-fit: scale-down;
`;
export const LogoImgWrap = styled.div`
  img {
    height: 60px;
    object-fit: scale-down;
    vertical-align: top;
    /* margin: 0 20px; */
  }
  span {
    font-size: 18px;
    font-family: Arial;
    margin-right: 100px;
    font-weight: bold;
    margin-left: 20px;
  }
`;
export const LogoTitleDiv = styled.div`
  color: #333;
  font-size: 26px;
  line-height: 60px;
  font-family: YouShe;
  margin-left: 30px;
  span {
    color: rgba(51, 51, 51, 0.2);
    font-size: 18px;
    margin-left: 20px;
  }
`;
export const HeaderDiv = styled.div`
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.2);
  letter-spacing: 3px;
  font-size: 36px;
  font-family: YouShe;
`;

export const Control = styled.div`
  position: absolute;
  top: 50%;
  left: 0px;
  font-size: 20px;
  color: #ffff;
  background-color: #ccc;
`;
export const Fold = styled.div`
  position: relative;
`;
