import { useRef, useState } from 'react';
import { IMedalsoftLayoutProps, IRoute } from '.';

/**
 * 获取路由对象数组中所有的目标属性的值
 * @param routes 路由对象数组
 * @param attribute 目标属性
 * @returns
 */
const recurseRoutesAttribute = <T = string>(
  routes: IRoute[],
  attribute: string,
): T[] =>
  (routes ?? [])
    .map((route) => [
      route[attribute],
      ...recurseRoutesAttribute<T>(route.children, attribute),
    ])
    .flat()
    .filter((i) => i);

/**
 * 利用keyword搜索关键词对路由对象数组进行过滤
 * @param routes
 * @param keyword
 * @returns
 */
const filterByMenuDate = (routes: IRoute[], keyword: string): IRoute[] => {
  const res = routes
    .map((item) => {
      if (
        (item.name && item.name.includes(keyword)) ||
        filterByMenuDate(item.children || [], keyword).length > 0
      ) {
        return {
          ...item,
          children: filterByMenuDate(item.children || [], keyword),
        };
      }

      return undefined;
    })
    .filter((item) => item);
  return res;
};

export default function useLayoutServices(props: IMedalsoftLayoutProps) {
  const { route } = props;
  const [keyword, setKeyword] = useState('');
  const [collapsed, setCollapsed] = useState(false);
  const [openkeys, setOpenkeys] = useState<string[]>(undefined);
  /**
   * 用于暂存openkeys
   * 搜索时方法的执行顺序getMenuDataBySearch->filterByMenuDate->onSearch
   *
   */
  const openkeysRef = useRef<string[]>();
  const getMenuDataBySearch = (data: IRoute[], keyword: string): IRoute[] => {
    const res = filterByMenuDate(data, keyword);
    openkeysRef.current = recurseRoutesAttribute(res, 'key');
    return res;
  };

  const onSearch = (value) => {
    setKeyword(value);
    setOpenkeys(value ? openkeysRef.current : undefined);
  };

  const markRoutesNotice = (route: IRoute) => {
    if(route?.notice?.path) return;
    if (route?.notice) {
      route.notice.path = route.path || route.component;
    }
    route?.routes?.forEach((route) => {
      markRoutesNotice(route);
    });
  };

  markRoutesNotice(route);

  return {
    getMenuDataBySearch,
    keyword,
    setKeyword,
    collapsed,
    setCollapsed,
    openkeys,
    onSearch,
  };
}
