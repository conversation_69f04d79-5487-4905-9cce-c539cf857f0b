import { LogoutOutlined, SettingOutlined, UserOutlined, DownOutlined } from '@ant-design/icons';
import { Menu, Tooltip } from 'antd';
import 'antd/dist/antd.less';
import { DropDownProps } from 'antd/lib/dropdown/index';
import * as React from 'react';
import { DropdownWrapper, MenuItemWrapper } from './style';
import usePersonalDropdownService, { IFormatMessage } from './usePersonalDropdownService';

export type Action = 'UserSetting' | 'UserCenter' | 'SignOut';
export type IMedalsoftPersonDropdownProps = {
  /** 用户头像的文件地址/Base64Url，不传使用默认头像,图片来自数据库则在业务层使用Image服务转化 */
  avatar?: string;
  /** 用户名 */
  userName?: string;

  /** 用户名最大长度，超出... */
  maxUserNameLength?: number;
  /** 是否显示退出登录按钮, 默认为显示 */
  showSignOut?: boolean;
  /** dropdown content render函数，支持复写默认样式 */
  contentRender?: (defaultDoms: {
    /** 个人中心 */
    // UserCenter: React.ReactNode;
    // /** 个人设置 */
    // UserSetting: React.ReactNode;
  }) => React.ReactNode[];

  /** 返回boolean，觉得是否执行默认的事件 */
  onClick?: (action: Action) => Promise<boolean>;
  /** 多语言格式化 */
  formatMessage?: IFormatMessage;
} & Omit<DropDownProps, 'overlay'>;

/** 框架自带默认头像 */
const DefaultAvatar = 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png';

/**
 * <a href='https://preview.pro.ant.design/dashboard/analysis?primaryColor=%231890ff&fixSiderbar=true&colorWeak=false&pwa=false' target='_blank'>👉参考UI</a>
 * <hr/>
 * 1、顶栏显示：头像/默认图标+用户名（显示最大20字符，溢出裁切）<br/>
 * 2、hover下拉带单<br/>
 * 3、菜单默认包含以下内容，支持拓展其他菜单<br/>
 * 3.1、个人设置 - 对接标准化页面个人设置：基本信息、区域化设置（时间日期格式控制，数字格式控制等）<br/>
 * 3.2、退出登录<br/>
 *
 * @param {MedalsoftPersonDropdown} props
 * @author: 康锐
 */
export const MedalsoftPersonDropdown = (props: IMedalsoftPersonDropdownProps) => {
  const { showSignOut, userName, avatar, contentRender } = props;
  const { formatUserName, formatMessage, onItemClick } = usePersonalDropdownService(props);

  /** 个人中心节点 */
  const UserCenter = (
    <MenuItemWrapper key="UserCenter" onClick={() => onItemClick('UserCenter')}>
      <UserOutlined />
      {formatMessage('个人中心')}
    </MenuItemWrapper>
  );
  /** 个人设置节点 */
  const UserSetting = (
    <MenuItemWrapper key="UserSetting" onClick={() => onItemClick('UserSetting')}>
      <SettingOutlined />
      {formatMessage('个人设置')}
    </MenuItemWrapper>
  );

  return (
    <DropdownWrapper
      trigger={['click']}
      {...props}
      overlay={
        <Menu>
          {contentRender?.({}).map((item) => <MenuItemWrapper>{item}</MenuItemWrapper>) ?? []}
          {showSignOut && (
            <>
              {/* <Menu.Divider /> */}
              <Menu.Item key="SignOut" onClick={() => onItemClick('SignOut')}>
                <LogoutOutlined />
                {formatMessage('退出登录')}
              </Menu.Item>
            </>
          )}
        </Menu>
      }
    >
      {/* <Tooltip placement="bottom" title={userName}> */}
      <div>
        <img src={avatar ?? DefaultAvatar}></img>
        {formatUserName(userName)}
        <DownOutlined style={{ fontSize: 10, marginLeft: 8 }} />
      </div>
      {/* </Tooltip> */}
    </DropdownWrapper>
  );
};
export default MedalsoftPersonDropdown;

const defaultProps: IMedalsoftPersonDropdownProps = {
  placement: 'bottomLeft',
  showSignOut: true,
  maxUserNameLength: 20,
};

MedalsoftPersonDropdown.defaultProps = defaultProps;
