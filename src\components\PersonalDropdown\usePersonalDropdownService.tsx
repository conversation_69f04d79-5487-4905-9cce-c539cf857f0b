import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import { history } from 'umi';
import { Action, IMedalsoftPersonDropdownProps } from '.';
import React, { useContext } from 'react';
import { AzureADService } from '@/components/ThirdPartyAuthentication/AzureAD/useAzureADService';
import { PublicClientApplication } from '@azure/msal-browser';
import { queryLoginUser } from '@/app/request/requestApi';
import Mgr from '@/services/SecurityService';

export type IFormatMessage = (key: string, attr?: Record<string, any>) => string;
const usePersonalDropdownService = (props: IMedalsoftPersonDropdownProps) => {
  const { onClick, maxUserNameLength = 20 } = props;

  const aadService = useContext(AzureADService);
 
  const config = {
    auth: {
      clientId: process.env.CLIENT_ID,
      redirectUri: process.env.REDIRECT_URL,
      // postLogoutRedirectUri: 'https://pto-test.lindemobile.cn/#/pto/internalHome',
      postLogoutRedirectUri: 'https://pto.lindemobile.cn/#/pto/internalHome',
    },
  };

  const myMsal = new PublicClientApplication(config);

  // you can select which account application should sign out
  // const logoutRequest = {
  //   account: myMsal.getAccountByHomeId(homeAccountId),
  // };

  /** 重写多语言翻译函数 */
  const formatMessage: IFormatMessage = (key, attr) => {
    return props.formatMessage ? props.formatMessage(key, attr) : key;
  };
  // 用户名格式化函数
  const formatUserName = (username: string) => {
    if (username) {
      return username.length > maxUserNameLength ? `${username.substring(0, maxUserNameLength)}...` : username;
    }
    return '';
  };
  const onItemClick = async (type: Action) => {
    if (onClick && !(await onClick(type))) {
      return;
    }

    switch (type) {
      case 'UserSetting':
        history.push('/setting');
        break;
      case 'UserCenter':
        history.push('/center');
        break;
      case 'SignOut':
        //TODO
        Modal.confirm({
          title: formatMessage('登出'),
          icon: <ExclamationCircleOutlined />,
          content: formatMessage('确定退出登录?'),
          okText: formatMessage('确定'),
          cancelText: formatMessage('取消'),
          onOk: () => {
            queryLoginUser().then((usersInfo) => {
              if (usersInfo?.data?.userType == 'Inner') {
                myMsal
                  .logoutPopup({
                    mainWindowRedirectUri: 'https://pto.lindemobile.cn/#/logout',
                  })
                  .then((res) => {
                    console.log(res);
                  });
              } else {
                const Mgrs = new Mgr();
                Mgrs.signOut();
              }
            });
          },
          centered: true,
        });
        break;
      default:
        break;
    }
  };
  return {
    formatUserName,
    formatMessage,
    onItemClick,
  };
};

export default usePersonalDropdownService;
