import { Table } from '@/components/ProTable/style';
import { ProTableProps } from '@ant-design/pro-table';
import { useTableHeight } from '@/components/ProTable/useProTableService';
import React from 'react';

export type MedalsoftTableProps<T, U> = ProTableProps<T, U> & {
  /**是否一屏展示 */
  unFullScreenTable?: boolean;
};

/**
 * 封装了一屏展示功能的ProTable
 * @extends ProTable
 * @param {MedalsoftTableProps} props
 * @param {*} U
 * @return {*}
 * @author: Phoebe.Lv
 */
export function MedalsoftTable<T, U>(props: MedalsoftTableProps<T, U>) {
  const tableHeight = useTableHeight();

  return (
    <Table
      tableAlertRender={false}
      options={false}
      rowKey={'id'}
      sticky={true}
      data-height={props.unFullScreenTable ? 'auto' : tableHeight}
      scroll={
        props.unFullScreenTable
          ? {}
          : { scrollToFirstRowOnChange: true, y: 450 }
      }
      pagination={{
        pageSize: 10,
        showSizeChanger: false,
      }}
      search={{
        defaultCollapsed: false,
        collapseRender: false,
      }}
      {...props}
    />
  );
}

export default MedalsoftTable;
