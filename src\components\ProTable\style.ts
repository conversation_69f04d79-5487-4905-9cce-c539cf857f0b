import styled from 'styled-components';

import ProTable from '@ant-design/pro-table';

export const TableWrapper = styled.div`
  height: 100%;
  width: 100%;
`;

export const Table = styled(ProTable)`
  height: 100%;
  ${(props) => !props.unFullScreenTable && FullScreenTable};
  .ant-form-item-label > label {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;

const FullScreenTable = styled(ProTable)`
  .ant-card {
    height: ${(props) => props['data-height']};
    .ant-card-body {
      height: 100%;
      padding: 0 24px 16px !important;
      .ant-pro-table-list-toolbar .ant-pro-table-list-toolbar-container {
        padding: 16px 0 0 0 !important;
      }
      .ant-table-wrapper {
        margin-top: 16px;
        height: calc(100% - 64px - 16px * 2);
        .ant-spin-nested-loading,
        .ant-spin-container {
          height: 100%;
          .ant-table {
            height: calc(100% - 24px - 16px);
            .ant-table-container {
              height: 100%;
              .ant-table-body {
                overflow-y: auto;
                max-height: calc(100% - 47px) !important;
                overflow-x: hidden;
              }
              .ant-table-sticky-scroll {
                display: none;
              }
            }
          }
        }
      }
    }
  }
`;

export const Title = styled.a`
  cursor: pointer;
  span {
    color: ${(props) => props.theme.aColor};
  }
`;

export const TextAlign = styled.div`
  text-align: center;
`;

// export const Tags = styled.div`
//   width: 100%;
//   overflow: hidden;
//   a {
//     display: block;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//     overflow: hidden;
//     cursor: unset;
//     color: #000;
//   }
// `;
