export interface PropTableResponse<T> {
  total?: number;
  success: boolean;
  data: T | [];
}

interface IPorTablePromiseArgs {
  url: string;
  params?: any;
  headers?: any;
}
interface IPorTablePromiseAction {
  successCallback?: Function;
  failureCallback?: Function;
}

export type IUsePorTablePromise = <T>(
  args: IPorTablePromiseArgs,
  aciton: IPorTablePromiseAction,
) => Promise<PropTableResponse<T>>;

export function useTableHeight() {
  const SearchAreaHeight = document.getElementsByClassName(
    'ant-pro-table-search',
  )[0]?.clientHeight;

  return `calc(100% - ${
    SearchAreaHeight ? SearchAreaHeight + 'px' : '0px'
  } - 16px) !important`;
}
