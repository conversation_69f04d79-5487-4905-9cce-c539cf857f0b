export const showOptionLabel = (classList, key) => {
  let filterObj = classList.filter((item) => item.value == key);
  return filterObj.length > 0 ? filterObj[0].label : '';
};

export const classList = [
  { label: '被退回', value: '-2', key: '-2' },
  { label: '对比失败', value: '-1', key: '-1' },
  { label: '草稿', value: '0', key: '0' },
  { label: '待比对', value: '1', key: '1' },
  { label: '已结算', value: '2', key: '2' },
  { label: '待生成格式PO', value: '3', key: '3' },
  { label: 'MTOP待确认', value: '4', key: '4' },
  { label: '待生成PR', value: '5', key: '5' },
  { label: '待生成PO', value: '6', key: '6' },
  { label: '待生成GR', value: '7', key: '7' },
];
export const classData = [
  { label: '被退回', value: '-2', key: '-2' },
  { label: '对比失败', value: '-1', key: '-1' },
  { label: '草稿', value: '0', key: '0' },
  { label: '待比对', value: '1', key: '1' },
  { label: '已结算', value: '2', key: '2' },
  { label: '待生成格式PO', value: '3', key: '3' },
  { label: 'MTOP待确认', value: '4', key: '4' },
  { label: '待结算', value: '5', key: '5' },
  { label: '待生成PO', value: '6', key: '6' },
  { label: '待生成GR', value: '7', key: '7' },
];
//采购状态
export const PoInvokeStatus = [
  { label: '同步失败', value: '-1', key: '-1' },
  { label: '预览状态', value: '0', key: '0' },
  { label: '待发送', value: '1', key: '1' },
  { label: '已同步', value: '2', key: '2' },
  { label: '已发送', value: '3', key: '3' },
  { label: 'po成功gr未成功', value: '4', key: '4' },
];
//SoInvoke状态
export const SoInvokeStatus = [
  { label: '同步失败', value: '-1', key: '-1' },
  { label: '已同步', value: '2', key: '2' },
  { label: '待发送', value: '1', key: '1' },
  { label: '已发送', value: '3', key: '3' },
  { label: '待发送', value: null, key: null },
];
//供应商数据
export const chartData = [
  { label: '总条数', value: 'totalCount', key: 'totalCount' },
  { label: '总退回条数', value: 'returnCount', key: 'returnCount' },
  { label: '退回次数（数量）', value: 'quantityReturnTimes', key: 'quantityReturnTimes' },
  { label: '退回次数（价格）', value: 'priceReturnTimes', key: 'priceReturnTimes' },
];
//车队数据
export const fleetData = [
  { label: '总条数', value: 'totalCount', key: 'totalCount' },
  { label: 'KPI失败条数', value: 'kpiFailedCount', key: 'kpiFailedCount' },
  { label: '总次数', value: 'totalTimes', key: 'totalTimes' },
  { label: 'KPI失败次数', value: 'kpiFailedTimes', key: 'kpiFailedTimes' },
];
//PM/采购数据
export const procurData = [
  { label: '总条数', value: 'totalCount', key: 'totalCount' },
  { label: '价格失败条数', value: 'failedCount', key: 'failedCount' },
  { label: '价格失败次数', value: 'failedTimes', key: 'failedTimes' },
];
export const analysisData = [
  { label: '比对失败和待比对条目条数', value: 'toCompareAndFailCount', key: 'toCompareAndFailCount' },
  { label: '待生成格式PO条数', value: 'toBeFormatPOCount', key: 'toBeFormatPOCount' },
  { label: '待结算条数', value: 'toBeSettlCount', key: 'toBeSettlCount' },
  { label: '已结算条数', value: 'settlCount', key: 'settlCount' },
  { label: '比对失败和待比对条目数量', value: 'toCompareAndFailQuantity', key: 'toCompareAndFailQuantity' },
  { label: '待生成格式PO数量', value: 'toBeFormatPOQuantity', key: 'toBeFormatPOQuantity' },
  { label: '待结算数量', value: 'toBeSettlQuantity', key: 'toBeSettlQuantity' },
  { label: '已结算数量', value: 'settlQuantity', key: 'settlQuantity' },
];
export const analysis = [
  { label: '比对失败和待比对条目条数', value: 'toCompareAndFailCount', key: 'toCompareAndFailCount', color: '#ff4d4f' },
  { label: '待生成格式PO条数', value: 'toBeFormatPOCount', key: 'toBeFormatPOCount', color: '#ff4d4f' },
  { label: '待结算条数', value: 'toBeSettlCount', key: 'toBeSettlCount', color: '#40a9ff' },
  { label: '已结算条数', value: 'settlCount', key: 'settlCount', color: '#40a9ff' },
];

export const color = [
  { label: '#F57878', value: 'toCompareAndFailCount', key: 'toCompareAndFailCount' },
  { label: '#D29DFF', value: 'toBeFormatPOCount', key: 'toBeFormatPOCount' },
  { label: '#81C9FF', value: 'toBeSettlCount', key: 'toBeSettlCount' },
  { label: '#6ED8B9', value: 'settlCount', key: 'settlCount' },
  { label: '#FEA657', value: 'toCompareAndFailQuantity', key: 'toCompareAndFailQuantity' },
  { label: '#D2D86E', value: 'toBeFormatPOQuantity', key: 'toBeFormatPOQuantity' },
  { label: '#FFA0D3', value: 'toBeSettlQuantity', key: 'toBeSettlQuantity' },
  { label: '#9CBE71', value: 'settlQuantity', key: 'settlQuantity' },
];

/**
 * @param {*} data   [后台数据]
 * @param {*} key    [要合并的字段]
 * @param {*} target [后台数据对应的index]
 * @returns 合并的行数
 * method of 获取合并的行数
 */
export const getRowSpanCount = (data, key, target) => {
  if (!Array.isArray(data)) return 1;
  data = data.map((_) => _[key]); // 只取出筛选项
  let preValue = data[0];
  const res = [[preValue]]; // 放进二维数组里
  let index = 0; // 二维数组下标
  for (let i = 1; i < data.length; i++) {
    if (data[i] === preValue) {
      // 相同放进二维数组
      res[index].push(data[i]);
    } else {
      // 不相同二维数组下标后移
      index += 1;
      res[index] = [];
      res[index].push(data[i]);
      preValue = data[i];
    }
  }
  const arr = [];
  res.forEach((_) => {
    const len = _.length;
    for (let i = 0; i < len; i++) {
      arr.push(i === 0 ? len : 0);
    }
  });
  return arr[target];
};
