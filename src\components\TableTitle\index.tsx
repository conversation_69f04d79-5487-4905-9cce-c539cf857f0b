import React from 'react';
import { TableTitleDiv, TaleTitleIconDiv } from '@/assets/style/list';

export default (props: any) => {
  const { icon = true, title, right } = props;
  return (
    <TableTitleDiv>
      <div>
        {icon && (
          <TaleTitleIconDiv>
            <span></span>
            <span></span>
          </TaleTitleIconDiv>
        )}
        <span style={{ verticalAlign: 'middle' }}>{title}</span>
      </div>
      <div>{right}</div>
    </TableTitleDiv>
  );
};
