import { useRef, useState } from 'react';
import { message, Modal } from 'antd';
import { UseAxiosResponse } from '@/app/request';
import type { ActionType } from '@ant-design/pro-table';

import Loading from '@/components/Loading';

export type IModal = {
  visible: boolean;
  dataSource: any;
  status: 'Blank' | 'Edit' | 'Preview';
};

export interface IProps {
  formatMessage: Function;
}
export type TableWithModalService = ReturnType<typeof useTableWithModalService>;

export default function useTableWithModalService<T extends IModal, K>(
  props: IProps,
) {
  const { formatMessage } = props;

  const [dataSource, setDataSource] = useState([]);
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [modal, setModal] = useState<T | IModal>({
    visible: false,
    dataSource: {},
    status: 'Blank',
  });

  /**
   * 新增
   */
  const onAdd = () => {
    setModal({
      visible: true,
      status: 'Blank',
      dataSource: {},
    });
  };

  /**
   * 预览
   */
  const onPreview = (record) => {
    console.log('预览', record);
    setModal({
      visible: true,
      status: 'Preview',
      dataSource: record,
    });
  };

  /**
   * 编辑
   */
  const onEdit = (record) => {
    console.log('编辑', record);
    setModal({
      visible: true,
      status: 'Edit',
      dataSource: record,
    });
  };

  /**
   * 删除（包含二次确认提示框）
   * @param ids 数据IDs
   * @param getIllegalData 计算非法数据逻辑
   * @param okPromise 删除函数
   */

  const onDelete = (
    ids: Array<Number | String>,
    okPromise: () => Promise<UseAxiosResponse<K>>,
    getIllegalData?: () => any[],
  ) => {
    if (!ids?.length) {
      message.warning(formatMessage('请选择要删除的数据'));
      return;
    }

    // 检查是否有不能删除的数据
    if (getIllegalData) {
      const illegalData = getIllegalData();
      if (illegalData.length) {
        message.warning(formatMessage('请选择要删除的正确数据'));
        return;
      }
    }

    Modal.confirm({
      centered: true,
      cancelText: formatMessage('取消'),
      okText: formatMessage('确认'),
      title: formatMessage('提示'),
      content: formatMessage('请确认是否要删除数据'),
      onCancel: (close) => {
        close();
      },
      onOk: async () => {
        console.log('删除', ids);
        Loading.show();
        let request = await okPromise();
        Loading.hide();
        console.log('onDelete', request);
        if (request.success) {
          actionRef.current.reload();
          message.success(formatMessage('删除成功'));
        }
      },
    });
  };

  return {
    dataSource,
    setDataSource,
    selectedRowKeys,
    setSelectedRowKeys,
    actionRef,
    modal,
    setModal,
    onAdd,
    onPreview,
    onEdit,
    onDelete,
  };
}
