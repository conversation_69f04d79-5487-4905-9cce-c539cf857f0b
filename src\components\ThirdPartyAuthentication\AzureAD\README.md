### AAD使用说明

**资料**
[@azure/msal-react文档](https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-react/docs/getting-started.md)



**常用登录方式**

AAD组件默认回调地址为:`${location.origin}${location.pathname}`

> umi框架本地调试：`http://localhost:8000`，端口根据项目配置
>
> 建议将azure回调地址设定为与框架默认调试地址一致，只到端口，即`http://localhost:8000`



以下说明均以azure回调地址设定为与框架默认调试地址一致为前提
- 无登录界面 + 微软跳转认证
  - layout注入AAD组件拦截，未认证则执行`aadService.login()`跳转登录页
  - 登录成功后跳回系统，认证判断通过进入系统
- 登录界面 + 微软跳转认证
  - layout注入AAD组件拦截，未认证则跳转login路由
    - 判断如果当前已经是login不进行跳转，避免死循环
  - 登录页点击登录调用`aadService.login()`进行跳转登录
  - 认证成功则根据当前页面路由加载页面
- 无登录界面 + popup登录
  - layout注入AAD组件拦截，未认证则执行`aadService.login()`进行popup登录
  - 登录过程通过`aadService.status === 'login'`判断当前是否正在登录中，显示等待界面
    - `aadService.status`类型为`InteractionStatus`，由`msal-react`提供
  - 登录完成组件自动重新render，通过判断进入系统
  
  


**顶层注入初始化**
```tsx
import { AzureAD } from "@/components/ThirdPartyAuthentication/AzureAD";
import { IAzureADService } from "@/components/ThirdPartyAuthentication/AzureAD/useAzureADService";

export const App = () => {
    return (
        <AzureAD
        scope=""
        clientId=""
        tenantId=""
        >
        {(aadService: IAzureADService) => {
            return (
            <div>
                {!aadService.isAuthenticated && (
                <button onClick={aadService.login}>login</button>
                )}
                {aadService.account}
                {aadService.accessToken}
                {aadService.status}
            </div>
            );
        }}
        </AzureAD>
    );
  }
```

**底层组件读取**

```tsx
import { AzureADService } from '@/components/ThirdPartyAuthentication/AzureAD/useAzureADService';
const App = () => {
  const aadService = useContext(AzureADService);
  return (
    <div>
      {!aadService.isAuthenticated && (
        <button onClick={aadService.login}>login</button>
      )}
      {aadService.account}
      {aadService.accessToken}
      {aadService.status}
    </div>
  );
};

```

**react框架外读取token**

需先初始化
```ts
import { acquireAccessToken } from '@/components/ThirdPartyAuthentication/AzureAD/useAzureADService';

let token = await acquireAccessToken();

```