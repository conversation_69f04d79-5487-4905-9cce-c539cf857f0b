import React from 'react';
import { IRouteComponentProps } from 'umi';
import { AzureAD } from '@/components/ThirdPartyAuthentication/AzureAD';
import { IAzureADService } from '@/components/ThirdPartyAuthentication/AzureAD/useAzureADService';
import { history as umiHistory } from 'umi';
import { InteractionStatus, InteractionType } from '@azure/msal-browser';

/** 有登录页示例 */
export function LayoutWithLogin({
  children,
  location,
  route,
  history,
  match,
}: IRouteComponentProps) {
  return (
    <AzureAD
      scope="api://87987862-b622-4e1b-81eb-4f99c99916ff/default"
      clientId="87987862-b622-4e1b-81eb-4f99c99916ff"
      tenantId="44c24f42-d49b-4192-9336-5f2989b87356"
      loginType={InteractionType.Redirect}
      redirectUri={'http://localhost:8001'}
    >
      {(aadService: IAzureADService) => {
        if (
          !aadService.isAuthenticated &&
          aadService.status === InteractionStatus.None
        ) {
          if (!history || history.location.pathname.indexOf('login') == -1) {
            umiHistory.push('/login');
          }
        }
        if (
          aadService.isAuthenticated &&
          history?.location?.pathname === '/login'
        ) {
          umiHistory.push('/admin');
        }
        return children;
      }}
    </AzureAD>
  );
}

/** 无登录页示例 */
export function Layout({
  children,
  location,
  route,
  history,
  match,
}: IRouteComponentProps) {
  return (
    <AzureAD
      scope="api://87987862-b622-4e1b-81eb-4f99c99916ff/default"
      clientId="87987862-b622-4e1b-81eb-4f99c99916ff"
      tenantId="44c24f42-d49b-4192-9336-5f2989b87356"
      loginType={InteractionType.Redirect}
      redirectUri={'http://localhost:8001'}
    >
      {(aadService: IAzureADService) => {
        if (
          !aadService.isAuthenticated &&
          aadService.status === InteractionStatus.None
        ) {
          aadService.login();
          return <Loading />;
        }
        if (
          !aadService.isAuthenticated &&
          (aadService.status === InteractionStatus.Login ||
            aadService.status === InteractionStatus.Startup ||
            aadService.status === InteractionStatus.HandleRedirect)
        ) {
          return <Loading />;
        }
        return children;
      }}
    </AzureAD>
  );
}
