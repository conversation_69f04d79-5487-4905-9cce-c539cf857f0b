import React, { useContext } from 'react';
import { AzureADService } from '@/components/ThirdPartyAuthentication/AzureAD/useAzureADService';
const Index = () => {
  const aadService = useContext(AzureADService);
  return (
    <div>
      {!aadService.isAuthenticated && (
        <button onClick={() => aadService.login()}>登录</button>
      )}

      {aadService.isAuthenticated && (
        <button onClick={() => aadService.msalInstance.logout()}>
          退出登录
        </button>
      )}
      <br />
      <span style={{ color: 'red' }}>账号信息:</span>
      {JSON.stringify(aadService.account)}
      <br />
      <br />
      <span style={{ color: 'red' }}>AccessToken:</span>
      {aadService.accessToken}
      <br />
      <br />
      <span style={{ color: 'red' }}>Status:</span>
      {aadService.status}
    </div>
  );
};
export default Index;
