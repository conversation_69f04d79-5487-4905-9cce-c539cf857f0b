import { theme } from '@/app/config/theme';
import useAuthService, { AuthService } from '@/modules/User/useAuthService';
import useFormatLanguageService, { FormatLanguageService } from '@/tools/formatLanguage';
import { UseRequestProvider } from 'ahooks';
import 'antd/dist/antd.less';
import axios from 'axios';
import React from 'react';
import { createGlobalStyle, ThemeProvider } from 'styled-components';
import { IRouteComponentProps } from 'umi';
import { AzureAD } from '@/components/ThirdPartyAuthentication/AzureAD';
import { IAzureADService } from '@/components/ThirdPartyAuthentication/AzureAD/useAzureADService';
import { InteractionStatus, InteractionType } from '@azure/msal-browser';
import { history as umiHistory } from 'umi';
import { queryLoginUser } from '@/app/request/requestApi';
import { Modal } from 'antd';
import { PublicClientApplication } from '@azure/msal-browser';

const GlobalStyle = createGlobalStyle`
 /**
    全局样式
   */

  ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      background-color: #f5f5f5;
  }
  ::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      background-color: #f5f5f5;
  }
  ::-webkit-scrollbar-thumb {
      border-radius: 4px;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      background-color: #c1c1c1;
  }
  /* 提升dropdown弹出框的显示优先级 */
  .ant-dropdown{
    z-index: 9999;
  }
  body{
    font-family: Microsoft Yahei;
    background-color:#ececec;
  }
  a,a:hover{
    color: #035393;
  }
  .ant-layout-header{
    height: 60px !important;
  }
  .ant-layout-sider.ant-layout-sider-light.ant-pro-sider.ant-pro-sider-fixed.ant-pro-sider-layout-mix.ant-pro-sider-light{
    padding-top: 60px !important;
  }
  .ant-menu-submenu-title{
    font-size: 16px;
    font-weight: bold;
  }
  .ant-menu-sub.ant-menu-inline > .ant-menu-item{
    padding-left: 48px !important;
  }
  .ant-layout-sider-children{
    padding: 20px 15px;
  }
  .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected{
    background-color: #ecf6f8;
  }
  .ant-menu-sub.ant-menu-inline{
    background-color: transparent;
  }
  .ant-menu-item:hover, .ant-menu-item-active, .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open, .ant-menu-submenu-active, .ant-menu-submenu-title:hover,.ant-menu-submenu-selected,.ant-menu-submenu:hover > .ant-menu-submenu-title > .ant-menu-submenu-expand-icon, .ant-menu-submenu:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow{
    color: #333;
  }
  .ant-pro-sider.ant-layout-sider-light .ant-menu-item-selected a, .ant-pro-sider.ant-layout-sider-light .ant-menu-item a:hover,.ant-menu-item-selected{
    color: #035393;
    font-weight: bold;
  }
  .ant-menu-inline .ant-menu-item::after {
    border-right: none;
  }
  .ant-pro-basicLayout-content,.ant-pro-page-container-children-content,.ant-pro-basicLayout-content .ant-pro-page-container {
    margin: 0;
  }
  .ant-pro-page-container,.ant-pro-grid-content,.ant-pro-grid-content-children,.ant-pro-page-container-children-content{
    height: 100%;
  }
  .ant-pro-page-container-warp{
    background: transparent;
  }
  .ant-page-header-heading{
    justify-content: center;
  }
  .ant-page-header-heading-title{
    color: rgba(181, 181, 182, .85);
    letter-spacing: 3px;
    font-size: 26px;
  }
  .ant-page-header{
    background: transparent;
    padding: 35px 25px 10px;
  }
  .ant-collapse-content > .ant-collapse-content-box{
    padding: 0 !important;
  }
  .ant-collapse > .ant-collapse-item > .ant-collapse-header{
    font-size: 16px;
    padding-bottom: 8px;
    font-weight: bold;
    color: #333;
  }
  .ant-btn,.ant-btn:hover,.ant-btn:active,.ant-btn:focus{
    border-radius: 5px;
    border-color: #035393;
    color: #035393;
  }
  .ant-btn-primary,.ant-btn-primary:hover,.ant-btn-primary:active,.ant-btn-primary:focus{
    color: #fff;
    background: #035393;
    border-color: #035393;
    border-radius: 5px;
  }
  .ant-btn-link{
    background: transparent;
    border:none;
  }
  .ant-table-thead > tr > th{
    background: #eaf4f6;
    font-size:16px;
    font-weight:bold;
  }
  .ant-table-container table > thead > tr:first-child th:last-child{
    border-bottom-right-radius:5px;
  }
  .ant-table-container table > thead > tr:first-child th:first-child{
    border-bottom-left-radius:5px;
  }
  // .ant-table-tbody > tr.ant-table-row-selected > td,.ant-table-tbody > tr.ant-table-row-selected:hover > td{
  //   background-color: transparent;
  // }
  .ant-input,.ant-input-number,.ant-select-selector,.ant-picker{
    border-radius: 5px!important;
  }
  .ant-input[disabled],.ant-select-disabled.ant-select-multiple .ant-select-selection-item,.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector,.ant-input-number-disabled .ant-input-number-input,.ant-input-number-disabled,.ant-picker.ant-picker-disabled,.ant-picker-input > input[disabled]{
    color: #333;
    cursor: default;
  }
  .ant-input:hover,.ant-input:focus, .ant-input-focused,.ant-select:not(.ant-select-disabled):hover .ant-select-selector,.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector,.ant-picker:not(.ant-picker-disabled):hover, .ant-picker-focused,.ant-input-number:not(.ant-input-number-disabled):hover,.ant-input-number:focus, .ant-input-number-focused,.ant-input-search .ant-input:hover, .ant-input-search .ant-input:focus{
    border-color: #035393;
  }
  .ant-input-group > .ant-input:first-child, .ant-input-group-addon:first-child{
    border-top-right-radius: 0!important;
    border-bottom-right-radius: 0!important;
  }
  .ant-input-search > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary){
    border-color: #d9d9d9;
  }
  .ant-picker-range .ant-picker-active-bar{
    background: #035393;
  }
  .ant-pagination-item-active{
    border-color: #035393;
  }
  .ant-pagination-item-active a{
    color: #035393;
  }
`;

export default function Layout({ children, location, route, history, match }: IRouteComponentProps) {
  const formatLanguageService = useFormatLanguageService();
  // const authService = useAuthService({ mode: 'normal', formatLanguageService });

  const config = {
    auth: {
      clientId: process.env.CLIENT_ID,
      redirectUri: process.env.REDIRECT_URL,
      postLogoutRedirectUri: process.env.WEB_URL,
    },
  };

  const myMsal = new PublicClientApplication(config);

  return (
    <ThemeProvider theme={theme}>
      {/* 全局样式 */}
      <GlobalStyle />
      <FormatLanguageService.Provider value={formatLanguageService}>
        <UseRequestProvider
          value={{
            requestMethod: (param) =>
              axios(param).then((r) => {
                return r.data;
              }),
          }}
        >
          {children}
        </UseRequestProvider>
      </FormatLanguageService.Provider>
    </ThemeProvider>
  );
}
