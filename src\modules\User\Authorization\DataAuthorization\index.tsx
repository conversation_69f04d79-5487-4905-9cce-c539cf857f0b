import { MedalsoftDataAuth } from '@/components/Authorization';
import Config from '@/app/config';
import { FormatLanguageService } from '@/tools/formatLanguage';
import React, { useContext } from 'react';
import {
  useProTablePromiseByPost,
  useProTablePromiseByGet,
} from '@/app/request/tableRequest';

const FunctionalAuthorization = () => {
  const { formatMessage } = useContext(FormatLanguageService);
  return (
    <MedalsoftDataAuth
      formatMessage={formatMessage}
      baseAPi={Config.Api.Base}
      messagePrefix={''}
      useProTablePromiseByGet={useProTablePromiseByGet}
      useProTablePromiseByPost={useProTablePromiseByPost}
    />
  );
};
export default FunctionalAuthorization;
