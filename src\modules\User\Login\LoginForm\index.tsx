import { FormatLanguageService } from '@/tools/formatLanguage';
import { Form, Input } from 'antd';
import React, { useContext } from 'react';
import { FormButton, FormItem } from './style';
import useLoginFormService from './useLoginFormService';

export default function LoginForm() {
  const { formatMessage } = useContext(FormatLanguageService);
  const { form, handleSubmit, isShowImageCode, setIsShowImageCode, login } =
    useLoginFormService();
  return (
    <Form layout="vertical" form={form}>
      <FormItem name="email">
        <Input
          placeholder={formatMessage('账号')}
          spellCheck={false}
          autoComplete="off"
        />
      </FormItem>

      <FormItem name="password">
        <Input
          placeholder={formatMessage('密码')}
          type="password"
          onKeyDown={login}
          autoComplete="off"
        />
      </FormItem>

      <FormItem>
        <FormButton
          type="primary"
          // htmlType="submit"
          onClick={login}
          block
          // loading={loginFormService.loading}
        >
          {formatMessage('登录')}
        </FormButton>
      </FormItem>
    </Form>
  );
}
