import { Card, Form, Button } from 'antd';
import React, { PropsWithChildren } from 'react';
import styled from 'styled-components';
import LoginBackGround from '../../../assets/images/LoginBackGround.png';

export const OtherMethods = styled.div`
  width: 100%;
  text-align: center;
  font-size: 0.8rem;
  line-height: 20px;
  font-family: Myriad Pro;
  color: #878a90;
  margin-top: 2vh;
`;

export const ForgotPSW = styled.a`
  font-size: 0.8rem;
  font-family: Myriad Pro;
  color: #878a90;
`;

export const FormItem = styled(Form.Item)`
  margin-bottom: 2vh;
`;

export const FormButton = styled(Button)`
  background: rgba(220, 221, 221, 1);
  border-color: rgba(220, 221, 221, 1);
  color: rgba(114, 113, 113, 1);
  :hover {
    background: rgba(220, 221, 221, 1);
    border-color: rgba(220, 221, 221, 1);
    color: rgba(114, 113, 113, 1);
  }
  :focus {
    background: rgba(220, 221, 221, 1);
    border-color: rgba(220, 221, 221, 1);
    color: rgba(114, 113, 113, 1);
  }
`;
export const ImageCodeContainer = styled.div`
  width: 500px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
  background: white;
  box-shadow: 0 2px 10px 0 #333;
  :hover {
    cursor: pointer;
  }
`;
export const Close = styled.div`
  text-align: right;
  font-size: 15px;
  font-weight: 800;
  margin-right: 20px;
`;
