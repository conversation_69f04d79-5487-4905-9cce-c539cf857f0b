import { TokenEncrypt } from '@/app/token/token-encrypt';
import { Form } from 'antd';
import { useState } from 'react';
import useLoginService from '../useLoginService';

export default function useLoginFormService() {
  // 注入上层服务
  const [isShowImageCode, setIsShowImageCode] = useState(false);
  const { normalLogin } = useLoginService();
  // 表格
  const [form] = Form.useForm();
  // 请求
  const handleSubmit = async () => {
    normalLogin(
      TokenEncrypt.encryptObject(
        {
          account: 'ABC12345',
          password: 'By2Test1!',
          clientType: 1,
        },
        ['password'],
      ),
      '/admin',
    );
  };
  const login = async (e) => {
    if (e.type === 'click' || (e.type === 'keydown' && e.keyCode === 13)) {
      const check = await form.validateFields(['email', 'password']);
      handleSubmit();
    }
  };

  return {
    form,
    handleSubmit,
    setIsShowImageCode,
    isShowImageCode,
    login,
  };
}
