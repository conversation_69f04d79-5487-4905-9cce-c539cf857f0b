import { Card } from 'antd';
import React, { PropsWithChildren } from 'react';
import styled from 'styled-components';

const Container = styled.div`
  background-size: 100% 100%;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.08);
`;

const Wrapper = styled(Card)`
  position: relative;
  width: 30vw;
  /* height: 60vh; */
  max-height: 605px;
  min-height: 400px;
  display: fixed;
  left: 10vw;
  top: 25vh;
  padding: 2vh 1vw;
  box-shadow: 0px 5px 20px 5px rgba(55, 53, 55, 0.06);
  border-radius: 10px;
  .ant-card-body {
    width: 100%;
    height: 100%;
  }
`;
const Logo = styled.img`
  height: 110px;
  position: fixed;
  left: 60%;
  top: 20%;
`;
const FormTitle = styled.p`
  color: #373537;
  font-size: 1.2rem;
  font-weight: 500;
`;

export default function LoginWrapper(props: PropsWithChildren<{}>) {
  return (
    <Container>
      <Wrapper>
        <FormTitle>login</FormTitle>
        {props.children}
      </Wrapper>
    </Container>
  );
}
