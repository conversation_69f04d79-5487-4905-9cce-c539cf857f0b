import { IProps, SocialMediaTypes } from './typing';

const SocialMediaHref = (props: IProps) => {
  let state: SocialMediaTypes;
  let href;
  let scope;
  let response_type;
  let access_type;
  switch (props.socialMediaType) {
    case 'Apple':
      break;
    case 'Facebook':
      state = 'Facebook';
      href = `https://www.facebook.com/v10.0/dialog/oauth?state=${state}&client_id=${props.clientId}&redirect_uri=${props.redirectUrl}`;
      return href;
    case 'Linkedin':
      state = 'Linkedin';
      scope = 'r_emailaddress,r_liteprofile';
      response_type = 'code';
      href = `https://www.linkedin.com/uas/oauth2/authorization?response_type=${response_type}&scope=${scope}&state=${state}&redirect_uri=${props.redirectUrl}&client_id=${props.clientId}`;
      return href;
    case 'Twitter':
      break;
    case 'Wechat':
      response_type = 'code';
      scope = 'snsapi_login';
      state = 'Wechat';
      href = `https://open.weixin.qq.com/connect/qrconnect?appid=${props.clientId}&redirect_uri=${props.redirectUrl}&response_type=${response_type}&scope=${scope}&state=${state}#wechat_redirect`;
      return href;
    case 'Youtube':
      access_type = 'offline';
      scope = 'openid';
      response_type = 'code';
      state = 'Youtube';
      href = `https://accounts.google.com/o/oauth2/v2/auth?access_type=${access_type}&scope=${scope}&include_granted_scopes=true&response_type=${response_type}&state=${state}&redirect_uri=${props.redirectUrl}&client_id=${props.clientId}`;
      return href;

    default:
      break;
  }
};
export default SocialMediaHref;
