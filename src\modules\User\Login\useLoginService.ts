import Config from '@/app/config';
import { usePost } from '@/app/request';
import { message } from 'antd';
import { useContext } from 'react';
import { useHistory } from 'umi';
import { AuthService } from '../useAuthService';

const useLoginService = () => {
  const history = useHistory();
  // const { setToken } = useContext(AuthService);

  /**
   * 普通登录方法
   * @param formData 提交的表单数据
   * @param homeRoute 登录成功后跳转的路由
   */
  const normalLogin = async (formData: object, homeRoute) => {
    // const res = await usePost(
    //   `${Config.Api.Base}${Config.Api.Login}`,
    //   formData,
    //   {
    //     autoLoading: true,
    //   },
    // );
    // if (res.success && res.data) {
    //   setToken(res.data);
    //   message.success('登陆成功');
    //   history.replace(homeRoute);
    // } else {
    //   message.error(res.msg);
    // }
  };
  /**
   * 第三方登录方法
   */
  enum SocialMediaEnum {
    Youtube = 0,
    Linkedin = 1,
    Facebook = 2,
    Twitter = 3,
    Wechat = 4,
    Appl = 5,
  }
  /**
   *
   * @param homeRoute 登录成功后跳转的路由
   * @param Token 第三方应用颁发的token
   * @param Type 第三方应用的类型
   * @param UserTag 第三方应用记录的用户身份唯一标识
   */
  const socialLogin = async (
    homeRoute: string,
    Token: string,
    Type: keyof typeof SocialMediaEnum,
    UserTag?: string,
  ) => {
    // const res = await usePost(
    //   `${Config.Api.Base}${Config.Api.SocialLogin}`,
    //   {
    //     UserTag,
    //     Token,
    //     Type: SocialMediaEnum[Type],
    //     // 客户端类型：1:web,2:App
    //     ClientType: 1,
    //   },
    //   {
    //     autoLoading: true,
    //   },
    // );
    // if (res.success && res.data) {
    //   setToken(res.data);
    //   history.replace(homeRoute);
    // } else {
    //   message.error(res.msg);
    // }
  };
  return { normalLogin, socialLogin, SocialMediaEnum };
};
export default useLoginService;
