import Config from '@/app/config';
import { useGet } from '@/app/request';
import Loading from '@/components/Loading';
import { IFormatLanguageService } from '@/tools/formatLanguage';
import { message } from 'antd';
import { useState } from 'react';

// 功能权限枚举
enum AuthCode {}

type IProps = {
  formatLanguageService: IFormatLanguageService;
};

export const useAuthCodeService = (props: IProps) => {
  const { formatMessage } = props.formatLanguageService;
  const [authCode, setAuthCode] = useState({});
  /**
   * 获取账号对应的功能权限
   */
  const initAuthCode = async () => {
    try {
      const res = await useGet<string[]>(
        `${Config.Api.Base}${Config.Api.AuthCodesByAccount}`,
      );
      if (res.success) {
        if (!res.data?.length) {
          message.warning(
            formatMessage('该账号暂无权限，请联系高级管理员配置！'),
          );
        } else {
          let userAuth = {};
          res.data.map((code) => {
            userAuth[code] = code;
          });
          setAuthCode(userAuth);
        }
      } else {
        console.error(formatMessage('获取账号对应的功能权限异常'), res.msg);
      }
    } catch (error) {
      console.error(formatMessage('获取账号对应的功能权限异常'), error);
    }
  };

  /**
   * 检查是否拥有数据权限
   * @returns boolean
   */
  const verificaAuth = (code: AuthCode | AuthCode[]) =>
    (Array.isArray(code) ? code : [code])?.some((c) => authCode[c]);

  /**
   * 多个组件同时赋予权限
   */
  const wrapAuth = (
    code: AuthCode[] | AuthCode,
    ComposedComponent: React.ReactNode,
  ) => (verificaAuth(code) ? ComposedComponent : null);

  return {
    wrapAuth,
    verificaAuth,
    initAuthCode,
  };
};

export default useAuthCodeService;
