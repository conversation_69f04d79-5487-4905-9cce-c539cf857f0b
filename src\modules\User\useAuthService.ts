import Config from '@/app/config';
import { UseAxiosResponse, usePost } from '@/app/request';
import { BasePayload, Expired } from '@/app/token/token-type';
import { BaseJWTContext, useJWTService } from '@/app/useBaseTokenService';
import Loading from '@/components/Loading';
import { IFormatLanguageService } from '@/tools/formatLanguage';
import getFutureTime from '@/tools/getFutureTime';
import getServiceContext from '@/tools/getServiceContext';
import { sp } from '@pnp/sp';
import { ISiteUserInfo } from '@pnp/sp/site-users/types';
import '@pnp/sp/site-users/web';
import '@pnp/sp/webs';
import { useUpdate } from 'ahooks';
import { message } from 'antd';
import { useRef } from 'react';
import { history } from 'umi';
import { useAuthCodeService } from './useAuthCodeService';

const ERROR_MSG_KEY = 'ErrorMsg';
const LOGIN_ROUTE = '/home';
const AUTH_WHITELIST = ['/download', '/404', '/', LOGIN_ROUTE];
const MAX_TIMEOUT = 2147483647;
const MAX_RETRY_COUNT = 3;

sp.setup({
  sp: {
    baseUrl: Config.SPUrl,
    headers: { ACCEPT: '*/*' },
  },
});

/**
 * 跳转到登录页
 */
const redirectToLogin = () => {
  if (history.location.pathname === LOGIN_ROUTE) {
    return;
  }
  history.push(LOGIN_ROUTE);
};

const isWhiteList = (whiteList) =>
  whiteList.some((i) => history.location.pathname === i);

const isTokenValid = (token, timeStamp) =>
  token && new Date(timeStamp) > new Date();

const refreshTime = (timeStamp) =>
  (new Date(timeStamp).getTime() - new Date(1970, 1, 1).getTime()) / 2;

export type AuthConfig = {
  mode?: 'SP' | 'normal';
  initAuthCode?: boolean;
  whiteList?: string[];
  formatLanguageService: IFormatLanguageService;
};

// // 这个服务将被注册至全局
export const AuthService = getServiceContext(useAuthService);

/**
 * 挂载具体业务的权限模块
 */
export default function useAuthService(config: AuthConfig) {
  const {
    mode = 'normal',
    initAuthCode = false,
    whiteList = AUTH_WHITELIST,
    formatLanguageService,
  } = config;

  const spSiteUserRef = useRef<ISiteUserInfo>();
  const isNormalLogin = mode === 'normal';
  const refeshTokenTimeoutRef = useRef<NodeJS.Timeout>();
  const update = useUpdate();
  const reTryCountRef = useRef(0);
  const authCodeService = useAuthCodeService({ formatLanguageService });

  /**
   * token校验，无token跳转登录，token未过期则更新token
   * @param context
   * @param isMount 是否是mount
   */
  const tokenVerification = async (
    context: BaseJWTContext<Expired, BasePayload>,
    isMount = true,
  ) => {
    const { setToken, jwtRef } = context;
    const login = () => (isNormalLogin ? redirectToLogin() : SPLogin());
    const refreshInfo = () =>
      isNormalLogin ? jwtRef.current.token : spSiteUserRef.current?.Email;

    /** Sharepoint登录，请求用户信息，获取token */
    const SPLogin = async () => {
      if (++reTryCountRef.current > MAX_RETRY_COUNT) {
        return history.push('/404');
      }
      try {
        Loading.show();
        let user = await sp.web.currentUser();
        if (!user) {
          return;
        }
        spSiteUserRef.current = user;
        if (!user.Email) {
          throw 'Email为空';
        }
        await refreshToken();
      } catch (error) {
        console.log('请求SharePoint信息失败', error);
        message.warning({
          content: '因网络问题请求SharePoint信息失败，请刷新重试。',
          key: ERROR_MSG_KEY,
        });
        login();
      } finally {
        Loading.hide();
      }
    };

    /**
     * token刷新函数
     * token刷新异常处理机制
     * 1、如果刷新token返回状态码为401则退出系统
     * 2、如果刷新token异常但是状态码不为401则重新设置定时器，在token有效期过去一半时进行重新刷新。
     *    直到有效期只有一秒，仍然异常，就退出系统
     */
    const refreshToken = async () => {
      let res = await usePost<string>(
        `${Config.Api.Base}${Config.Api.ReflushToken}`,
        {
          info: refreshInfo(),
        },
      );

      if (res.success && res.data?.length > 0) {
        // token刷新完成,重试次数清0
        reTryCountRef.current = 0;
        if (jwtRef.current.toString() === res.data) {
          update();
          return;
        } else {
          setToken((v) => res.data);
        }

        /** 根据当前项目是否有做前端权限控制判断是否调用获取权限接口 */
        initAuthCode ? await authCodeService.initAuthCode() : null;
      } else if (res.status === 401) {
        message.warning({
          content: '请求token异常，请重新登录',
          key: ERROR_MSG_KEY,
        });
        login();
      } else {
        // 刷新token失败， 后端异常，定时重新刷新
        console.log(`刷新异常,重新刷新第${reTryCountRef.current}次`);
        setRefeshTokenTimeout((pre, next) => {
          // 刷新token服务器异常时，且没有是第一次进行刷新，拦截器ref未更新，completed为false，导致页面白屏，强制render保证页面正常加载
          if (!context.interceptorMount && !res.response) {
            update();
          }
        });
      }
    };

    /**
     * 重新设置下次请求时间
     * @comment 根据token过期时间设置定时任务，过期前1/2进行一次刷新,若刷新时间小于1s,重新登录
     * */
    const setRefeshTokenTimeout = (
      afterNextSetTimeout?: (pre: NodeJS.Timeout, next: NodeJS.Timeout) => void,
    ) => {
      refeshTokenTimeoutRef.current &&
        clearTimeout(refeshTokenTimeoutRef.current);
      const ms = refreshTime(jwtRef.current?.playload.exp);

      if (ms <= 1000) {
        // 若刷新时间小于1s,重新登录
        login();
      }

      let refeshTokenTimeout = setTimeout(
        () => {
          refreshToken();
        },
        ms > MAX_TIMEOUT ? MAX_TIMEOUT : ms,
      );

      console.log(
        `执行了setRefeshTokenTimeout,${getFutureTime(ms)}时刷新token`,
      );
      let preSetTimeout = refeshTokenTimeoutRef.current;
      refeshTokenTimeoutRef.current = refeshTokenTimeout;
      afterNextSetTimeout?.(preSetTimeout, refeshTokenTimeout);
    };
    const verification = () => {
      if (isWhiteList(whiteList)) {
        return;
      } else if (!isMount) {
        setRefeshTokenTimeout();
      } else if (
        isTokenValid(jwtRef.current.toString(), jwtRef.current?.playload?.exp)
      ) {
        refreshToken();
      } else {
        login();
      }
    };
    verification();
  };

  return {
    ...useJWTService({
      onMount: tokenVerification,
      onSetToken: (context) => tokenVerification(context, false),
    }),
    ...authCodeService,
    spSiteUserInfo: spSiteUserRef.current,
  };
}
