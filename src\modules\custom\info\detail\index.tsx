import React, { useState } from 'react';
import {
  Form,
  Select,
  Input,
  InputNumber,
  Row,
  Col,
  Collapse,
  Modal,
  Popconfirm,
  DatePicker,
  message,
  Space,
  Button,
  Checkbox,
  Upload,
  List,
  Divider,
  Image,
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  DownCircleOutlined,
  UpOutlined,
  DownOutlined,
} from '@ant-design/icons';
import {
  ContainerDiv,
  WrapperDiv,
  FormDiv,
  InputRightWrapDiv,
  PanelTitleDiv,
  FormTitleSpanDiv,
} from '@/assets/style/form';
import { HeaderDiv } from '@/components/Layout/style';
import { history } from 'umi';
import ReactJson from 'react-json-view';
import useService from './useService';
export default (props: any) => {
  const { Option } = Select;
  const { Panel } = Collapse;
  const layout: any = {
    requiredMark: true,
    wrapperCol: { flex: 'auto' },
  };
  const {
    form,
    tagForm,
    qccForm,
    tagList,
    infoTitle,
    isInfoVisible,
    infoJson,
    entTypeList,
    econKindList,
    qccInfo,
    activeKeys,
    setIsInfoVisible,
    submitCustom,
    showJsonInfo,
    setActiveKeys,
  } = useService(props);

  const handleTag = (item) => {
    let tag;
    switch (item?.labelType) {
      case 'Selection':
        tag =
          item?.optionsType && item?.optionsType == 1 ? (
            <Select disabled={item?.editable == 0} allowClear mode="multiple">
              {item.labelOptions.map((e) => {
                return (
                  <Option key={e} value={e}>
                    {e}
                  </Option>
                );
              })}
            </Select>
          ) : (
            <Select disabled={item?.editable == 0} allowClear>
              {item.labelOptions.map((e) => {
                return (
                  <Option key={e} value={e}>
                    {e}
                  </Option>
                );
              })}
            </Select>
          );
        break;
      case 'Text':
        tag = <Input disabled={item?.editable == 0} maxLength={50} />;
        break;
      case 'DateTime':
        tag = (
          <DatePicker
            disabled={item?.editable == 0}
            style={{ width: '100%' }}
          />
        );
        break;
      case 'Number':
        tag = (
          <InputNumber
            disabled={item?.editable == 0}
            style={{ width: '100%' }}
          />
        );
        break;
      default:
        tag = <Input disabled={item?.editable == 0} maxLength={50} />;
        break;
    }
    return tag;
  };

  return (
    <ContainerDiv>
      <HeaderDiv>客户信息</HeaderDiv>
      <WrapperDiv>
        <Collapse
          defaultActiveKey={['0', '1']}
          ghost={true}
          collapsible="header"
          onChange={(key: any) => {
            setActiveKeys(key);
          }}
        >
          <Panel
            header={
              <>
                <div style={{ float: 'left' }}>
                  <span style={{ paddingRight: 13 }}>基本信息</span>
                  <FormTitleSpanDiv></FormTitleSpanDiv>
                </div>
                <div style={{ float: 'right' }}>
                  <Button
                    icon={
                      activeKeys.includes('0') ? (
                        <UpOutlined />
                      ) : (
                        <DownOutlined />
                      )
                    }
                  >
                    {activeKeys.includes('0') ? '收起' : '展开'}
                  </Button>
                </div>
              </>
            }
            key={0}
            showArrow={false}
          >
            <FormDiv>
              <Form {...layout} form={form} layout="vertical">
                <Row gutter={40}>
                  <Col span={8}>
                    <Form.Item label="中文名称" name="customerZH">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="英文名称" name="customerEN">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="省份" name="province">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="城市" name="city">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="地址" name="address">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="组织机构代码" name="orgCode">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="街道" name="street">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="邮编号码" name="postalCode">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="区域" name="area">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="分区" name="partition">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="分类等级" name="classGrade">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="分类属性" name="classProp">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="CNOC号" name="cnocCode">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="客户编号" name="customerCode">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="最后修改人" name="modifiedBy">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="最后修改时间" name="modified">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
              {Object.keys(qccInfo).length != 0 ? (
                <Form {...layout} form={qccForm} layout="vertical">
                  <Row gutter={40}>
                    <Col span={8}>
                      <Form.Item label="统一社会信用代码" name="creditCode">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="Q公司名称" name="name">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="法定代表人" name="operName">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="状态" name="status">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="成立日期" name="startDate">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="注册资本" name="registCapi">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="实缴资本" name="recCap">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="更新日期" name="updatedDate">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="Q组织机构代码" name="orgNo">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="注册号" name="no">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="企业类型" name="econKindCodeList">
                        <Select disabled mode="multiple">
                          {econKindList.map((item) => {
                            return (
                              <Select.Option
                                key={item.value}
                                value={item.value}
                              >
                                {item.label}
                              </Select.Option>
                            );
                          })}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="营业期限始" name="termStart">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="营业期限至" name="teamEnd">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="行业分类" name="industry">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('industry'),
                                '行业分类',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="所属地区" name="area">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('area'),
                                '所属地区',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="省份" name="province">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="登记机关" name="belongOrg">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="人员规模" name="personScope">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="参保人数" name="insuredCount">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="曾用名" name="originalName">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('originalName'),
                                '曾用名',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="Q英文名" name="englishName">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="进出口企业代码" name="ixCode">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="注册地址" name="address">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="营业范围" name="scope">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="企业类型" name="entType">
                        <Select disabled>
                          {entTypeList.map((item) => {
                            return (
                              <Select.Option
                                key={item.value}
                                value={item.value}
                              >
                                {item.label}
                              </Select.Option>
                            );
                          })}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="分支机构" name="branches">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('branches'),
                                '分支机构',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="投资人及出资信息" name="partners">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('partners'),
                                '投资人及出资信息',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="主要人员" name="employees">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('employees'),
                                '主要人员',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="股票代码" name="stockNumber">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="Logo 地址" name="imageUrl">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="上市类型" name="stockType">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="吊销日期" name="endDate">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="发照日期" name="checkDate">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="变更信息" name="changeRecords">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('changeRecords'),
                                '变更信息',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="类型" name="econKind">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="标签列表" name="tagList">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('tagList'),
                                '标签列表',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="是否上市" name="isOnStock">
                        <Select disabled>
                          <Select.Option value="0">未上市</Select.Option>
                          <Select.Option value="1">上市</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="注销吊销信息" name="revokeInfo">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('revokeInfo'),
                                '注销吊销信息',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="联系信息" name="contactInfo">
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('contactInfo'),
                                '联系信息',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="最新企业年报中的联系方式"
                        name="arContactList"
                      >
                        <div className="inputBtn">
                          <Button
                            type="link"
                            onClick={() => {
                              showJsonInfo(
                                qccForm.getFieldValue('arContactList'),
                                '最新企业年报中的联系方式',
                              );
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </Form.Item>
                    </Col>
                    {/* <Col span={8}>
                      <Form.Item label="Logo" name="imageUrl">
                        <Image
                          width={100}
                          height={100}
                          src={qccForm?.getFieldValue('imageUrl')}
                        />
                      </Form.Item>
                    </Col> */}
                  </Row>
                </Form>
              ) : (
                ''
              )}
            </FormDiv>
          </Panel>
          <Panel
            header={
              <>
                <div style={{ float: 'left' }}>
                  <span style={{ paddingRight: 13 }}>人为信息</span>
                  <FormTitleSpanDiv></FormTitleSpanDiv>
                </div>
                <div style={{ float: 'right' }}>
                  <Button
                    icon={
                      activeKeys.includes('1') ? (
                        <UpOutlined />
                      ) : (
                        <DownOutlined />
                      )
                    }
                  >
                    {activeKeys.includes('1') ? '收起' : '展开'}
                  </Button>
                </div>
              </>
            }
            key={1}
            showArrow={false}
          >
            <FormDiv>
              <Form {...layout} form={tagForm} layout="vertical">
                <Row gutter={40}>
                  <Form.List name="tags">
                    {(fields) =>
                      fields.map((field, index) => (
                        <Col span={8} key={index}>
                          <Form.Item
                            {...field}
                            name={[field.name, 'labelValue']}
                            label={tagList[index]?.labelName}
                          >
                            {handleTag(tagList[index])}
                          </Form.Item>
                        </Col>
                      ))
                    }
                  </Form.List>
                </Row>
              </Form>
            </FormDiv>
          </Panel>
        </Collapse>
        <div style={{ textAlign: 'center' }}>
          <Space size={20} style={{ margin: '20px auto' }}>
            <Button
              onClick={() => {
                history.goBack();
              }}
            >
              取消
            </Button>
            <Button onClick={submitCustom} type="primary">
              提交
            </Button>
          </Space>
        </div>
      </WrapperDiv>
      <Modal
        title={infoTitle}
        visible={isInfoVisible}
        width={800}
        footer={null}
        onCancel={() => setIsInfoVisible(false)}
        destroyOnClose
        maskClosable={false}
        keyboard={false}
      >
        <div style={{ maxHeight: 600, overflowY: 'auto' }}>
          <ReactJson
            src={infoJson}
            name={false}
            displayDataTypes={false}
            displayObjectSize={false}
            displayArrayKey={false}
            enableClipboard={false}
          />
        </div>
      </Modal>
    </ContainerDiv>
  );
};
