import { useRef, useState, useEffect } from 'react';
import { Form, Modal } from 'antd';
import { history } from 'umi';
import { message } from 'antd';
import moment from 'moment';
import localFields from '@/tools/labelFields';

export default (props: any) => {
  const [form] = Form.useForm();
  const [tagForm] = Form.useForm();
  const [qccForm] = Form.useForm();

  const [tagList, setTagList] = useState(Array<any>([]));
  const [customerId, setCustomerId] = useState('');
  const [infoTitle, setInfoTitle] = useState('');
  const [isInfoVisible, setIsInfoVisible] = useState(false);
  const [infoJson, setInfoJson] = useState();
  const [qccInfo, setQccInfo] = useState({});
  const [activeKeys, setActiveKeys] = useState(['0', '1']);

  const entTypeList = [
    {
      label: '企业',
      value: '0',
    },
    {
      label: '社会组织',
      value: '1',
    },
    {
      label: '中国香港公司',
      value: '3',
    },
    {
      label: '事业单位',
      value: '4',
    },
    {
      label: '基金会',
      value: '6',
    },
    {
      label: '医院',
      value: '7',
    },
    {
      label: '海外公司',
      value: '8',
    },
    {
      label: '律师事务所',
      value: '9',
    },
    {
      label: '学校',
      value: '10',
    },
    {
      label: '其他',
      value: '-1',
    },
  ];
  const econKindList = [
    {
      label: '有限责任公司',
      value: '10',
    },
    {
      label: '股份有限公司',
      value: '20',
    },
    {
      label: '国企',
      value: '30',
    },
    {
      label: '外商投资企业',
      value: '40',
    },
    {
      label: '个人独资企业',
      value: '50',
    },
    {
      label: '合伙制企业',
      value: '60',
    },
    {
      label: '个体工商户',
      value: '70',
    },
    {
      label: '联营企业',
      value: '80',
    },
    {
      label: '集体所有制',
      value: '90',
    },
    {
      label: '有限合伙',
      value: '100',
    },
    {
      label: '普通合伙',
      value: '110',
    },
  ];
  const objectTypeList = [
    'industry',
    'area',
    'revokeInfo',
    'contactInfo',
    'originalName',
    'branches',
    'partners',
    'employees',
    'changeRecords',
    'tagList',
    'arContactList',
  ];
  const listTypeList = [
    'originalName',
    'branches',
    'partners',
    'employees',
    'changeRecords',
    'tagList',
    'arContactList',
  ];

  const getDetail = () => {
    let customerId = props.location.query?.id;
    setCustomerId(customerId);
  };

  const setNewObject = (info, field) => {
    let data = { ...info };
    for (let key in data) {
      data[field[key]] = data[key];
      if (['KeyNo', 'Id', 'CompanyId'].includes(key)) {
        delete data[key];
      }
      delete data[key];
    }
    return data;
  };

  const setNewList = (list, field) => {
    let arr = list ? [...list] : [];
    arr.forEach((item) => {
      for (let key in item) {
        item[field[key]] = item[key];
        if (['KeyNo', 'Id', 'CompanyId'].includes(key)) {
          delete item[key];
        }
        delete item[key];
      }
    });
    return arr;
  };

  const showJsonInfo = (json, name) => {
    setInfoTitle(name);
    setIsInfoVisible(true);
    setInfoJson(json);
  };

  const submitCustom = () => {
    Modal.confirm({
      title: 'Tips',
      content: '是否确认修改医院标签内容？',
      onOk: () => {
        let tags = JSON.parse(JSON.stringify(tagForm.getFieldValue('tags'))).filter((item) => item.editable == 1);
        tags.forEach((item) => {
          if (item.labelType == 'DateTime') {
            item['labelValue'] = item.labelValue ? moment(item.labelValue).format('YYYY-MM-DD') : null;
          }
          if (item.labelType == 'Selection' && item.optionsType == 1) {
            item['labelValue'] = item.labelValue && item.labelValue.length ? item.labelValue?.join(',') : null;
          }
        });
        let labels = tags.map((item) => ({
          labelId: item.labelId,
          value: item.labelValue,
        }));
        if (labels.length == 0) {
          message.destroy();
          message.error(`暂无可修改的人为信息`);
          return;
        }
        let data = {
          customerId: customerId,
          labels: labels,
        };
      },
    });
  };

  useEffect(() => {
    getDetail();
  }, []);

  return {
    form,
    tagForm,
    qccForm,
    tagList,
    infoTitle,
    isInfoVisible,
    infoJson,
    entTypeList,
    econKindList,
    qccInfo,
    activeKeys,
    setIsInfoVisible,
    submitCustom,
    showJsonInfo,
    setActiveKeys,
  };
};
