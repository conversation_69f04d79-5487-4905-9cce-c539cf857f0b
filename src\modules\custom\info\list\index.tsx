import React, { useEffect, useState } from 'react';
import {
  Form,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  Space,
  Modal,
  Collapse,
  Card,
  message,
  InputNumber,
} from 'antd';
import {
  ContainerDiv,
  WrapperDiv,
  SearchDiv,
  TableWrapDiv,
  TableTopDiv,
  TableTitleDiv,
  TableTitleSpanDiv,
  TaleTitleIconDiv,
  TableBtnDiv,
  BtnBlaWrap,
  BtnOrgWrap,
  OperDiv,
} from '@/assets/style/list';
import { HeaderDiv } from '@/components/Layout/style';
import {
  PanelWrapperDiv,
  PanelHeaderDiv,
  SquareWrapper,
  SquareGrayWrapper,
  SquareTag,
  SquareSelectedTag,
} from '@/assets/style/form';
import {
  SettingOutlined,
  CloseOutlined,
  CloseCircleOutlined,
  MinusOutlined,
  EditOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import useService from './useService';
import { history } from 'umi';
import { DraggableAreasGroup } from 'react-draggable-tags';
import Draggable from 'react-draggable';

export default (props: any) => {
  const { Option } = Select;
  const { Search } = Input;
  const { RangePicker } = DatePicker;
  const layout: any = {
    requiredMark: true,
    // labelCol: { flex: '80px' },
    // wrapperCol: { flex: 'auto' },
  };
  const editLayout: any = {
    requiredMark: true,
    labelCol: { span: 7 },
    wrapperCol: { span: 17 },
  };
  const {
    searchForm,
    pageData,
    pageParams,
    isModalVisible,
    selectRows,
    isEditModalVisible,
    tagForm,
    tagList,
    authBtns,
    searchTags,
    resultTags,
    searchLabel,
    tableColumns,
    columns,
    provinceList,
    setColumns,
    formSearch,
    formReset,
    onPageChange,
    toDetail,
    toSync,
    handleOk,
    handleCancel,
    handleMinCancel,
    setIsModalVisible,
    batchEdit,
    selectRowsChange,
    handleEditOk,
    handleEditCancel,
    selectAllRowsChange,
    setFields,
    handleSearchTags,
    handleSearchRight,
    handleClickDelete,
    onSearch,
    handleResultTags,
    handleResultRight,
    handleClickDeleteResult,
    onResultSearch,
    toSyncAzureData,
    getProvince,
  } = useService(props);

  const operColumns: any = [
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      width: 200,
      fixed: 'right',
      render: (text, record) => {
        const _btnJsx = {
          CustomerView: (
            <BtnOrgWrap key="view">
              <Button
                type="link"
                onClick={() => {
                  toDetail(record.id, 'view');
                }}
              >
                查看
              </Button>
            </BtnOrgWrap>
          ),
          CustomerEdit: (
            <Button
              key="edit"
              type="link"
              onClick={() => {
                toDetail(record.id, 'detail');
              }}
            >
              修改
            </Button>
          ),
          CustomerSync: (
            <Button
              key="sync"
              type="link"
              onClick={() => {
                toSync(record.id);
              }}
            >
              同步
            </Button>
          ),
        };
        return <OperDiv>{authBtns.map((item) => _btnJsx[item])}</OperDiv>;
      },
    },
  ];

  const group = new DraggableAreasGroup();
  const DraggableArea = group.addArea('area');
  const DraggableAreaA = group.addArea('areaA');
  const DraggableAreaB = group.addArea('areaB');

  const group1 = new DraggableAreasGroup();
  const DraggableArea1 = group1.addArea('area1');
  const DraggableArea2 = group1.addArea('area2');
  const DraggableArea3 = group1.addArea('area3');

  const handleTag = (item) => {
    let tag;
    switch (item?.labelType) {
      case 'Selection':
        tag =
          item?.optionsType && item?.optionsType == 1 ? (
            <Select allowClear mode="multiple">
              {item.labelOptions.map((e) => {
                return (
                  <Option key={e} value={e}>
                    {e}
                  </Option>
                );
              })}
            </Select>
          ) : (
            <Select allowClear>
              {item.labelOptions.map((e) => {
                return (
                  <Option key={e} value={e}>
                    {e}
                  </Option>
                );
              })}
            </Select>
          );
        break;
      case 'Text':
        tag = <Input maxLength={50} />;
        break;
      case 'DateTime':
        tag = <DatePicker style={{ width: '100%' }} />;
        break;
      case 'Number':
        tag = <InputNumber style={{ width: '100%' }} />;
        break;
      default:
        tag = <Input maxLength={50} />;
        break;
    }
    return tag;
  };

  const handleLabel = (item) => {
    let label;
    switch (item?.fieldProperty) {
      case 'Selection':
        label = (
          <Select allowClear>
            {item?.labelOptions.map((e) => {
              return (
                <Option key={e} value={e}>
                  {e}
                </Option>
              );
            })}
          </Select>
        );
        break;
      case 'DateTime':
        label = (
          <RangePicker allowEmpty={[true, true]} style={{ width: '100%' }} />
        );
        break;
      case 'provinceSelection':
        label = (
          <Select
            allowClear
            mode="multiple"
            onChange={getProvince}
            maxTagCount={'responsive' as const}
          >
            {provinceList.map((e) => {
              return (
                <Option key={e.value} value={e.label}>
                  {e.label}
                </Option>
              );
            })}
          </Select>
        );
        break;
      default:
        label = <Input maxLength={50} />;
        break;
    }
    return label;
  };

  const [disabled, setDisabled] = useState(true);
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });

  let draggleRef = React.createRef();

  const onStart = (event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    let bound = {
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    };
    setBounds(bound);
  };

  useEffect(() => {
    setColumns([...tableColumns, ...operColumns]);
  }, [tableColumns]);

  return (
    <ContainerDiv>
      <HeaderDiv>客户信息</HeaderDiv>
      <WrapperDiv>
        <SearchDiv>
          <Form {...layout} form={searchForm} labelAlign="left">
            <Row gutter={20}>
              <Col span={22}>
                <Row gutter={20}>
                  <Form.List name="params">
                    {(fields) =>
                      fields.map((field, index) => (
                        <Col span={6} key={index}>
                          <Form.Item
                            {...field}
                            name={[field.name, 'fieldValue']}
                            label={searchLabel[index]?.name}
                          >
                            {handleLabel(searchLabel[index])}
                          </Form.Item>
                        </Col>
                      ))
                    }
                  </Form.List>
                </Row>
              </Col>
              <Col
                span={2}
                style={{
                  display: 'flex',
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                }}
              >
                <Button
                  type="primary"
                  htmlType="submit"
                  style={{ margin: '0 10px 24px 0' }}
                  onClick={formSearch}
                >
                  搜索
                </Button>
                <Button
                  type="default"
                  style={{ marginBottom: 24 }}
                  onClick={formReset}
                >
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
        </SearchDiv>

        <TableWrapDiv>
          <TableTopDiv>
            <TableTitleDiv style={{ float: 'left' }}>
              <span style={{ verticalAlign: 'middle', paddingRight: 12 }}>
                客户信息列表
              </span>
              <TableTitleSpanDiv></TableTitleSpanDiv>
            </TableTitleDiv>
            <TableBtnDiv style={{ float: 'right' }}>
              <Space>
                {authBtns && authBtns.includes('SuperAdmin') ? (
                  <Button type="primary" onClick={toSyncAzureData}>
                    SuperAdmin
                  </Button>
                ) : (
                  ''
                )}
                {authBtns && authBtns.includes('CustomerBatchEdit') ? (
                  <Button
                    type="primary"
                    onClick={batchEdit}
                    disabled={selectRows.length == 0}
                    icon={<EditOutlined />}
                    className="iconBtns"
                  >
                    批量修改
                  </Button>
                ) : (
                  ''
                )}
                <Button
                  icon={<SettingOutlined />}
                  className="iconBtns"
                  onClick={setFields}
                >
                  自定义设置
                </Button>
              </Space>
            </TableBtnDiv>
          </TableTopDiv>
          <div>
            <Table
              size="middle"
              dataSource={pageData.data}
              pagination={{
                current: pageParams.pageIndex,
                total: pageData.totalCount,
              }}
              rowKey="id"
              columns={columns}
              onChange={(pagination, filters, sorter) => {
                onPageChange(pagination);
              }}
              rowSelection={{
                fixed: true,
                selectedRowKeys: selectRows.map((x) => x.id),
                selections: false,
                onSelect: (record, selected, selectedRows, nativeEvent) => {
                  selectRowsChange(selectedRows);
                },
                onSelectAll: (selected, selectedRows, changeRows) => {
                  selectAllRowsChange(selectedRows);
                },
              }}
              rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
              scroll={{ x: 1800 }}
            />
          </div>
        </TableWrapDiv>
        <Modal
          title="批量修改"
          visible={isEditModalVisible}
          width={1000}
          onOk={handleEditOk}
          onCancel={handleEditCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Form {...editLayout} form={tagForm} labelAlign="left">
            <Row gutter={20}>
              <Form.List name="tags">
                {(fields) =>
                  fields.map((field, index) => (
                    <Col span={12} key={index}>
                      <Form.Item
                        {...field}
                        name={[field.name, 'labelValue']}
                        label={tagList[index]?.labelName}
                      >
                        {handleTag(tagList[index])}
                      </Form.Item>
                    </Col>
                  ))
                }
              </Form.List>
            </Row>
          </Form>
        </Modal>
        <Modal
          title={
            <div style={{ position: 'relative' }}>
              <div
                style={{
                  width: 'calc(100% - 30px)',
                  cursor: 'move',
                }}
                onMouseOver={() => {
                  if (disabled) {
                    setDisabled(false);
                  }
                }}
                onMouseOut={() => {
                  setDisabled(true);
                }}
                onFocus={() => {}}
                onBlur={() => {}}
              >
                自定义项目列表页设置
              </div>
              <MinusOutlined
                style={{ position: 'absolute', top: 0, right: 0 }}
                onClick={() => {
                  handleMinCancel();
                }}
              />
            </div>
          }
          visible={isModalVisible}
          width={1200}
          onOk={handleOk}
          onCancel={handleCancel}
          closable={false}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
          modalRender={(modal) => (
            <Draggable
              disabled={disabled}
              bounds={bounds}
              onStart={(event, uiData) => onStart(event, uiData)}
            >
              <div ref={draggleRef}>{modal}</div>
            </Draggable>
          )}
        >
          <Space
            direction="vertical"
            style={{
              width: '100%',
              overflowY: 'auto',
              overflowX: 'hidden',
              maxHeight: '700px',
            }}
          >
            <PanelWrapperDiv>
              <PanelHeaderDiv>
                <span>查询条件</span>
                <TableTitleSpanDiv></TableTitleSpanDiv>
              </PanelHeaderDiv>
              <Row gutter={20}>
                <Col span={16}>
                  <Row gutter={10} style={{ height: '100%' }}>
                    <Col span={12}>
                      <SquareGrayWrapper>
                        <Row style={{ marginBottom: 10 }}>
                          <Col span={6}>
                            <div className="squareTitle">· 基本信息</div>
                          </Col>
                          <Col span={18}>
                            <Search
                              onSearch={(value) => {
                                onSearch(value, 'basic');
                              }}
                              style={{ width: 200, float: 'right' }}
                            />
                          </Col>
                        </Row>
                        <DraggableArea
                          tags={searchTags['basic']}
                          render={({ tag }) => (
                            <SquareTag>{tag.name}</SquareTag>
                          )}
                          onChange={(leftTags, { fromArea, toArea }) => {
                            handleSearchTags(
                              leftTags,
                              { fromArea, toArea },
                              'basic',
                            );
                          }}
                        />
                      </SquareGrayWrapper>
                    </Col>
                    <Col span={12}>
                      <SquareGrayWrapper>
                        <Row style={{ marginBottom: 10 }}>
                          <Col span={6}>
                            <div className="squareTitle">· 人为信息</div>
                          </Col>
                          <Col span={18}>
                            <Search
                              onSearch={(value) => {
                                onSearch(value, 'person');
                              }}
                              style={{ width: 200, float: 'right' }}
                            />
                          </Col>
                        </Row>
                        <DraggableAreaA
                          tags={searchTags['person']}
                          render={({ tag }) => (
                            <SquareTag>{tag.name}</SquareTag>
                          )}
                          onChange={(leftTags, { fromArea, toArea }) => {
                            handleSearchTags(
                              leftTags,
                              { fromArea, toArea },
                              'person',
                            );
                          }}
                        />
                      </SquareGrayWrapper>
                    </Col>
                  </Row>
                </Col>
                <Col span={8}>
                  <SquareWrapper>
                    <Row style={{ marginBottom: 10 }}>
                      <Col span={6}>
                        <div className="squareTitle">· 已选信息</div>
                      </Col>
                    </Row>
                    <DraggableAreaB
                      tags={searchTags['right']}
                      render={({ tag }) => (
                        <div className="selectedSquare">
                          <SquareSelectedTag>{tag.name}</SquareSelectedTag>
                          <CloseCircleOutlined
                            className="closeIcon"
                            onClick={() => handleClickDelete(tag)}
                          />
                        </div>
                      )}
                      onChange={(tag, { fromArea, toArea }) => {
                        handleSearchRight(tag, fromArea, toArea);
                      }}
                    />
                  </SquareWrapper>
                </Col>
              </Row>
            </PanelWrapperDiv>
            <PanelWrapperDiv>
              <PanelHeaderDiv>
                <span>查询结果</span>
                <TableTitleSpanDiv></TableTitleSpanDiv>
              </PanelHeaderDiv>
              <Row gutter={20}>
                <Col span={16}>
                  <Row gutter={10} style={{ height: '100%' }}>
                    <Col span={12}>
                      <SquareGrayWrapper>
                        <Row style={{ marginBottom: 10 }}>
                          <Col span={6}>
                            <div className="squareTitle">· 基本信息</div>
                          </Col>
                          <Col span={18}>
                            <Search
                              onSearch={(value) => {
                                onResultSearch(value, 'basic');
                              }}
                              style={{ width: 200, float: 'right' }}
                            />
                          </Col>
                        </Row>
                        <DraggableArea1
                          tags={resultTags['basic']}
                          render={({ tag }) => (
                            <SquareTag>{tag.name}</SquareTag>
                          )}
                          onChange={(leftTags, { fromArea, toArea }) => {
                            handleResultTags(
                              leftTags,
                              { fromArea, toArea },
                              'basic',
                            );
                          }}
                        />
                      </SquareGrayWrapper>
                    </Col>
                    <Col span={12}>
                      <SquareGrayWrapper>
                        <Row style={{ marginBottom: 10 }}>
                          <Col span={6}>
                            <div className="squareTitle">· 人为信息</div>
                          </Col>
                          <Col span={18}>
                            <Search
                              onSearch={(value) => {
                                onResultSearch(value, 'person');
                              }}
                              style={{ width: 200, float: 'right' }}
                            />
                          </Col>
                        </Row>
                        <DraggableArea2
                          tags={resultTags['person']}
                          render={({ tag }) => (
                            <SquareTag>{tag.name}</SquareTag>
                          )}
                          onChange={(leftTags, { fromArea, toArea }) => {
                            handleResultTags(
                              leftTags,
                              { fromArea, toArea },
                              'person',
                            );
                          }}
                        />
                      </SquareGrayWrapper>
                    </Col>
                  </Row>
                </Col>
                <Col span={8}>
                  <SquareWrapper>
                    <Row style={{ marginBottom: 10 }}>
                      <Col span={6}>
                        <div className="squareTitle">· 已选信息</div>
                      </Col>
                    </Row>
                    <DraggableArea3
                      tags={resultTags['right']}
                      render={({ tag }) => (
                        <div className="selectedSquare">
                          <SquareSelectedTag>{tag.name}</SquareSelectedTag>
                          <CloseCircleOutlined
                            className={`closeIcon ${
                              tag?.isSearch ? 'disabled' : ''
                            }`}
                            onClick={() => handleClickDeleteResult(tag)}
                          />
                        </div>
                      )}
                      onChange={(tag, { fromArea, toArea }) => {
                        handleResultRight(tag, fromArea, toArea);
                      }}
                    />
                  </SquareWrapper>
                </Col>
              </Row>
            </PanelWrapperDiv>
          </Space>
        </Modal>
      </WrapperDiv>
    </ContainerDiv>
  );
};
