import React, { useEffect, useState } from 'react';
import {
  Form,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  Space,
  Modal,
  Collapse,
  Card,
} from 'antd';
import {
  SearchDiv,
  TableWrapDiv,
  TableTopDiv,
  TableTitleDiv,
  TaleTitleIconDiv,
  TableBtnDiv,
  BtnBlueWrap,
  OperDiv,
} from '@/assets/style/list';
import {
  PanelWrapperDiv,
  PanelHeaderDiv,
  SquareWrapper,
  SquareTag,
} from '@/assets/style/form';
import { SettingOutlined, CloseOutlined } from '@ant-design/icons';
import moment from 'moment';
import Tooltip from 'antd/es/tooltip';
import useService from './useService';
import { history } from 'umi';
import { DraggableAreasGroup } from 'react-draggable-tags';
export default (props: any) => {
  const { Panel } = Collapse;
  const {
    pageData,
    regions,
    districts,
    isModalVisible,
    // serachLeftTags,
    // searchRightTags,
    regionChange,
    toEdit,
    handleOk,
    handleCancel,
    setFields,
    onSetLeftTag,
    onSetRightTag,
    handleClickDelete,
    // setSerachLeftTags,
    // setSerachRightTags,
  } = useService(props);

  const columns: any = [
    {
      title: '客户名称',
      dataIndex: 'customName',
      key: 'customName',
      align: 'center',
    },
    {
      title: '跟进人',
      dataIndex: 'follower',
      key: 'follower',
      align: 'center',
    },
    {
      title: '客户号',
      dataIndex: 'customNo',
      key: 'customNo',
      align: 'center',
    },
    {
      title: '所在大区',
      dataIndex: 'region',
      key: 'region',
      align: 'center',
    },
    {
      title: '所在小区',
      dataIndex: 'district',
      key: 'district',
      align: 'center',
    },
    {
      title: '中文地址',
      dataIndex: 'address',
      key: 'address',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      width: 200,
      // fixed: 'right',
      render: (text, record) => (
        <OperDiv>
          <Button
            type="link"
            onClick={() => {
              history.push({
                pathname: '/custom/info/view',
                query: {
                  id: record.id,
                },
              });
            }}
          >
            查看
          </Button>
          <Button
            type="link"
            onClick={() => {
              toEdit(record.id);
            }}
          >
            修改
          </Button>
        </OperDiv>
      ),
    },
  ];

  const [resourceTags, setSource] = useState({
    left: [
      { id: 1, content: '医院属性' },
      { id: 2, content: '医院等级' },
      { id: 3, content: '重点科室' },
      { id: 4, content: '所属集团' },
      { id: 5, content: '大区' },
      { id: 6, content: '小区' },
    ],
    right: [
      { id: 11, content: '客户名称' },
      { id: 22, content: 'CS KAM' },
      { id: 33, content: 'CS VIP' },
      { id: 44, content: '床位数' },
    ],
  });
  const [serachLeftTags, setSerachLeftTags] = useState([
    { id: 1, content: '医院属性' },
    { id: 2, content: '医院等级' },
    { id: 3, content: '重点科室' },
    { id: 4, content: '所属集团' },
    { id: 5, content: '大区' },
    { id: 6, content: '小区' },
  ]);
  const [searchRightTags, setSerachRightTags] = useState([
    { id: 11, content: '客户名称' },
    { id: 22, content: 'CS KAM' },
    { id: 33, content: 'CS VIP' },
    { id: 44, content: '床位数' },
  ]);
  const group = new DraggableAreasGroup();
  const DraggableArea1 = group.addArea();
  const DraggableArea2 = group.addArea();
  useEffect(() => {
    return () => {
      setSerachLeftTags(null);
      setSerachRightTags(null);
    };
  }, []);

  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ span: 8 }}>
          <Row>
            <Col span={6}>
              <Form.Item name="region" label="所在大区">
                <Select
                  allowClear
                  placeholder="所在大区"
                  onChange={regionChange}
                >
                  {regions.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.code}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="district" label="所在小区">
                <Select allowClear placeholder="所在小区">
                  {districts.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.code}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="keyword" label="关键字：">
                <Input placeholder="关键字" maxLength={50} />
              </Form.Item>
            </Col>
            <Col span={2} style={{ textAlign: 'right' }}>
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
            </Col>
          </Row>
        </Form>
      </SearchDiv>

      <TableWrapDiv>
        <TableTopDiv>
          <TableBtnDiv style={{ float: 'right' }}>
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={setFields}
            ></Button>
          </TableBtnDiv>
        </TableTopDiv>
        <div>
          <Table
            dataSource={pageData.data}
            pagination={{ total: pageData.totalCount }}
            rowKey="id"
            columns={columns}
          />
        </div>
      </TableWrapDiv>
      <Modal
        title="自定义项目列表页设置"
        visible={isModalVisible}
        width={1100}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <PanelWrapperDiv>
          <PanelHeaderDiv>查询条件</PanelHeaderDiv>
          <div>可查询条件：</div>
          <Row gutter={20}>
            {/* <Col span={5}>
              <SquareWrapper>
                <DraggableArea1
                  tags={serachLeftTags}
                  render={({tag}) => (
                    <SquareTag>
                      {tag.content}
                    </SquareTag>
                  )}
                  onChange={onSetLeftTag}
                />
              </SquareWrapper>
            </Col>
            <Col span={19}>
              <SquareWrapper>
                <DraggableArea2
                  tags={searchRightTags}
                  render={({tag}) => (
                    <SquareTag>
                      <CloseOutlined onClick={() => handleClickDelete(tag)} />
                      {tag.content}
                    </SquareTag>
                  )}
                  onChange={onSetRightTag}
                />
              </SquareWrapper>
            </Col> */}
            <SquareWrapper key="1">
              <DraggableArea1
                tags={resourceTags.left}
                render={({ tag }) => <SquareTag>{tag.content}</SquareTag>}
                onChange={(tag) => {
                  console.log('左边');
                  // setSource({...resourceTags,left:tag})
                  // setSerachLeftTags([...tag])
                }}
              />
              {/* </SquareWrapper>
            <SquareWrapper  key="2"> */}
              <DraggableArea2
                tags={resourceTags.right}
                render={({ tag }) => (
                  <SquareTag>
                    <CloseOutlined onClick={() => handleClickDelete(tag)} />
                    {tag.content}
                  </SquareTag>
                )}
                onChange={(tag) => {
                  console.log('youbian', tag);

                  setSource({ ...resourceTags, right: [...tag] });
                }}
              />
            </SquareWrapper>
          </Row>
        </PanelWrapperDiv>
      </Modal>
    </div>
  );
};
