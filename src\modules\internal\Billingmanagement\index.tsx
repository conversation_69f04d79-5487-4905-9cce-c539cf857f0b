import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv } from './style';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  InputNumber,
  message,
  Tooltip,
} from 'antd';
import moment from 'moment';
import { Operation } from './style';
import { history } from 'umi';
import TableTitle from '../../../components/TableTitle';
import useServices from './useServices';
import AuthorityComponent from '@/components/AuthorityComponent';

export default memo(function (props) {
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    btns,
    form,
    orderType,
    scrollY,
    supplierNameData,
    supplierTypeData,
    queryClearingData,
    queryProductData,
    exportReport,
  } = useServices(props);
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '70px' },
  };
  const [outlineForm] = Form.useForm();
  const { RangePicker } = DatePicker;
  //账单进度
  const BillingProgress = [
    { name: '被退回', code: '-2' },
    { name: '待比对', code: '1' },
    { name: '比对失败', code: '-1' },
    { name: '待生成格式PO', code: '3' },
  ];
  //运输方式
  const gettransport = [
    { name: '自提', code: '自提' },
    { name: '送货', code: '送货' },
  ];
  //结算公司
  const settlement = [{ name: '微钉科技', code: '1' }];
  useEffect(() => {}, []);
  //查看对账单详情
  const onDetails = (id: string) => {
    console.log(id);
    history.push({
      pathname: '/pto/internal/StatementDetails',
      state: { id: id, name: 'sign' },
    });
  };

  const formatDetails = (id: string) => {
    console.log(id);
    history.push({
      pathname: '/pto/internal/FormatpoDetails',
      state: { id: id },
    });
  };

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const columns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      width: 200,
      render: (text, record) => {
        return (
          <AuthorityComponent type="Billingmanagement-Details">
            <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.id)}>
              {text}
            </span>
          </AuthorityComponent>
        );
      },
    },
    {
      title: '格式单号',
      dataIndex: 'formatPoNo',
      key: 'formatPoNo',
      align: 'center',
      width: 200,
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => formatDetails(record.formatPoNo)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '结算公司',
      dataIndex: 'lindeClearingCompany',
      key: 'lindeClearingCompany',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '结算公司代码',
      dataIndex: 'lindeClearingCompanyCode',
      key: 'lindeClearingCompanyCode',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '日期',
      dataIndex: 'billingDate',
      key: 'billingDate',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '货源点/客户',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 3,
              maximumFractionDigits: 3,
            }).format(text)
          : '';
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '含税单价',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税金额',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '账单进度',
      dataIndex: 'billingStatusLabel',
      key: 'billingStatusLabel',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, record) => {
        return (
          <div
            style={{
              background:
                record.status == '1'
                  ? 'orange'
                  : record.status == '-1'
                  ? 'red'
                  : record.status == '-2'
                  ? '#cccccc'
                  : 'green',
              color: '#fff',
              cursor: 'pointer',
              maxWidth: 150,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            }}
          >
            <Tooltip placement="top" title={text}>
              {text}
            </Tooltip>
          </div>
        );
      },
    },
  ];
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="billingDate" label="账期年月">
                <DatePicker picker="month" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierType" label="供应商类型">
                <Select placeholder="供应商类型" allowClear>
                  {supplierTypeData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierType}>
                        {x.supplierType}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierName" label="供应商名称">
                <Select placeholder="供应商名称" allowClear showSearch>
                  {supplierNameData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierName}>
                        {x.supplierName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="modeTransport" label="运输方式">
                <Select placeholder="运输方式" allowClear>
                  {gettransport.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.code}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="productName" label="产品名称">
                <Select placeholder="产品名称" allowClear>
                  {queryProductData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.productName}>
                        {x.productName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="billStatus" label="账单进度">
                <Select placeholder="账单进度" allowClear>
                  {BillingProgress.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.code}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="lindeClearingCompany" label="结算公司">
                <Select placeholder="结算公司" showSearch allowClear>
                  {queryClearingData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.name}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="keyWords" label="关键字">
                <Input placeholder="关键字" className="keywordInput" allowClear />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Operation>
                <AuthorityComponent type="Billingmanagement-Search">
                  <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                    搜索
                  </Button>
                </AuthorityComponent>
                <AuthorityComponent type="Billingmanagement-Export">
                  <Button onClick={() => exportReport()} type="primary" className="searchBut">
                    导出
                  </Button>
                </AuthorityComponent>
              </Operation>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          dataSource={data}
          pagination={{
            total: total,
            current: current,
            pageSize: pageSize,
            showSizeChanger: true,
          }}
          onChange={onPageChange}
          columns={columns}
          rowKey="id"
          scroll={{ x: columns?.length * 160, y: scrollY }}
        />
      </TableWrapDiv>
    </div>
  );
});
