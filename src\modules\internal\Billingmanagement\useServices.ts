import { useRef, useState, useEffect } from 'react';
import {
  queryInnerBillingList,
  querySupplierNameInfo,
  querySupplierTypeInfo,
  queryClearingInfo,
  queryProductInfo,
  exportInnerBillingList,
} from '@/app/request/requestApi';
import { useLocation } from 'umi';
import { message, Form } from 'antd';
import { getTableScroll } from '@/tools/utils';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [orderStatus, setOrderStatus] = useState([]);
  const location = useLocation<any>();
  const [supplierNameData, setSupplierNameData] = useState(<any>[]);
  const [supplierTypeData, setSupplierTypeData] = useState(<any>[]);
  const [queryClearingData, setQueryClearingData] = useState(<any>[]);
  const [queryProductData, setQueryProductData] = useState(<any>[]);

  const [search, setSearch] = useState(<any>{
    status: '',
    orderType: '',
    productCategory: '',
    keyword: '',
    searchFromDate: '',
    searchToDate: '',
  });
  const [btns, setBtns] = useState([]);

  //导出被退回的条目的账单
  const exportReport = () => {
    exportInnerBillingList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 99999,
    }).then((res: any) => {
      if (res.response.status == 200) {
        // decodeURIComponent(uri)
        let data = res.response.headers['content-disposition'];
        var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = `${decodeURIComponent(filenameRegex.exec(data)[1])}`;
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };

  const getTable = () => {
    let searchFromDate: string;
    let searchToDate: string;
    if (!form.getFieldValue('searchDate')) {
      searchFromDate = '';
      searchToDate = '';
    } else {
      searchFromDate = form.getFieldValue('searchDate')[0];
      searchToDate = form.getFieldValue('searchDate')[1];
    }
    //首页列表接口 现在这个接口还是一期的
    queryInnerBillingList({
      ...form.getFieldsValue(),
      pageIndex: current,
      pageSize: pageSize,
    }).then((res) => {
      // console.log(res.msg);
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
  };

  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      // 清除订阅
      window.removeEventListener('resize', resize);
    };
  }, []);
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  useEffect(() => {
    querySupplierNameInfo('').then((res) => {
      console.log(res);
      if (res.success) {
        setSupplierNameData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取供应商状态
    querySupplierTypeInfo().then((res) => {
      if (res.success) {
        setSupplierTypeData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取结算公司代码
    queryClearingInfo().then((res) => {
      console.log(res);
      if (res.success) {
        setQueryClearingData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取产品信息名称
    queryProductInfo('').then((res) => {
      if (res.success) {
        setQueryProductData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
  }, [location]);
  //翻页：重置current
  const onPageChange = (pagination, filters, sorter) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const onValuesChange = (changedValues, allValues) => {
    setSearch({ ...allValues });
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };

  return {
    search,
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onValuesChange,
    onPageChange,
    getTable,
    tableHeight,
    btns,
    form,
    scrollY,
    supplierNameData,
    supplierTypeData,
    queryClearingData,
    queryProductData,
    exportReport,
  };
};
