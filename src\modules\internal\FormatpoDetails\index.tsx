import {
  Button,
  Upload,
  Row,
  Col,
  Space,
  Form,
  Input,
  Select,
  DatePicker,
  Table,
  message,
  InputNumber,
  Popconfirm,
} from 'antd';
import React, { memo, useState, useEffect, useRef } from 'react';
import moment from 'moment';
import { ButtonFooter, CardForm, CardTable, SectionTitle, HeaderTitle } from '../../supplier/initstatement/style';
import { queryInnerHomeStatementById } from '../../../app/request/requestApi';
import { useLocation } from 'umi';
import TableTitle from '../../../components/TableTitle';
import { classList, classData, showOptionLabel } from '../../../components/StateVerification';
import { CopyOutlined, DeleteOutlined, PlusSquareOutlined } from '@ant-design/icons';
const { Option } = Select;

export default memo(function (props) {
  const [form] = Form.useForm();
  const tableRef = useRef<any>();
  const location = useLocation<any>();
  const [data, setdata] = useState();
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 24 },
    wrapperCol: { span: 24 },
  };
  const objectToFormData = (obj, form) => {
    const fd = form || new FormData();
    for (let property in obj) {
      if (obj.hasOwnProperty(property)) {
        if (typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
          objectToFormData(obj[property], fd);
        } else if (obj[property] instanceof File) {
          fd.append('file', obj[property]);
        }
      }
    }
    return fd;
  };

  const columns: any = [
    {
      title: '对账单号',
      align: 'center',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      width: 100,
    },
    {
      title: '日期', //必填项
      align: 'center',
      dataIndex: 'billingDate',
      key: 'billingDate',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式', //必填项
      align: 'center',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      width: 100,
    },
    {
      title: '货源点/客户', //必填项
      align: 'center',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      width: 100,
    },
    {
      title: '车牌号', //必填/非必填-根据运输方式
      align: 'center',
      key: 'carNo',
      dataIndex: 'carNo',
      width: 100,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '数量', //必填/非必填？
      align: 'center',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 3,
              maximumFractionDigits: 3,
            }).format(text)
          : '';
      },
    },
    {
      title: '单位', //必填项,选择了TO或M3则最多3位小数，否则必须为整数
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
    },
    {
      title: '不含税单价', //必填项
      align: 'center',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 6,
              maximumFractionDigits: 6,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税单价', //必填项
      align: 'center',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4,
            }).format(text)
          : '';
      },
    },
    {
      title: '不含税金额', //计算得出
      align: 'center',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税金额', //计算得出
      align: 'center',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币',
      align: 'center',
      dataIndex: 'currency',
      key: 'currency',
      width: 100,
      render: (data, record) => {
        return data ? <div>{data}</div> : <div>CNY</div>;
      },
    },
    {
      title: '单据尾号',
      align: 'center',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      width: 100,
    },
    {
      title: '公司代码',
      align: 'center',
      dataIndex: 'companyCode',
      key: 'companyCode',
      width: 100,
    },
    {
      title: '供应商类型',
      dataIndex: 'sourceType',
      key: 'sourceType',
      ellipsis: true,
      align: 'center',
      width: 100,
    },
    {
      title: '结算公司',
      dataIndex: 'lindeClearingCompany',
      key: 'lindeClearingCompany',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '结算公司代码',
      dataIndex: 'lindeClearingCompanyCode',
      key: 'lindeClearingCompanyCode',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '交货条款',
      dataIndex: 'deliveryType',
      key: 'deliveryType',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '备注',
      align: 'center',
      dataIndex: 'remarks',
      width: 100,
      key: 'remarks',
    },
  ];
  const Upperlevel = () => {
    window.history.back();
  };

  const getDataById = () => {
    queryInnerHomeStatementById(location?.state?.id)
      .then((res) => {
        if (res.success) {
          setdata(res.data.billingEntryList);
          form.setFieldsValue({
            formatPoNo: res.data.formatPoNo,
            confirmStatus: showOptionLabel(classData, res.data.confirmStatus),
            supplierName: res.data.supplierName,
            supplieCode: res.data.supplieCode,
            lindeClearingCompany: res.data.lindeClearingCompany,
            productName: res.data.productName,
            unitPriceExcludingTax: res.data.unitPriceExcludingTax,
            settlementQuantity: res.data.settlementQuantity,
            amountExcludingTax: res.data.amountExcludingTax,
            poDate: moment(res.data.poDate).format('YYYY-MM-DD'),
            documentDate: moment(res.data.documentDate).format('YYYY-MM-DD'),
            valnPrice: res.data.valnPrice,
            poItem: res.data.poItem,
            totalValue: res.data.totalValue,
            poNo: res.data.poNo,
            productCode: res.data.productCode,
          });
        } else {
          // message.error();
        }
      })
      .catch((e) => {
        console.log(e);
      });
  };
  useEffect(() => {
    if (location?.state?.id) {
      getDataById();
    }
    console.log(window.location.search.substring(1), '详情id');
  }, [location]);

  return (
    <>
      <HeaderTitle>查看格式PO</HeaderTitle>
      <CardForm>
        <TableTitle title="格式PO基本信息"></TableTitle>
        <Form form={form} {...layout} layout="vertical">
          <Row gutter={20}>
            <Col span={6}>
              <Form.Item name="formatPoNo" label="格式单号">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="confirmStatus" label="状态">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="supplierName" label="供应商名称">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="supplieCode" label="供应商代码">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="poNo" label="订单号">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="poItem" label="订单条目">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="lindeClearingCompany" label="林德结算公司">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="productCode" label="产品代码">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="valnPrice" label="不含税单价">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="settlementQuantity" label="结算数量">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="totalValue" label="不含税金额">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="poDate" label="订单日期">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="documentDate" label="凭证日期">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </CardForm>
      <CardTable>
        <TableTitle title="对应供应商账单"></TableTitle>
        <div ref={tableRef}>
          <Table
            style={{ width: '100%' }}
            pagination={{
              showSizeChanger: true,
            }}
            columns={columns}
            dataSource={data}
            rowKey={(record) => record?.key}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            scroll={{ x: columns?.length * 190, y: 380 }}
          />
        </div>
      </CardTable>
      <ButtonFooter>
        <Space size="large">
          <Button type="primary" onClick={() => Upperlevel()}>
            关闭
          </Button>
        </Space>
      </ButtonFooter>
    </>
  );
});
