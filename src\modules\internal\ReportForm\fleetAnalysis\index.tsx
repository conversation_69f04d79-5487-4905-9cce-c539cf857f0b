import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv } from './style';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  InputNumber,
  message,
} from 'antd';
import moment from 'moment';
import { Operation } from './style';
import { history } from 'umi';
import useServices from './useServices';
import { fleetData, getRowSpanCount, showOptionLabel } from '@/components/StateVerification';
import AuthorityComponent from '@/components/AuthorityComponent';
import { Axis, Bar, Chart, Coord, Legend, Tooltip, Interval } from 'viser-react';
import TableTitle from '@/components/TableTitle';
import { QueryFleetAnalysisList, QueryFleetChartsList } from '@/app/request/requestApi';
const DataSet = require('@antv/data-set');
export default memo(function (props) {
  const {
    onPageChange,
    form,
    orderType,
    scrollY,
    queryClearingData2,
    supplierTypeData,
    supplierNameData,
    productData,
    pageSize,
    fleetLead,
    ExportFleet,
    QuantityData,
    onQueryQuantity,
    setqueryrecord,
  } = useServices(props);
  const { RangePicker } = DatePicker;
  const [isSupplement, setisSupplement] = useState(false); //数量
  //限制时间选项不超过半年
  const [dates, setDates] = useState(null);
  const [value, setValue] = useState(null);
  const [trendsColumns, setTrendsColumns] = useState(null); //车队动态表格title
  const [vehicleData, setVehicleData] = useState([]);
  const [current, setCurrent] = useState(1);
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  //
  const disabledDate = (current) => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') > 180;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') > 180;
    return !!tooEarly || !!tooLate;
  };
  //G2

  const dv = new DataSet.View().source(vehicleData);
  dv.transform({
    type: 'fold',
    fields: ['totalCount', 'kpiFailedCount', 'totalTimes', 'kpiFailedTimes'],
    key: 'type',
    value: 'value',
    screenX: 'year',
    color: ({ value }) => {},
  });
  //处理数据
  const handleData = (data) => {
    const dataSource = data.map((item) => {
      console.log(item);
      return {
        label: `${item.region}-${item.accountPeriod}`,
        type: showOptionLabel(fleetData, item.type),
        value: item.value,
      };
    });
    return dataSource;
  };
  const dataCree = handleData(dv.rows);
  //查看对账单详情
  const columns: any = [
    {
      title: '车队信息',
      children: [
        {
          title: '区域',
          dataIndex: 'region',
          key: 'region',
          align: 'center',
          width: 150,
          render: (value, record, index) => {
            const obj = {
              children: value,
              props: { rowSpan: null },
            };
            obj.props.rowSpan = getRowSpanCount(data, 'region', index);
            obj.children = <div>{record.region}</div>;
            return obj;
          },
        },
        {
          title: '车队负责人',
          dataIndex: 'fleetLeader',
          key: 'poItem',
          align: 'center',
          width: 180,
        },
        {
          title: '邮箱地址',
          dataIndex: 'fleetMail',
          key: 'fleetMail',
          align: 'center',
          width: 150,
        },
      ],
    },
  ];
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };

  //查看对账单详情
  const onDetails = (billingId: string) => {
    history.push({
      pathname: '/pto/internal/StatementDetails',
      state: { id: billingId, name: 'sign' },
    });
  };
  //数量比对失败明细
  const quantityColumns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.billingDetailId)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      align: 'center',
      width: 100,
    },
    {
      title: '结算公司',
      dataIndex: 'clearingCompanyName',
      key: 'clearingCompanyName',
      align: 'center',
      width: 100,
    },
    {
      title: '日期',
      dataIndex: 'billingDate',
      key: 'billingDate',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      align: 'center',
      width: 100,
    },
    {
      title: '货源点',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      align: 'center',
      width: 100,
    },
    {
      title: '车牌号',
      dataIndex: 'carNo',
      key: 'carNo',
      align: 'center',
      width: 100,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      width: 100,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      width: 100,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      width: 100,
    },
    {
      title: '含税单价',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税单价',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '含税金额',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税金额',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号/DN#',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
      width: 100,
    },
    {
      title: '车队负责人',
      dataIndex: 'fleetLeader',
      key: 'fleetLeader',
      align: 'center',
      width: 100,
    },
    {
      title: '总失败次数',
      dataIndex: 'totalFailedTimes',
      key: 'totalFailedTimes',
      align: 'center',
      width: 100,
    },
    {
      title: 'KPI失败次数',
      dataIndex: 'kpiFailedTimes',
      key: 'kpiFailedTimes',
      align: 'center',
      width: 100,
    },
  ];
  //点击次数
  const onQuantityDetails = (record, state) => {
    setqueryrecord(record);
    onQueryQuantity(record, state);
    setisSupplement(true);
  };
  //关闭数量详情
  const onClose = () => {
    setisSupplement(false);
  };

  const getTable = () => {
    let accountPeriodStart: any;
    let accountPeriodEnd: any;
    if (!form.getFieldValue('billingDate')) {
      accountPeriodStart = '';
      accountPeriodEnd = '';
    } else {
      accountPeriodStart = form.getFieldValue('billingDate')[0];
      accountPeriodEnd = form.getFieldValue('billingDate')[1];
    }
    QueryFleetAnalysisList({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
      pageIndex: current,
      pageSize: pageSize,
    }).then((res) => {
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
        if (res.data.length > 0) {
          res.data.map((item, index) => {
            const test = item.statisticsDataList.map((x, index) => {
              return [
                {
                  title: x.accountPeriod,
                  dataIndex: x.accountPeriod,
                  key: x.accountPeriod,
                  children: [
                    {
                      title: '总负责条数',
                      dataIndex: 'totalCount',
                      key: 'totalCount',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span
                            style={{ color: 'blue', cursor: 'pointer' }}
                            onClick={() => onQuantityDetails(record, 4)}
                          >
                            {record.statisticsDataList[index]?.totalCount}
                          </span>
                        );
                      },
                    },
                    {
                      title: '总失败次数',
                      dataIndex: 'totalTimes',
                      key: 'totalTimes',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span
                            style={{ color: 'blue', cursor: 'pointer' }}
                            onClick={() => onQuantityDetails(record, 2)}
                          >
                            {record.statisticsDataList[index]?.totalTimes}
                          </span>
                        );
                      },
                    },
                    {
                      title: 'KPI失败次数',
                      dataIndex: 'kpiFailedTimes',
                      key: 'kpiFailedTimes',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span
                            style={{ color: 'blue', cursor: 'pointer' }}
                            onClick={() => onQuantityDetails(record, 3)}
                          >
                            {record.statisticsDataList[index]?.kpiFailedCount}
                          </span>
                        );
                      },
                    },
                    {
                      title: 'KPI失败条数',
                      dataIndex: 'kpiFailedCount',
                      key: 'kpiFailedCount',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span
                            style={{ color: 'blue', cursor: 'pointer' }}
                            onClick={() => onQuantityDetails(record, 0)}
                          >
                            {record.statisticsDataList[index]?.kpiFailedTimes}
                          </span>
                        );
                      },
                    },
                  ],
                },
              ];
            });
            setTrendsColumns([...columns, ...test.flat()].flat());
          });
        } else {
          setTrendsColumns([...columns]);
        }
      } else {
        message.warning(res.msg);
      }
    });
    QueryFleetChartsList({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
    }).then((res) => {
      if (res.success) {
        setVehicleData(res.data);
      } else {
        return;
      }
    });
  };
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  //
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="billingDate" label="账期年月">
                <RangePicker
                  disabledDate={disabledDate}
                  onCalendarChange={(val) => setDates(val)}
                  onChange={(val) => setValue(val)}
                  separator="-"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="region" label="区域">
                <Select mode="multiple" placeholder="请选择" allowClear>
                  <Select.Option value="东区">东区</Select.Option>
                  <Select.Option value="北区">北区</Select.Option>
                  <Select.Option value="西南区">西南区</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierType" label="供应商类型">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {supplierTypeData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierType}>
                        {x.supplierType}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="supplierName" label="供应商名称">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {supplierNameData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierName}>
                        {x.supplierName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="lindeClearingCompany" label="结算公司">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {queryClearingData2.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.name}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="modeTransport" label="运输方式">
                <Select mode="multiple" placeholder="请选择" allowClear>
                  <Select.Option value="自提">自提</Select.Option>
                  <Select.Option value="送货">送货</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="productGroup" label="产品组">
                <Select mode="multiple" placeholder="请选择" showSearch allowClear>
                  {productData.map((item, index) => {
                    return (
                      <Select.Option key={index} value={item.productGroup}>
                        {item.productGroup}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="fleetLeader" label="车队负责人">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {fleetLead.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.mail}>
                        {x.leader}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Operation>
                <AuthorityComponent type="Billingmanagement-Search">
                  <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                    搜索
                  </Button>
                </AuthorityComponent>
                <AuthorityComponent type="Billingmanagement-Export">
                  <Button onClick={ExportFleet} type="primary" className="searchBut">
                    导出
                  </Button>
                </AuthorityComponent>
              </Operation>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <Table
          style={{ width: '100%', minHeight: '0px' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          dataSource={data}
          pagination={{
            total: total,
            current: current,
            pageSize: pageSize,
            // showSizeChanger: true,
          }}
          onChange={onPageChange}
          scroll={{ x: columns?.length * 160, y: 355 }}
          columns={trendsColumns}
          rowKey="id"
          bordered
        />
      </TableWrapDiv>
      <TableWrapDiv>
        {/* <div>
          <Form labelCol={{ flex: '100px' }}>
            <Row gutter={24}>
              <Col span={5}>
                <Form.Item name="name" label="月份">
                  <DatePicker picker="month" />
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item name="name2" label="区域">
                  <Select placeholder="请选择">
                    <Select.Option value="东区">东区</Select.Option>
                    <Select.Option value="北区">北区</Select.Option>
                    <Select.Option value="西南区">西南区</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item name="name3" label="负责方">
                  <Select placeholder="负责方" allowClear>
                    {['东区', '西区', '华南', '华北'].map((x, index) => {
                      return (
                        <Select.Option key={index} value={x}>
                          {x}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div> */}
        <div style={{ background: `rgba(37, 98, 157, 0.1)`, padding: '10px', borderRadius: '10px' }}>
          <Chart forceFit height={500} data={dataCree} padding={[50, 50, 0, 100]} width={800}>
            {/* 条形图 */}
            <Coord type="rect" direction="LT" />
            {/* 是否需要提示框 */}
            <Tooltip />
            {/* 是否支持下方筛选 */}
            <Legend />
            <Axis dataKey="label" position="right" />
            <Axis dataKey="label" label={{ offset: 12 }} />
            {/* <Bar position="label*value" color="type" adjust={[{ type: 'dodge', marginRatio: 1 / 32 }]} label="value" /> */}
            <Bar position="label*value" color="type" adjust={[{ type: 'dodge', marginRatio: 1 / 32 }]} />
          </Chart>
        </div>
      </TableWrapDiv>
      <Modal
        width={'60wh'}
        title={<div style={{ textAlign: 'center', fontWeight: 'bold' }}>数量比对失败明细</div>}
        visible={isSupplement}
        onCancel={onClose}
        footer
      >
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          pagination={
            {
              // showSizeChanger: true,
            }
          }
          columns={quantityColumns}
          dataSource={QuantityData}
          bordered
          scroll={{ x: columns?.length * 160, y: 535 }}
        />
      </Modal>
    </div>
  );
});
