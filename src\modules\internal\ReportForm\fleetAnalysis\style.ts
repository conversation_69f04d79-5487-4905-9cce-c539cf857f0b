import styled from 'styled-components';

export const Operation = styled.div`
  .searchBtn {
    width: 100px;
    margin-left: 19%;
  }
  .searchBut {
    width: 100px;
    margin-left: 20%;
  }
`;
export const SearchDiv = styled.div`
  padding: 30px 0px 0px;
  border-radius: 10px;
  background-color: #fff;
  border-bottom: 1px solid #eaf4f6;
  #title {
    color: #005293;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 15px;
    margin-left: 30px;
  }
`;
export const TableWrapDiv = styled.div`
  margin-top: 20px;
  border-radius: 10px;
  padding: 30px;
  background: #fff;
  .stripe,
  .stripe .ant-table-cell-fix-right,
  .stripe .ant-table-cell-fix-left {
    background-color: #f9f9f9;
  }
  .ant-spin-container .ant-table {
    min-height: 0px;
  }
  ant-table-body {
    min-height: 0px !important;
    height: 0px !important;
    background-color: #beced4 !important;
  }
  ant-card-body {
    overflow: scroll;
    max-height: 400px;
  }
  .ant-table {
    min-height: 400px !important;
  }
`;
