import React, { memo, useState, useEffect, useCallback } from 'react';
import { SearchDiv, TableWrapDiv } from './style';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  InputNumber,
  message,
  Card,
} from 'antd';
import moment from 'moment';
import { Operation, CardDiv } from './style';
import { Column } from '@ant-design/plots';
import { showOptionLabel, analysisData, analysis, color, getRowSpanCount } from '@/components/StateVerification';
import { G2, Chart, Tooltip, Interval, Coord } from 'bizcharts';
import { history } from 'umi';
import useServices from './useServices';

export default memo(function (props) {
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    form,
    orderType,
    scrollY,
    queryClearingData2,
    supplierTypeData,
    supplierNameData,
    productData,
    dataTree,
    dataTree1,
    dataTree2,
    ExportAllData,
  } = useServices(props);
  //限制时间选项不超过半年
  const [dates, setDates] = useState(null);
  const [value, setValue] = useState(null);
  //
  const disabledDate = (current) => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') > 180;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') > 180;
    return !!tooEarly || !!tooLate;
  };
  // 计算累加值
  const treatment = (data) => {
    const a = data.reduce((pre, cur) => {
      const { region: month, value: rain } = cur;
      let item = pre.find((p) => p.key === month);
      if (!item) {
        item = { key: month, value: rain };
        pre.push(item);
      } else item.value += rain;
      return pre;
    }, []);
    return a;
  };
  //
  const scale = {
    region: {
      values: treatment(dataTree).map((s) => s.key),
    },
  };
  //处理数据
  const handleData = (data) => {
    let a = data.map((item) => {
      console.log(showOptionLabel(color, item.name), '2111111111111111111111');
      return {
        name: showOptionLabel(analysisData, item.name),
        region: item.region,
        value: item.value,
        color: showOptionLabel(color, item.name),
      };
    });
    return a;
  };
  //所有条目数
  const handle = (data) => {
    let a = data.map((item) => {
      return {
        name: showOptionLabel(analysis, item.name),
        region: item.region,
        value: item.value,
      };
    });
    return a;
  };
  const { RangePicker } = DatePicker;
  //查看对账单详情

  const columns: any = [
    {
      title: '账期',
      dataIndex: 'accountPeriod',
      key: 'accountPeriod',
      align: 'center',
      width: 60,
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: { rowSpan: null },
        };
        obj.props.rowSpan = getRowSpanCount(data, 'accountPeriod', index);
        obj.children = <div>{record.accountPeriod}</div>;
        return obj;
      },
    },
    {
      title: '地区',
      dataIndex: 'region',
      key: 'region',
      align: 'center',
      width: 60,
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: { rowSpan: null },
        };
        obj.props.rowSpan = getRowSpanCount(data, 'region', index);
        obj.children = <div>{record.region}</div>;
        return obj;
      },
    },
    {
      title: '比对失败和待比对条数',
      dataIndex: 'toCompareAndFailCount',
      key: 'toCompareAndFailCount',
      align: 'center',
      width: 150,
    },
    {
      title: '待生成PO条数',
      dataIndex: 'toBeFormatPOCount',
      key: 'toBeFormatPOCount',
      align: 'center',
      width: 90,
    },
    {
      title: '待结算条数',
      dataIndex: 'toBeSettlCount',
      key: 'toBeSettlCount',
      align: 'center',
      width: 90,
    },
    {
      title: '已结算条数',
      dataIndex: 'settlCount',
      key: 'settlCount',
      align: 'center',
      width: 90,
    },
    {
      title: '比对失败和待比对数量',
      dataIndex: 'toCompareAndFailQuantity',
      key: 'toCompareAndFailQuantity',
      align: 'center',
      width: 150,
    },
    {
      title: '比对失败和待比对数量(氢气)',
      dataIndex: 'toCompareAndFailQuantityM3',
      key: 'toCompareAndFailQuantityM3',
      align: 'center',
      width: 150,
    },
    {
      title: '待生成PO数量',
      dataIndex: 'toBeFormatPOQuantity',
      key: 'toBeFormatPOQuantity',
      align: 'center',
      width: 90,
    },
    {
      title: '待生成PO数量(氢气)',
      dataIndex: 'toBeFormatPOQuantityM3',
      key: 'toBeFormatPOQuantityM3',
      align: 'center',
      width: 110,
    },
    {
      title: '待结算数量',
      dataIndex: 'toBeSettlQuantity',
      key: 'toBeSettlQuantity',
      align: 'center',
      width: 90,
    },
    {
      title: '待结算数量(氢气)',
      dataIndex: 'toBeSettlQuantityM3',
      key: 'toBeSettlQuantityM3',
      align: 'center',
      width: 100,
    },
    {
      title: '已结算数量',
      dataIndex: 'settlQuantity',
      key: 'settlQuantity',
      align: 'center',
      width: 90,
    },
    {
      title: '已结算数量(氢气)',
      dataIndex: 'settlQuantityM3',
      key: 'settlQuantityM3',
      align: 'center',
      width: 90,
    },
  ];
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ span: 7 }} form={form}>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="billingDate" label="账期年月">
                <RangePicker
                  separator="-"
                  style={{ width: '100%' }}
                  disabledDate={disabledDate}
                  onCalendarChange={(val) => setDates(val)}
                  onChange={(val) => setValue(val)}
                />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="region" label="区域">
                <Select mode="multiple" placeholder="请选择" allowClear>
                  <Select.Option value="东区">东区</Select.Option>
                  <Select.Option value="北区">北区</Select.Option>
                  <Select.Option value="西南区">西南区</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierType" label="供应商类型">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {supplierTypeData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierType}>
                        {x.supplierType}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="supplierName" label="供应商名称">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {supplierNameData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierName}>
                        {x.supplierName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="lindeClearingCompany" label="结算公司">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {queryClearingData2.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.name}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="modeTransport" label="运输方式">
                <Select mode="multiple" placeholder="请选择" allowClear>
                  <Select.Option value="自提">自提</Select.Option>
                  <Select.Option value="送货">送货</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}></Col>
            <Col span={7}></Col>
            <Col span={10}>
              <Operation>
                <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                  搜索
                </Button>
                <Button onClick={() => ExportAllData()} type="primary" className="searchBut">
                  导出
                </Button>
              </Operation>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          pagination={{
            total: total,
            current: current,
            pageSize: pageSize,
            showSizeChanger: true,
          }}
          onChange={onPageChange}
          columns={columns}
          dataSource={data}
          // rowKey="id"
          scroll={{ x: columns?.length * 160, y: 355 }}
        />
      </TableWrapDiv>
      <TableWrapDiv>
        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
          <CardDiv style={{ background: `rgba(37, 98, 157, 0.1)` }}>
            <h3 style={{ textAlign: 'center', fontWeight: 'bold', paddingBottom: '20px' }}>所有产品条目数量</h3>
            <Chart height={400} padding="auto" data={handle(dataTree)} scale={scale} autoFit width={500}>
              <Interval
                adjust={[
                  {
                    type: 'stack',
                  },
                ]}
                color={[
                  'name',
                  (xVal) => {
                    console.log(xVal, '54325432523');
                    if (xVal === '已结算条数') {
                      return '#FFFACD';
                    } else if (xVal === '待结算条数') {
                      return '#ADD8E6';
                    } else if (xVal === '待生成格式PO条数') {
                      return '#FFDAB9';
                    } else if (xVal === '比对失败和待比对条目条数') {
                      return '#FFB6C1';
                    }
                  },
                ]}
                position="region*value"
              />
              <Tooltip shared />
            </Chart>
          </CardDiv>
          <CardDiv style={{ background: `rgba(37, 98, 157, 0.1)` }}>
            <h3 style={{ textAlign: 'center', fontWeight: 'bold', paddingBottom: '20px' }}>非氢气的量(TO)</h3>
            <Chart height={400} padding="auto" data={handleData(dataTree1)} scale={scale} autoFit width={500}>
              <Interval
                adjust={[
                  {
                    type: 'stack',
                  },
                ]}
                color={[
                  'name',
                  (xVal) => {
                    console.log(xVal, '54325432523');
                    if (xVal === '已结算条数') {
                      return '#F0E68C';
                    } else if (xVal === '待结算条数') {
                      return '#ADD8E6';
                    } else if (xVal === '待生成格式PO条数') {
                      return '#FFDAB9';
                    } else if (xVal === '比对失败和待比对条目条数') {
                      return '#FFB6C1';
                    } else if (xVal === '已结算数量') {
                      return '#FFFACD';
                    } else if (xVal === '待结算数量') {
                      return '#FFEBCD';
                    } else if (xVal === '待生成格式PO数量') {
                      return '#FFE4E1';
                    } else if (xVal === '比对失败和待比对条目数量') {
                      return '#E0FFFF';
                    }
                  },
                ]}
                position="region*value"
              />
              <Tooltip shared />
            </Chart>
          </CardDiv>
          <CardDiv style={{ background: `rgba(37, 98, 157, 0.1)` }}>
            <h3 style={{ textAlign: 'center', fontWeight: 'bold', paddingBottom: '20px' }}>氢气的量(M3)</h3>
            <Chart height={400} padding="auto" data={handleData(dataTree2)} scale={scale} autoFit width={500}>
              <Interval
                adjust={[
                  {
                    type: 'stack',
                  },
                ]}
                color={[
                  'name',
                  (xVal) => {
                    console.log(xVal, '54325432523');
                    if (xVal === '已结算条数') {
                      return '#F0E68C';
                    } else if (xVal === '待结算条数') {
                      return '#ADD8E6';
                    } else if (xVal === '待生成格式PO条数') {
                      return '#FFDAB9';
                    } else if (xVal === '比对失败和待比对条目条数') {
                      return '#FFB6C1';
                    } else if (xVal === '已结算数量') {
                      return '#FFFACD';
                    } else if (xVal === '待结算数量') {
                      return '#FFEBCD';
                    } else if (xVal === '待生成格式PO数量') {
                      return '#FFE4E1';
                    } else if (xVal === '比对失败和待比对条目数量') {
                      return '#E0FFFF';
                    }
                  },
                ]}
                position="region*value"
              />
              <Tooltip shared />
            </Chart>
          </CardDiv>
        </div>
      </TableWrapDiv>
    </div>
  );
});
