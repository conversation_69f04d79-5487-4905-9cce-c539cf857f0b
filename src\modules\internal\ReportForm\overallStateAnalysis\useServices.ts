import { useRef, useState, useEffect } from 'react';
import {
  queryClearingInfo,
  queryProductGroup,
  querySupplierNameInfo,
  querySupplierTypeInfo,
  QueryPurchaseAnalysisList,
  QueryAllDataStatusAlyList,
  QueryAllDataStatusAlyChartsList,
  ExportAllDataStatusData,
} from '@/app/request/requestApi';
import { useLocation } from 'umi';
import { message, Form } from 'antd';
import { getNewDate } from '../../../../tools/utils';
import { getTableScroll } from '@/tools/utils';
import { name } from '@umijs/deps/compiled/mustache';
import moment from 'moment';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [orderStatus, setOrderStatus] = useState([]);
  const [supplierNameData, setSupplierNameData] = useState(<any>[]); //供应商名称
  const [supplierTypeData, setSupplierTypeData] = useState(<any>[]); //供应商类型
  const [productData, setProductData] = useState(<any>[]); //产品组
  const [queryClearingData2, setQueryClearingData2] = useState(<any>[]);
  const [dataTree, setDataTree] = useState([]);
  const [dataTree1, setDataTree1] = useState([]);
  const [dataTree2, setDataTree2] = useState([]);
  const location = useLocation<any>();

  const getNewArr = (arr) => {
    // 循环数组中的元素判断元素是否为数组
    while (arr.some((item) => Array.isArray(item))) {
      // 解构数组
      arr = [].concat(...arr);
    }
    return arr;
  };
  const getTable = () => {
    let accountPeriodStart: any;
    let accountPeriodEnd: any;
    if (!form.getFieldValue('billingDate')) {
      accountPeriodStart = moment(getNewDate('before', 3));
      accountPeriodEnd = moment(new Date());
    } else {
      accountPeriodStart = form.getFieldValue('billingDate')[0];
      accountPeriodEnd = form.getFieldValue('billingDate')[1];
    }
    QueryAllDataStatusAlyList({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
      pageIndex: current,
      pageSize: pageSize,
    }).then((res) => {
      // console.log(res.msg);
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
    QueryAllDataStatusAlyChartsList({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
    }).then((res) => {
      if (res.success) {
        const data = res.data.map((item) => [
          { name: 'toCompareAndFailCount', region: item.region, value: item.toCompareAndFailCount },
          { name: 'toBeFormatPOCount', region: item.region, value: item.toBeFormatPOCount },
          { name: 'toBeSettlCount', region: item.region, value: item.toBeSettlCount },
          { name: 'settlCount', region: item.region, value: item.settlCount },
          // { name: 'toCompareAndFailQuantity', region: item.region, value: item.toCompareAndFailQuantity },
          // { name: 'toBeFormatPOQuantity', region: item.region, value: item.toBeFormatPOQuantity },
          // { name: 'toBeSettlQuantity', region: item.region, value: item.toBeSettlQuantity },
          // { name: 'settlQuantity', region: item.region, value: item.settlQuantity },
        ]);
        setDataTree(getNewArr(data));
      }
    });
    QueryAllDataStatusAlyChartsList({
      productUnit: 'TO',
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
    }).then((res) => {
      if (res.success) {
        const data = res.data.map((item) => [
          { name: 'toCompareAndFailCount', region: item.region, value: item.toCompareAndFailCount },
          { name: 'toBeFormatPOCount', region: item.region, value: item.toBeFormatPOCount },
          { name: 'toBeSettlCount', region: item.region, value: item.toBeSettlCount },
          { name: 'settlCount', region: item.region, value: item.settlCount },
          { name: 'toCompareAndFailQuantity', region: item.region, value: item.toCompareAndFailQuantity },
          { name: 'toBeFormatPOQuantity', region: item.region, value: item.toBeFormatPOQuantity },
          { name: 'toBeSettlQuantity', region: item.region, value: item.toBeSettlQuantity },
          { name: 'settlQuantity', region: item.region, value: item.settlQuantity },
        ]);
        setDataTree1(getNewArr(data));
      } else {
      }
    });
    QueryAllDataStatusAlyChartsList({
      productUnit: 'M3',
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
    }).then((res) => {
      if (res.success) {
        const data = res.data.map((item) => [
          { name: 'toCompareAndFailCount', region: item.region, value: item.toCompareAndFailCount },
          { name: 'toBeFormatPOCount', region: item.region, value: item.toBeFormatPOCount },
          { name: 'toBeSettlCount', region: item.region, value: item.toBeSettlCount },
          { name: 'settlCount', region: item.region, value: item.settlCount },
          { name: 'toCompareAndFailQuantity', region: item.region, value: item.toCompareAndFailQuantity },
          { name: 'toBeFormatPOQuantity', region: item.region, value: item.toBeFormatPOQuantity },
          { name: 'toBeSettlQuantity', region: item.region, value: item.toBeSettlQuantity },
          { name: 'settlQuantity', region: item.region, value: item.settlQuantity },
        ]);
        setDataTree2(getNewArr(data));
      } else {
      }
    });
  };

  //导出
  const ExportAllData = () => {
    let accountPeriodStart: any;
    let accountPeriodEnd: any;
    if (!form.getFieldValue('billingDate')) {
      accountPeriodStart = moment(getNewDate('before', 3));
      accountPeriodEnd = moment(new Date());
    } else {
      accountPeriodStart = form.getFieldValue('billingDate')[0];
      accountPeriodEnd = form.getFieldValue('billingDate')[1];
    }
    ExportAllDataStatusData({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999999,
    }).then((res: any) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '整体数据状态分析.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };

  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    //结算公司
    queryClearingInfo().then((res) => {
      console.log(res);
      if (res.success) {
        setQueryClearingData2(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //供应商类型
    querySupplierTypeInfo().then((res) => {
      if (res.success) {
        setSupplierTypeData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //供应商名称
    querySupplierNameInfo('').then((res) => {
      if (res.success) {
        setSupplierNameData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取产品组
    queryProductGroup().then((res) => {
      if (res.success) {
        setProductData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
  }, []);
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  //翻页：重置current
  const onPageChange = (pagination, filters, sorter) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };

  return {
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onPageChange,
    getTable,
    tableHeight,
    form,
    scrollY,
    queryClearingData2,
    supplierTypeData,
    supplierNameData,
    productData,
    dataTree,
    dataTree1,
    dataTree2,
    ExportAllData,
  };
};
