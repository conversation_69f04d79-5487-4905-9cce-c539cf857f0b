import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv } from './style';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  InputNumber,
  message,
  Tooltip,
} from 'antd';
import moment from 'moment';
import { Operation, SubmitTable } from './style';
import { history } from 'umi';
import useServices from './useServices';
import AuthorityComponent from '@/components/AuthorityComponent';
import { getRowSpanCount } from '@/components/StateVerification';

export default memo(function (props) {
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    form,
    orderType,
    scrollY,
    queryClearingData2,
    supplierTypeData,
    supplierNameData,
    productData,
    ExportAllData,
    setqueryrecord,
    onQueryQuantity,
    QuantityData,
    onQueryPrice,
    Pricedata,
    onBilling,
    subData,
  } = useServices(props);
  const { RangePicker } = DatePicker;
  const [isSupplement, setisSupplement] = useState(false); //数量
  const [isSupplementPrice, setisSupplementPrice] = useState(false); //价格
  const [isSupplementSub, setisSupplementSub] = useState(false); //供应商
  useEffect(() => {}, []);
  //限制时间选项不超过半年
  const [dates, setDates] = useState(null);
  const [value, setValue] = useState(null);
  //
  const disabledDate = (current) => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') > 180;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') > 180;
    return !!tooEarly || !!tooLate;
  };
  //查看对账单详情
  const columns: any = [
    {
      title: '账期',
      dataIndex: 'accountPeriod',
      key: 'accountPeriod',
      align: 'center',
      width: 100,
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: {
            rowSpan: null,
          },
        };
        obj.props.rowSpan = getRowSpanCount(data, 'accountPeriod', index);
        obj.children = <div>{record.accountPeriod}</div>;
        return obj;
      },
    },
    {
      title: '区域',
      dataIndex: 'region',
      key: 'region',
      align: 'center',
      width: 100,
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: { rowSpan: null },
        };
        obj.props.rowSpan = getRowSpanCount(data, 'region', index);
        obj.children = <div>{record.region}</div>;
        return obj;
      },
    },
    {
      title: '产品组',
      dataIndex: 'productGroup',
      key: 'productGroup',
      align: 'center',
      width: 100,
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: { rowSpan: null },
        };
        obj.props.rowSpan = getRowSpanCount(data, 'productGroup', index);
        obj.children = <div>{record.productGroup}</div>;
        return obj;
      },
    },
    {
      title: '提交总条数',
      dataIndex: 'totalCount',
      key: 'totalCount',
      align: 'center',
      width: 150,
    },
    {
      title: '一次性比对成功条数',
      dataIndex: 'comparisonSucceedCount',
      key: 'comparisonSucceedCount',
      align: 'center',
      width: 170,
    },
    {
      title: '数量比对失败',
      children: [
        {
          title: '失败条数',
          dataIndex: 'quantityFailedCount',
          key: 'quantityFailedCount',
          width: 100,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onQuantityDetails(record, 1)}>
                {value}
              </span>
            );
          },
        },
        {
          title: '总失败次数',
          dataIndex: 'quantityFailedCount',
          key: 'quantityFailedCount',
          width: 100,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onQuantityDetails(record, 2)}>
                {value}
              </span>
            );
          },
        },
        {
          title: 'KPI失败次数',
          dataIndex: 'kpiQuantityFailedTimes',
          key: 'kpiQuantityFailedTimes',
          width: 110,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onQuantityDetails(record, 3)}>
                {value}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '价格比对失败',
      children: [
        {
          title: '失败条数',
          dataIndex: 'priceFailedCount',
          key: 'priceFailedCount',
          width: 100,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onPriceDetails(record, 1)}>
                {value}
              </span>
            );
          },
        },
        {
          title: '总失败次数',
          dataIndex: 'priceFailedTimes',
          key: 'priceFailedTimes',
          width: 100,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onPriceDetails(record, 2)}>
                {value}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '供应商原因被退回',
      children: [
        {
          title: '退回条数(数量)',
          dataIndex: 'quantityReturnCount',
          key: 'quantityReturnCount',
          width: 130,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 1)}>
                {value}
              </span>
            );
          },
        },
        {
          title: '总退回次数(数量)',
          dataIndex: 'quantityReturnTimes',
          key: 'quantityReturnTimes',
          width: 150,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 2)}>
                {value}
              </span>
            );
          },
        },
        {
          title: '退回条数(价格)',
          dataIndex: 'priceReturnCount',
          key: 'priceReturnCount',
          width: 150,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 3)}>
                {value}
              </span>
            );
          },
        },
        {
          title: '总退回次数(价格)',
          dataIndex: 'priceReturnTimes',
          key: 'priceReturnTimes',
          width: 150,
          render: (value, record, index) => {
            return (
              <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 4)}>
                {value}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '待生成格式PO&待结算金额',
      dataIndex: 'formatPoAndToBeSettleAmount',
      key: 'formatPoAndToBeSettleAmount',
      align: 'center',
      width: 250,
    },
    {
      title: '已结算金额',
      dataIndex: 'settleAmount',
      key: 'settleAmount',
      align: 'center',
      width: 150,
    },
    {
      title: '比对失败和待比对条目条数',
      dataIndex: 'toCompareAndFailCount',
      key: 'toCompareAndFailCount',
      align: 'center',
      width: 250,
    },
    {
      title: '待生成格式PO条数',
      dataIndex: 'toBeFormatPOCount',
      key: 'toBeFormatPOCount',
      align: 'center',
      width: 170,
    },
    {
      title: '待结算条数',
      dataIndex: 'toBeSettleCount',
      key: 'toBeSettleCount',
      align: 'center',
      width: 150,
    },
    {
      title: '已结算条数',
      dataIndex: 'settleCount',
      key: 'settleCount',
      align: 'center',
      width: 150,
    },
    {
      title: '比对失败和待比对条目数量',
      dataIndex: 'toCompareAndFailQuantity',
      key: 'toCompareAndFailQuantity',
      align: 'center',
      width: 210,
    },
    {
      title: '待生成格式PO数量',
      dataIndex: 'toBeFormatPOQuantity',
      key: 'toBeFormatPOQuantity',
      align: 'center',
      width: 160,
    },
    {
      title: '待结算数量',
      dataIndex: 'toBeSettlQuantity',
      key: 'toBeSettlQuantity',
      align: 'center',
      width: 150,
    },
    {
      title: '已结算数量',
      dataIndex: 'settlQuantity',
      key: 'settlQuantity',
      align: 'center',
      width: 150,
    },
  ];
  //查看对账单详情
  const onDetails = (billingId: string) => {
    history.push({
      pathname: '/pto/internal/StatementDetails',
      state: { id: billingId, name: 'sign' },
    });
  };
  //数量比对失败明细
  const quantityColumns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.billingDetailId)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      align: 'center',
      width: 100,
    },
    {
      title: '结算公司',
      dataIndex: 'clearingCompanyName',
      key: 'clearingCompanyName',
      align: 'center',
      width: 100,
    },
    {
      title: '日期',
      dataIndex: 'billingDate',
      key: 'billingDate',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      align: 'center',
      width: 100,
    },
    {
      title: '货源点',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      align: 'center',
      width: 100,
    },
    {
      title: '车牌号',
      dataIndex: 'carNo',
      key: 'carNo',
      align: 'center',
      width: 100,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      width: 100,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      width: 100,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      width: 100,
    },
    {
      title: '含税单价',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税单价',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '含税金额',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税金额',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号/DN#',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
      width: 100,
    },
    {
      title: '车队负责人',
      dataIndex: 'fleetLeader',
      key: 'fleetLeader',
      align: 'center',
      width: 100,
    },
    {
      title: '总失败次数',
      dataIndex: 'totalFailedTimes',
      key: 'totalFailedTimes',
      align: 'center',
      width: 100,
    },
    {
      title: 'KPI失败次数',
      dataIndex: 'kpiFailedTimes',
      key: 'kpiFailedTimes',
      align: 'center',
      width: 100,
    },
  ];
  //价格比对失败明细
  const peiceColumns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.billingDetailId)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      align: 'center',
      width: 100,
    },
    {
      title: '结算公司',
      dataIndex: 'clearingCompanyName',
      key: 'clearingCompanyName',
      align: 'center',
      width: 100,
    },
    {
      title: '日期',
      dataIndex: 'billingDate',
      key: 'billingDate',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      align: 'center',
      width: 100,
    },
    {
      title: '货源点',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      align: 'center',
      width: 100,
    },
    {
      title: '车牌号',
      dataIndex: 'carNo',
      key: 'carNo',
      align: 'center',
      width: 100,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      width: 100,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      width: 100,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      width: 100,
    },
    {
      title: '含税单价',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税单价',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '含税金额',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税金额',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号/DN#',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
      width: 100,
    },
    {
      title: 'PM采购负责人',
      dataIndex: 'pmPurchaseLeader',
      key: 'pmPurchaseLeader',
      align: 'center',
      width: 100,
    },
    {
      title: '总失败次数',
      dataIndex: 'totalFailedTimes',
      key: 'totalFailedTimes',
      align: 'center',
      width: 100,
    },
  ];
  //供应商失败明细
  const supColumns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.billingDetailId)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      align: 'center',
      width: 100,
    },
    {
      title: '结算公司',
      dataIndex: 'clearingCompanyName',
      key: 'clearingCompanyName',
      align: 'center',
      width: 100,
    },
    {
      title: '日期',
      dataIndex: 'billingDate',
      key: 'billingDate',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      align: 'center',
      width: 100,
    },
    {
      title: '货源点',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      align: 'center',
      width: 100,
    },
    {
      title: '车牌号',
      dataIndex: 'carNo',
      key: 'carNo',
      align: 'center',
      width: 100,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      width: 100,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      width: 100,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      width: 100,
    },
    {
      title: '含税单价',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税单价',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '含税金额',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税金额',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号/DN#',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
      width: 100,
    },
    {
      title: '总退回次数',
      dataIndex: 'returnTimes',
      key: 'returnTimes',
      align: 'center',
      width: 100,
    },
  ];
  //关闭数量详情
  const onClose = () => {
    setisSupplement(false);
  };
  //点击数量详情
  const onQuantityDetails = (record, state) => {
    setqueryrecord(record);
    onQueryQuantity(record, state);
    setisSupplement(true);
  };
  //关闭价格详情
  const onClosePrice = () => {
    setisSupplementPrice(false);
  };
  //点击价格详情
  const onPriceDetails = (record, state) => {
    setqueryrecord(record);
    onQueryPrice(record, state);
    setisSupplementPrice(true);
  };
  //关闭供应商详情
  const onCloseSub = () => {
    setisSupplementSub(false);
  };
  //点击供应商详情
  const onSupDetails = (record, state) => {
    setqueryrecord(record);
    onBilling(record, state);
    setisSupplementSub(true);
  };
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ span: 7 }} form={form}>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="billingDate" label="账期年月">
                <RangePicker
                  disabledDate={disabledDate}
                  onCalendarChange={(val) => setDates(val)}
                  onChange={(val) => setValue(val)}
                  separator="-"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="region" label="区域">
                <Select mode="multiple" placeholder="请选择" allowClear>
                  <Select.Option value="东区">东区</Select.Option>
                  <Select.Option value="北区">北区</Select.Option>
                  <Select.Option value="西南区">西南区</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierType" label="供应商类型">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {supplierTypeData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierType}>
                        {x.supplierType}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="supplierName" label="供应商名称">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {supplierNameData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierName}>
                        {x.supplierName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="lindeClearingCompany" label="结算公司">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {queryClearingData2.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.name}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="modeTransport" label="运输方式">
                <Select mode="multiple" placeholder="请选择" allowClear>
                  <Select.Option value="自提">自提</Select.Option>
                  <Select.Option value="送货">送货</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="productGroup" label="产品组">
                <Select mode="multiple" placeholder="请选择" showSearch allowClear>
                  {productData.map((item, index) => {
                    return (
                      <Select.Option key={index} value={item.productGroup}>
                        {item.productGroup}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Operation>
                <AuthorityComponent type="Billingmanagement-Search">
                  <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                    搜索
                  </Button>
                </AuthorityComponent>
                <AuthorityComponent type="Billingmanagement-Export">
                  <Button onClick={ExportAllData} type="primary" className="searchBut">
                    导出
                  </Button>
                </AuthorityComponent>
              </Operation>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          pagination={{
            total: total,
            current: current,
            pageSize: pageSize,
            // showSizeChanger: true,
          }}
          onChange={onPageChange}
          columns={columns}
          dataSource={data}
          bordered
          scroll={{ x: columns?.length * 160, y: 535 }}
        />
      </TableWrapDiv>
      <Modal
        width={'60wh'}
        title={<div style={{ textAlign: 'center', fontWeight: 'bold' }}>数量比对失败明细</div>}
        visible={isSupplement}
        onCancel={onClose}
        footer
      >
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          pagination={
            {
              // showSizeChanger: true,
            }
          }
          columns={quantityColumns}
          dataSource={QuantityData}
          bordered
          scroll={{ x: columns?.length * 160, y: 535 }}
        />
      </Modal>
      <Modal
        width={'60wh'}
        title={<div style={{ textAlign: 'center', fontWeight: 'bold' }}>价格比对失败明细</div>}
        visible={isSupplementPrice}
        onCancel={onClosePrice}
        footer
      >
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          pagination={
            {
              // showSizeChanger: true,
            }
          }
          columns={peiceColumns}
          dataSource={Pricedata}
          bordered
          scroll={{ x: columns?.length * 160, y: 535 }}
        />
      </Modal>
      <Modal
        width={'60wh'}
        title={<div style={{ textAlign: 'center', fontWeight: 'bold' }}>供应商原因被退回账单明细（数量原因）</div>}
        visible={isSupplementSub}
        onCancel={onCloseSub}
        footer
      >
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          pagination={
            {
              // showSizeChanger: true,
            }
          }
          columns={supColumns}
          dataSource={subData}
          bordered
          scroll={{ x: columns?.length * 160, y: 535 }}
        />
      </Modal>
    </div>
  );
});
