import { useRef, useState, useEffect } from 'react';
import {
  QueryAllDataQualityAlyList,
  queryClearingInfo,
  queryProductGroup,
  querySupplierNameInfo,
  querySupplierTypeInfo,
  ExportAllDataQualityData,
  QueryQuantityCompareFailedDetail,
  QueryPriceCompareFailedDetail,
  BillingReturnDetailDo,
} from '@/app/request/requestApi';
import { useLocation } from 'umi';
import { message, Form } from 'antd';
import { getNewDate, getTableScroll } from '@/tools/utils';
import moment from 'moment';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [orderStatus, setOrderStatus] = useState([]);
  const [queryrecord, setqueryrecord] = useState(null);
  const [Pricedata, setPricedata] = useState([]); //价格比对失败明细
  const [QuantityData, setQuantityData] = useState([]); //数量详情数据
  const [subData, setSubData] = useState([]);
  const [supplierNameData, setSupplierNameData] = useState(<any>[]); //供应商名称
  const [supplierTypeData, setSupplierTypeData] = useState(<any>[]); //供应商类型
  const [productData, setProductData] = useState(<any>[]); //产品组
  const [queryClearingData2, setQueryClearingData2] = useState(<any>[]);
  const location = useLocation<any>();

  const getTable = () => {
    let accountPeriodStart: any;
    let accountPeriodEnd: any;
    if (!form.getFieldValue('billingDate')) {
      accountPeriodStart = moment(getNewDate('before', 3));
      accountPeriodEnd = moment(new Date());
    } else {
      accountPeriodStart = form.getFieldValue('billingDate')[0];
      accountPeriodEnd = form.getFieldValue('billingDate')[1];
    }
    QueryAllDataQualityAlyList({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
      pageIndex: current,
      pageSize: pageSize,
    }).then((res) => {
      console.log(res.msg);
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
  };
  //数量比对失败明细
  const onQueryQuantity = (record, state) => {
    console.log(queryrecord);
    QueryQuantityCompareFailedDetail({
      ...record,
      ...form.getFieldsValue(),
      QuantityCompareStatistics: state,
      pageIndex: 1,
      pageSize: 9999999,
    }).then((res) => {
      if (res.success) {
        setQuantityData(res.data);
      } else {
      }
    });
  };
  //价格比失败明细
  const onQueryPrice = (record, state) => {
    QueryPriceCompareFailedDetail({
      ...record,
      ...form.getFieldsValue(),
      PriceCompareStatistics: state,
      pageIndex: 1,
      pageSize: 9999999,
    }).then((res) => {
      if (res.success) {
        setPricedata(res.data);
      } else {
      }
    });
  };
  //供应商数量原因退回
  const onBilling = (record, state) => {
    BillingReturnDetailDo({
      ...record,
      ...form.getFieldsValue(),
      ReturnStatistics: state,
      pageIndex: 1,
      pageSize: 9999999,
    }).then((res) => {
      if (res.success) {
        setSubData(res.data);
      } else {
      }
    });
  };
  //导出
  const ExportAllData = () => {
    let accountPeriodStart: any;
    let accountPeriodEnd: any;
    if (!form.getFieldValue('billingDate')) {
      accountPeriodStart = moment(getNewDate('before', 3));
      accountPeriodEnd = moment(new Date());
    } else {
      accountPeriodStart = form.getFieldValue('billingDate')[0];
      accountPeriodEnd = form.getFieldValue('billingDate')[1];
    }
    ExportAllDataQualityData({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999999,
    }).then((res: any) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '整体数据质量分析.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    //结算公司
    queryClearingInfo().then((res) => {
      console.log(res);
      if (res.success) {
        setQueryClearingData2(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //供应商类型
    querySupplierTypeInfo().then((res) => {
      if (res.success) {
        setSupplierTypeData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //供应商名称
    querySupplierNameInfo('').then((res) => {
      if (res.success) {
        setSupplierNameData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取产品组
    queryProductGroup().then((res) => {
      if (res.success) {
        setProductData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
  }, []);
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  //翻页：重置current
  const onPageChange = (pagination, filters, sorter) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };

  return {
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onPageChange,
    getTable,
    tableHeight,
    form,
    scrollY,
    queryClearingData2,
    supplierTypeData,
    supplierNameData,
    productData,
    ExportAllData,
    setqueryrecord,
    QuantityData,
    onQueryQuantity,
    onQueryPrice,
    Pricedata,
    onBilling,
    subData,
  };
};
