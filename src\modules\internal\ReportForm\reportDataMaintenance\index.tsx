import React, { useCallback, useState } from 'react';
import { SearchDiv } from '../../../../assets/style/list';
import {
  Col,
  DatePicker as TDatePicker,
  Form,
  Row,
  Select,
  Input,
  Button,
  Checkbox,
  Table,
  Space,
  Popconfirm,
  InputNumber,
  message,
} from 'antd';
import TableTitle from '../../../../components/TableTitle';
import { CardTable, ButtonFooter, Picker, SubmitTable, Operation } from './style';
import {} from '../../../supplier/initstatement/style';
import useServices from './useServices';
import { randomWord, getArrayIndex, getArrDifference } from './method';
import moment from 'moment';
import { CopyOutlined, DeleteOutlined, PlusSquareOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { momenyFormat } from '@/modules/supplier/backentry/momenyFormat';

let DatePicker: any = TDatePicker;

export default (props) => {
  const {
    form,
    onSearch,
    handleBatchDelete,
    rowSelection,
    onPageChange,
    pageParams,
    total,
    current,
    pageSize,
    setDataSource,
    dataSource,
    supplierTypeData,
    supplierNameData,
    handleSave,
    queryClearingData,
    productData,
    onReportUpdate,
    deleteData,
    setNull,
    onExportProvision,
    apidata,
    companyId,
    dataindex,
    start,
    queryClearingData2,
    setFromProductGroup,
    EndData,
    settlementDetails,
  } = useServices(props);
  //保存修改后的提交数据
  const [rowData, setRowData] = useState();
  const columns: any = [
    {
      title: '序号',
      align: 'center',
      width: 50,
      fixed: 'left',
      render: (data, record, index) => {
        return <div>{getArrayIndex(dataSource, record) + 1}</div>;
      },
    },
    {
      title: '报表年月',
      align: 'center',
      dataIndex: 'yearMonth',
      key: 'yearMonth',
      width: 100,
      render: (text, record, index) => {
        return (
          <DatePicker
            picker="month"
            defaultValue={text && text != 'Invalid date' && moment(text)}
            onChange={(value) => handleSave(value, 'yearMonth', record, index, '')}
          />
        );
      },
    },
    {
      title: '计提开始日',
      align: 'center',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 100,
      render: (text, record, index) => {
        return (
          <DatePicker
            disabledDate={disabledStrDate}
            defaultValue={text && text != 'Invalid date' && moment(text)}
            onChange={(value) => handleSave(value, 'startDate', record, index, '')}
          />
        );
      },
    },
    {
      title: '计提截止日',
      align: 'center',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 100,
      render: (text, record, index) => {
        return (
          <DatePicker
            defaultValue={text && text != 'Invalid date' && moment(text)}
            onChange={(value) => handleSave(value, 'endDate', record, index, '')}
            disabledDate={disabledEndDate}
            disabled={!record.startDate}
          />
        );
      },
    },
    {
      title: '供应商名称',
      align: 'center',
      key: 'supplierName',
      dataIndex: 'supplierName',
      width: 150,
      render: (text, record, index) => {
        return (
          <Select
            showSearch
            style={{ width: '100%' }}
            defaultValue={text}
            onChange={(value, label) => {
              handleSave(value, 'supplierName', record, index, label);
            }}
          >
            {supplierNameData.map((x, index) => {
              return (
                <Select.Option key={x.id} value={x.supplierName}>
                  {x.supplierName}
                </Select.Option>
              );
            })}
          </Select>
        );
      },
    },
    {
      title: '供应商代码',
      align: 'center',
      dataIndex: 'supplierNo',
      key: 'supplierNo',
      width: 100,
    },
    {
      title: '供应商类型',
      align: 'center',
      dataIndex: 'supplierType',
      key: 'supplierType',
      width: 100,
    },
    {
      title: '产品组',
      align: 'center',
      dataIndex: 'productGroup',
      key: 'productGroup',
      width: 100,
      render: (text, record, index) => {
        return (
          <Select
            style={{ width: '100%' }}
            defaultValue={text}
            onChange={(value, lable) => {
              handleSave(value, 'productGroup', record, index, lable);
            }}
          >
            {productData.map((item, index) => {
              return (
                <Select.Option key={index} value={index}>
                  {item.productGroup}
                </Select.Option>
              );
            })}
          </Select>
        );
      },
    },
    {
      title: '产品代码',
      align: 'center',
      dataIndex: 'productGroupCode',
      key: 'productGroupCode',
      width: 150,
    },
    {
      title: '结算公司',
      align: 'center',
      dataIndex: 'clearingCompany',
      key: 'clearingCompany',
      width: 150,
      // render: (text, record, index) => {
      //   return (
      //     <Select
      //       //如果选择的那条与数据中相同则禁用
      //       disabled={getArrayIndex(dataSource, dataindex) == getArrayIndex(dataSource, record) ? companyId : true}
      //       style={{ width: '100%' }}
      //       defaultValue={text ? text : ''}
      //       value={text ? text : ''}
      //       onChange={(value) => {
      //         handleSave(value, 'clearingCompany', record, index, '');
      //       }}
      //     >
      //       {settlementDetails?.map((x, index) => {
      //         return (
      //           <Select.Option key={index} value={x.clearingCompany}>
      //             {x.clearingCompany}
      //           </Select.Option>
      //         );
      //       })}
      //     </Select>
      //   );
      // },
    },
    {
      title: '结算工厂',
      align: 'center',
      dataIndex: 'clearingFactory',
      key: 'clearingFactory',
      width: 100,
    },
    {
      title: '公司代码',
      align: 'center',
      dataIndex: 'companyCode',
      key: 'companyCode',
      width: 100,
    },
    {
      title: '区域',
      align: 'center',
      dataIndex: 'region',
      key: 'region',
      width: 100,
    },
    {
      title: '采购未入库量',
      align: 'center',
      dataIndex: 'purchaseNotInStorage',
      key: 'purchaseNotInStorage',
      width: 100,
      render: (text, record, index) => {
        return (
          <InputNumber
            defaultValue={text}
            onChange={(value) => {
              handleSave(value, 'purchaseNotInStorage', record, index, '');
            }}
          />
        );
      },
    },
    {
      title: '单位',
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
      render: (text, record, index) => {
        return (
          <Select
            defaultValue={text}
            onChange={(value, label) => {
              handleSave(value, 'unit', record, index, label);
            }}
            style={{ width: '100%' }}
          >
            <Select.Option value="M3">M3</Select.Option>
            <Select.Option value="TO">TO</Select.Option>
          </Select>
        );
      },
    },
    {
      title: '未税价格',
      align: 'center',
      dataIndex: 'priceExcludingTax',
      width: 150,
      key: 'priceExcludingTax',
      render: (text, record, index) => {
        return (
          <InputNumber
            {...NumberProps}
            style={{ width: '100%' }}
            defaultValue={text ? momenyFormat(text) : ''}
            value={text ? momenyFormat(text) : ''}
            onChange={(value) => {
              handleSave(value, 'priceExcludingTax', record, index, '');
            }}
          />
        );
      },
    },
    {
      title: '未税金额',
      align: 'center',
      dataIndex: 'amountExcludingTax',
      width: 150,
      key: 'amountExcludingTax',
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税金额',
      align: 'center',
      dataIndex: 'amountWithTax',
      width: 150,
      key: 'amountWithTax',
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '备注',
      align: 'center',
      dataIndex: 'remarks',
      width: 150,
      key: 'remarks',
      render: (text, record, index) => {
        return (
          <Input
            defaultValue={text}
            onChange={(e) => {
              handleSave(e.target.value, 'remarks', record, index, '');
            }}
          />
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: 180,
      render: (text, record, index) => {
        return (
          <Space>
            <Popconfirm
              key="del"
              title="确定删除该条记录？"
              icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() => handleDel(record)}
            >
              <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
            </Popconfirm>
            <PlusSquareOutlined
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
              onClick={() => handleAdd(record, index)}
            />
            <CopyOutlined
              onClick={() => handleCop(record, index)}
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
            />
          </Space>
        );
      },
    },
  ];
  const newData = {
    id: '',
    yearMonth: undefined,
    startDate: undefined,
    endDate: undefined,
    supplierName: '',
    supplierNo: '',
    supplierType: '',
    productGroup: '',
    productGroupCode: '',
    clearingCompany: '',
    companyCode: '',
    region: '',
    purchaseNotInStorage: '',
    unit: '',
    priceExcludingTax: '',
    amountExcludingTax: '',
    remarks: '',
    deleted: false,
    amountincludingtax: '',
  };
  //复制单条
  const handleCop = (record, index) => {
    let temp = [...dataSource];
    let copyRow = temp?.find((item, itemIndex) => item?.id == record?.id);
    temp.splice(getArrayIndex(dataSource, record) + 1, 0, { ...copyRow, id: '', key: '' });
    let result = temp.map((item, index) => {
      item.id ? (item.id = item.id) : (item.id = randomWord(32));
      return item;
    });
    setDataSource([...result]);
  };
  //+加号单条
  const handleAdd = (record, index) => {
    let temp = [...dataSource];
    temp.splice(getArrayIndex(dataSource, record) + 1, 0, { ...newData, id: '', key: '' });
    let result = temp.map((item) => {
      item.id ? (item.id = item.id) : (item.id = randomWord(32));
      return item;
    });
    setDataSource([...result]);
  };
  //删除校验是接口数据还是未提交数据
  const checkaData = (record) => {
    let temp = [...dataSource].filter((item) => record?.id != item.id);
    let result = temp.map((item) => {
      item.id = Math.random();
      return item;
    });
    setDataSource([...result]);
  };
  //删除
  const handleDel = (record) => {
    const data = getArrDifference(dataSource, apidata);
    data.length > 0 ? checkaData(record) : deleteData(record.id);
  };
  //新增单条
  const handleAddSingle = () => {
    const temp = [{ ...newData }].map((item, index) => {
      return item;
    });
    setDataSource([...temp]);
  };
  //修改提交时
  const handleSubmit = async () => {
    let data = dataSource.filter((item) => {
      return (
        item.yearMonth == null ||
        item.endDate == null ||
        item.priceExcludingTax == null ||
        item.productGroup == null ||
        item.startDate == null ||
        item.clearingCompany == null ||
        item.supplierName == null ||
        item.purchaseNotInStorage == null ||
        item.endDate == 'Invalid date' ||
        item.startDate == 'Invalid date' ||
        item.yearMonth == 'Invalid date'
      );
    });
    data.length > 0 ? message.warning('提交信息不能为空,请检查') : onReportUpdate();
  };
  //

  const disabledEndDate = (current) => {
    return current && current < start;
  };
  const disabledStrDate = (current) => {
    return current && current > EndData && EndData;
  };
  const onNull = (e) => {
    setNull(e.target.checked);
  };
  //定义InputNumber的参数校验
  const NumberProps = {
    min: '0', //最小值
    // stringMode: true, //字符值模式，开启后支持高精度小数
    step: '0.0001', //小数位数
    formatter: (value: any) => {
      //指定输入框展示值的格式
      const reg1 = `${value}`.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3');
      return momenyFormat(reg1);
      //如果不需要添加千位分隔符可以直接返回return reg1
    },
  };
  return (
    <>
      <SearchDiv>
        <Form labelCol={{ flex: '200px' }} form={form}>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="yearMonth" label="报表年月">
                <DatePicker picker="month" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="region" label="区域">
                <Select placeholder="区域" allowClear>
                  <Select.Option value="东区">东区</Select.Option>
                  <Select.Option value="北区">北区</Select.Option>
                  <Select.Option value="西南区">西南区</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="clearingCompany" label="结算公司">
                <Select placeholder="结算公司" allowClear>
                  {queryClearingData2.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.name}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="supplierType" label="供应商类型">
                <Select placeholder="供应商类型" optionFilterProp="children" allowClear>
                  {supplierTypeData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierType}>
                        {x.supplierType}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierName" label="供应商名称">
                <Select placeholder="供应商名称" showSearch allowClear>
                  {supplierNameData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierName}>
                        {x.supplierName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="productGroup" label="产品组">
                <Select
                  placeholder="产品组"
                  onChange={(lable, key) => {
                    setFromProductGroup(key?.children);
                  }}
                  allowClear
                >
                  {productData.map((item, index) => {
                    return (
                      <Select.Option key={index} value={index}>
                        {item.productGroup}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={2}></Col>
            <Col span={5}>
              <Checkbox onChange={(e) => onNull(e)}>只显示价格为空的数据</Checkbox>
            </Col>
            <Col span={8}></Col>
            <Col span={7}>
              <Operation>
                <Button type="primary" className="searchBtn" htmlType="submit" onClick={() => onSearch()}>
                  搜索
                </Button>
                <Button className="searchBut" onClick={() => onExportProvision()}>
                  导出
                </Button>
              </Operation>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <CardTable>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <TableTitle title="计提明细信息"></TableTitle>
          <div>
            {dataSource?.length <= 0 && (
              <Button type="primary" style={{ marginRight: '10px' }} onClick={handleAddSingle}>
                新增
              </Button>
            )}
            {/* <Popconfirm
              key="del"
              title="确定批量删除所选记录？"
              icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() => handleBatchDelete()}
            >
              <Button>批量删除</Button>
            </Popconfirm> */}
          </div>
        </div>
        <div>
          <SubmitTable>
            <Table
              columns={columns}
              rowKey="id"
              // rowSelection={rowSelection}
              rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
              // onChange={(pagination, filters, sorter) => {
              //   onPageChange(pagination);
              // }}
              pagination={{
                // total: total,
                // current: current,
                // pageSize: pageSize,
                showSizeChanger: true,
              }}
              onRow={(record) => {
                return {
                  onClick: () => {
                    setRowData(record);
                  },
                };
              }}
              onChange={(pagination, filters, sorter, value) => console.log(pagination)}
              dataSource={dataSource}
              scroll={{ x: columns?.length * 160, y: 355 }}
            />
          </SubmitTable>
        </div>
      </CardTable>
      <ButtonFooter>
        <Button type="primary" onClick={handleSubmit} disabled={dataSource.length <= 0}>
          提交
        </Button>
      </ButtonFooter>
    </>
  );
};
