import { useRef, useState, useCallback, useEffect } from 'react';
import {
  querySupplierNameInfo,
  querySupplierTypeInfo,
  queryClearingInfo,
  queryProvisionReportFormPage,
  queryProductGroup,
  updateProvisionData,
  deleteProvisionData,
  exportProvisionData,
  queryClearCompanyBySupplier,
  queryLoginUser,
  dataVerification,
} from '../../../../app/request/requestApi';
import { message, Form, Modal } from 'antd';
import moment from 'moment';
import { getTableScroll } from '../../../../tools/utils';
import { momenyFormat } from '@/modules/supplier/backentry/momenyFormat';
import { ExclamationCircleOutlined } from '@ant-design/icons';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [orderStatus, setOrderStatus] = useState([]);
  const [pageParams, setPageParams] = useState({ pageIndex: 1, pageSize: 10 });
  const [selectedRowKeys, setSelectedRowKeys] = useState(<any>[]);
  const [supplierTypeData, setSupplierTypeData] = useState(<any>[]); //供应商类型
  const [supplierNameData, setSupplierNameData] = useState(<any>[]); //供应商名称
  const [queryClearingData, setQueryClearingData] = useState(<any>[]); //获取结算公司
  const [settlementDetails, setSettlementDetails] = useState(null); //结算公司明细
  const [queryClearingData2, setQueryClearingData2] = useState(<any>[]);
  const [productData, setProductData] = useState(<any>[]); //产品组
  const [apidata, setApiData] = useState(<any>[]); //列表接口数据 勿删
  const [companyId, setCompanyid] = useState(true); //结算公司
  const [fromProductGroup, setFromProductGroup] = useState(null); //产品组
  const [start, setStartDate] = useState(<any>null);
  const [EndData, setStartEnd] = useState(<any>null);
  const [dataindex, setdataIndex] = useState();
  const [isNull, setNull] = useState(false);

  const [taxRate, setTaxRate] = useState(null);
  const { confirm } = Modal;
  //TEMP
  const [dataSource, setDataSource] = useState(<any>[]);
  const [search, setSearch] = useState(<any>{
    status: '',
    orderType: '',
    productCategory: '',
    keyword: '',
    searchFromDate: '',
    searchToDate: '',
  });
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys) => {
      setSelectedRowKeys([...newSelectedRowKeys]);
    },
  };
  const handleBatchDelete = useCallback(() => {
    if (selectedRowKeys?.length <= 0) {
      message.warning('请先勾选账单');
    } else {
      const temp = dataSource?.filter((item1) => {
        return !selectedRowKeys?.includes(item1.id);
      });
      temp.forEach((item, index) => (item.id = Math.random()));
      setDataSource(temp);
    }
  }, [selectedRowKeys, dataSource]);

  //表格onChange
  const handleSave = (value, field, record, index, label) => {
    const temp = [...dataSource];
    let result = temp.map((item) => {
      if (item.id == record.id) {
        item[field] = value;
        //报表年月
        if (field == 'yearMonth') {
          if (item.yearMonth == record.yearMonth) {
            item[field] = moment(value).format('YYYY-MM-DD');
          }
        }
        //计提开始日
        if (field == 'startDate') {
          setStartDate(value);
          if (item.startDate == record.startDate) {
            item[field] = moment(value).format('YYYY-MM-DD');
          }
        }
        //计提截至日
        if (field == 'endDate') {
          setStartEnd(value);
          if (item.endDate == record.endDate) {
            item[field] = moment(value).format('YYYY-MM-DD');
          }
        }
        //供应商名称
        if (field == 'supplierName') {
          setdataIndex(record);
          if (item.supplierName == record.supplierName) {
            item[field] = value;
          }
          console.log(supplierNameData);
          //遍历供应商data 如果选择的供应商与data中的供应商相同 返回对应的code和供应商类型
          let parameter_code = supplierNameData?.filter((a) => a.supplierName == value)[0].supplierNo;
          let parameter_type = supplierNameData?.filter((a) => a.supplierName == value)[0].supplierType;
          item.supplierNo = parameter_code;
          item.supplierType = parameter_type;
          item.clearingCompany = '';
          item.companyCode = '';
          item.region = '';
          queryClearCompanyBySupplier({ supplierId: label.key }).then((res) => {
            if (res.success) {
              setQueryClearingData(res.data);
              setCompanyid(false);
              console.log(res.data);
              item.companyCode = res.data?.companyCode;
              item.clearingFactory = res.data?.clearingFactory;
              item.clearingCompany = res.data?.clearingCompany;
              item.region = res.data?.region;
              setDataSource([...result]);
            } else {
              message.warning(res.msg);
            }
          });
        }
        //结算公司
        // if (field == 'clearingCompany') {
        //   if (item.clearingCompany == record.clearingCompany) {
        //     item[field] = value;
        //   }
        //   item.companyCode = settlementDetails?.companyCode;
        //   item.clearingFactory = settlementDetails?.clearingFactory;
        //   item.clearingCompany = settlementDetails?.clearingCompany;
        //   // 遍历结算公司data 如果选择的供应商与data中的供应商相同 返回对应的code和region
        //   let parameter_code = queryClearingData?.filter((a) => a.name == value)[0].code;
        //   let parameter_region = queryClearingData?.filter((a) => a.name == value)[0].region;
        //   item.companyCode = parameter_code;
        //   item.region = parameter_region;
        // }
        //采购入库量
        if (field == 'purchaseNotInStorage') {
          if (item.purchaseNotInStorage == record.purchaseNotInStorage) {
            item[field] = value;
            item.amountExcludingTax = item.priceExcludingTax * value;
            item.amountWithTax = record.priceExcludingTax * (1 + taxRate) * value;
          }
        }
        //产品组
        if (field == 'productGroup') {
          if (item.productGroup == record.productGroup) {
            item[field] = label.children;
          }
          let productCode = productData?.filter((a, index) => index == label.value)[0].productGroupCode;
          item.productGroupCode = productCode;
        }
        //未税价格
        if (field == 'priceExcludingTax') {
          item[field] = value ? `${value}`.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3') : '';
          item.amountExcludingTax = item.purchaseNotInStorage * value;
          item.amountWithTax = value * (1 + taxRate) * record.purchaseNotInStorage;
        }
        //单位
        if (field == 'unit') {
          if (item.unit == record.unit) {
            item[field] = value;
          }
        }
        //备注
        if (field == 'remarks') {
          item[field] = value;
        }
      }
      return item;
    });
    setDataSource([...result]);
  };
  //更新报表
  const onReportUpdate = () => {
    dataVerification(dataSource).then((res) => {
      if (!res.success) {
        confirm({
          title: res.msg ? `${res.msg}是否提交` : '校验正确',
          onOk() {
            updateProvisionData(dataSource).then((res) => {
              if (res.success) {
                message.success('提交成功');
              } else {
                message.warning('提交失败');
              }
            });
          },
        });
      } else {
        updateProvisionData(dataSource).then((res) => {
          if (res.success) {
            message.success('提交成功');
          } else {
            message.warning('提交失败');
          }
        });
      }
    });
  };
  //删除报表
  const deleteData = (id) => [
    deleteProvisionData(id).then((res) => {
      if (res.success) {
        message.success('删除成功');
        getTable();
      } else {
        message.warning('删除失败');
      }
    }),
  ];
  //导出
  const onExportProvision = () => {
    const Month = form.getFieldValue('yearMonth') ? moment(form.getFieldValue('yearMonth')).format('YYYY-MM') : '';
    exportProvisionData({
      pageIndex: 1,
      pageSize: 99999,
      isNull,
      yearMonth: Month,
      productGroup: fromProductGroup,
      ...form.getFieldsValue(['region', 'clearingCompany', 'supplierType', 'supplierName']),
    }).then((res: any) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '计提明细信息.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  //列表接口
  const getTable = () => {
    const Month = form.getFieldValue('yearMonth') ? moment(form.getFieldValue('yearMonth')).format('YYYY-MM') : '';
    queryProvisionReportFormPage({
      pageIndex: 1,
      pageSize: 99999,
      isNull,
      yearMonth: Month,
      productGroup: fromProductGroup,
      ...form.getFieldsValue(['region', 'clearingCompany', 'supplierType', 'supplierName']),
    }).then((res) => {
      if (res.success) {
        setDataSource(res.data);
        setApiData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
  };
  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = <any>[];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      // 清除订阅
      window.removeEventListener('resize', resize);
    };
  }, []);
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  useEffect(() => {
    //供应商类型
    querySupplierTypeInfo().then((res) => {
      if (res.success) {
        setSupplierTypeData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //供应商名称
    querySupplierNameInfo('').then((res) => {
      if (res.success) {
        setSupplierNameData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取结算公司代码
    queryClearingInfo().then((res) => {
      console.log(res);
      if (res.success) {
        setQueryClearingData2(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取产品组
    queryProductGroup().then((res) => {
      if (res.success) {
        setProductData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取税率
    queryLoginUser().then((res) => {
      if (res.success) {
        setTaxRate(res.data.taxRate);
      }
    });
  }, []);
  //翻页：重置current
  const onPageChange = (pagination) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const onValuesChange = (changedValues, allValues) => {
    setSearch({ ...allValues });
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };
  return {
    search,
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onValuesChange,
    onPageChange,
    getTable,
    tableHeight,
    form,
    scrollY,
    selectedRowKeys,
    setDataSource,
    dataSource,
    pageParams,
    rowSelection,
    handleBatchDelete,
    supplierTypeData,
    supplierNameData,
    handleSave,
    queryClearingData,
    productData,
    onReportUpdate,
    deleteData,
    setNull,
    onExportProvision,
    apidata,
    companyId,
    dataindex,
    queryClearingData2,
    start,
    setFromProductGroup,
    EndData,
    settlementDetails,
  };
};
