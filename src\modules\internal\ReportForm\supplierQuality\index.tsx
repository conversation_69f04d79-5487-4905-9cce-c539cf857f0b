import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv } from './style';
import { Form, Select, Row, Col, Button, Table, DatePicker, Modal, message } from 'antd';
import moment from 'moment';
import { Operation } from './style';
import { history } from 'umi';
import useServices from './useServices';
import { getRowSpanCount } from '@/components/StateVerification';
import { chartData, showOptionLabel } from '@/components/StateVerification';
import { Axis, Bar, Chart, Coord, Legend, Tooltip, Interval } from 'viser-react';
import AuthorityComponent from '@/components/AuthorityComponent';
import { QuerySupplierDataAnalysisList, QuerySupplierDataChartsList } from '@/app/request/requestApi';
const DataSet = require('@antv/data-set');

export default memo(function (props) {
  const {
    // data,
    // current,
    pageSize,
    // total,
    // onPageChange,
    // onSearch,
    form,
    orderType,
    scrollY,
    queryClearingData2,
    supplierTypeData,
    supplierNameData,
    productData,
    // vehicleData,
    subData,
    setqueryrecord,
    onBilling,
    ExportAllData,
  } = useServices(props);
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '70px' },
  };
  const { RangePicker } = DatePicker;
  const [isSupplementSub, setisSupplementSub] = useState(false); //供应商
  //限制时间选项不超过半年
  const [dates, setDates] = useState(null);
  const [value, setValue] = useState(null);
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [vehicleData, setVehicleData] = useState([]);
  const [trendsColumns, setTrendsColumns] = useState(null); //车队动态表格title
  //
  const disabledDate = (current) => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') > 180;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') > 180;
    return !!tooEarly || !!tooLate;
  };
  useEffect(() => {}, []);
  //查看对账单详情

  const columns: any = [
    {
      title: '供应商信息',
      children: [
        {
          title: '供应商类型',
          dataIndex: 'supplierType',
          key: 'supplierType',
          align: 'center',
          width: 150,
          render: (value, record, index) => {
            const obj = {
              children: value,
              props: {},
            };
            obj.props.rowSpan = getRowSpanCount(data, 'supplierType', index);
            obj.children = <div>{record.supplierType}</div>;
            return obj;
          },
        },
        {
          title: '供应商名称',
          dataIndex: 'supplierName',
          key: 'supplierName',
          align: 'center',
          width: 150,
        },
      ],
    },
  ];
  //查看对账单详情
  const onDetails = (billingId: string) => {
    history.push({
      pathname: '/pto/internal/StatementDetails',
      state: { id: billingId, name: 'sign' },
    });
  };
  //供应商失败明细
  const supColumns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.billingDetailId)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      align: 'center',
      width: 100,
    },
    {
      title: '结算公司',
      dataIndex: 'clearingCompanyName',
      key: 'clearingCompanyName',
      align: 'center',
      width: 100,
    },
    {
      title: '日期',
      dataIndex: 'billingDate',
      key: 'billingDate',
      align: 'center',
      width: 100,
    },
    {
      title: '运输方式',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      align: 'center',
      width: 100,
    },
    {
      title: '货源点',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      align: 'center',
      width: 100,
    },
    {
      title: '车牌号',
      dataIndex: 'carNo',
      key: 'carNo',
      align: 'center',
      width: 100,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      width: 100,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      width: 100,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      width: 100,
    },
    {
      title: '含税单价',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税单价',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '含税金额',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '不含税金额',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      align: 'center',
      width: 100,
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      align: 'center',
      width: 100,
    },
    {
      title: '单据尾号/DN#',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
      width: 100,
    },
    {
      title: '总退回次数',
      dataIndex: 'returnTimes',
      key: 'returnTimes',
      align: 'center',
      width: 100,
    },
  ];
  //关闭供应商详情
  const onCloseSub = () => {
    setisSupplementSub(false);
  };
  //点击供应商详情
  const onSupDetails = (record, state) => {
    setqueryrecord(record);
    onBilling(record, state);
    setisSupplementSub(true);
  };
  //G2
  const dv = new DataSet.View().source(vehicleData);
  dv.transform({
    type: 'fold',
    fields: ['totalCount', 'returnCount', 'quantityReturnTimes', 'priceReturnTimes'],
    key: 'type',
    value: 'value',
    screenX: 'year',
    color: ({ value }) => {},
  });
  //处理数据
  const handleData = (data) => {
    const dataSource = data.map((item) => {
      console.log(item);
      return {
        label: `${item.region}-${item.accountPeriod}`,
        type: showOptionLabel(chartData, item.type),
        value: item.value,
      };
    });
    return dataSource;
  };
  const dataCree = handleData(dv.rows);
  const getTable = () => {
    let accountPeriodStart: any;
    let accountPeriodEnd: any;
    if (!form.getFieldValue('billingDate')) {
      accountPeriodStart = '';
      accountPeriodEnd = '';
    } else {
      accountPeriodStart = form.getFieldValue('billingDate')[0];
      accountPeriodEnd = form.getFieldValue('billingDate')[1];
    }
    QuerySupplierDataAnalysisList({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
      pageIndex: current,
      pageSize: pageSize,
    }).then((res) => {
      // console.log(res.msg);
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
        if (res.data.length > 0) {
          res.data.map((item, index) => {
            const test = item.statisticsDataList.map((x, index) => {
              return [
                {
                  title: x.accountPeriod,
                  dataIndex: x.accountPeriod,
                  key: x.accountPeriod,
                  children: [
                    {
                      title: '总条数',
                      dataIndex: 'totalCount',
                      key: 'totalCount',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 5)}>
                            {record.statisticsDataList[index]?.totalCount}
                          </span>
                        );
                      },
                    },
                    {
                      title: '退回条数（数量）',
                      dataIndex: 'quantityReturnCount',
                      key: 'quantityReturnCount',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 1)}>
                            {record.statisticsDataList[index]?.quantityReturnCount}
                          </span>
                        );
                      },
                    },
                    {
                      title: '退回次数（数量）',
                      dataIndex: 'quantityReturnTimes',
                      key: 'quantityReturnTimes',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 2)}>
                            {record.statisticsDataList[index]?.quantityReturnTimes}
                          </span>
                        );
                      },
                    },
                    {
                      title: '退回条数（价格）',
                      dataIndex: 'priceReturnCount',
                      key: 'priceReturnCount',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 3)}>
                            {record.statisticsDataList[index]?.priceReturnCount}
                          </span>
                        );
                      },
                    },
                    {
                      title: '退回次数（价格）',
                      dataIndex: 'priceReturnTimes',
                      key: 'priceReturnTimes',
                      align: 'center',
                      width: 150,
                      render: (value, record, index2) => {
                        return (
                          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onSupDetails(record, 4)}>
                            {record.statisticsDataList[index]?.priceReturnTimes}
                          </span>
                        );
                      },
                    },
                  ],
                },
              ];
            });
            setTrendsColumns([...columns, ...test.flat()].flat());
          });
        } else {
          setTrendsColumns([...columns]);
        }
      } else {
        message.warning(res.msg);
      }
    });
    QuerySupplierDataChartsList({
      accountPeriodStart,
      accountPeriodEnd,
      ...form.getFieldsValue(),
    }).then((res) => {
      if (res.success) {
        setVehicleData(res.data);
      }
    });
  };
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  const onPageChange = (pagination, filters, sorter) => {
    setCurrent(pagination.current);
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="billingDate" label="账期年月">
                <RangePicker
                  disabledDate={disabledDate}
                  onCalendarChange={(val) => setDates(val)}
                  onChange={(val) => setValue(val)}
                  separator="-"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="region" label="区域">
                <Select mode="multiple" placeholder="请选择" allowClear>
                  <Select.Option value="东区">东区</Select.Option>
                  <Select.Option value="北区">北区</Select.Option>
                  <Select.Option value="西南区">西南区</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierType" label="供应商类型">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {supplierTypeData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierType}>
                        {x.supplierType}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="supplierName" label="供应商名称">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {supplierNameData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierName}>
                        {x.supplierName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="lindeClearingCompany" label="结算公司">
                <Select mode="multiple" placeholder="请选择" allowClear showSearch>
                  {queryClearingData2.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.name}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="modeTransport" label="运输方式">
                <Select mode="multiple" placeholder="请选择" allowClear>
                  <Select.Option value="自提">自提</Select.Option>
                  <Select.Option value="送货">送货</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="productGroup" label="产品组">
                <Select mode="multiple" placeholder="请选择" showSearch allowClear>
                  {productData.map((item, index) => {
                    return (
                      <Select.Option key={index} value={item.productGroup}>
                        {item.productGroup}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Operation>
                <AuthorityComponent type="Billingmanagement-Search">
                  <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                    搜索
                  </Button>
                </AuthorityComponent>
                <AuthorityComponent type="Billingmanagement-Export">
                  <Button onClick={() => ExportAllData()} type="primary" className="searchBut">
                    导出
                  </Button>
                </AuthorityComponent>
              </Operation>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          pagination={{
            total: total,
            current: current,
            pageSize: pageSize,
            // showSizeChanger: true,
          }}
          onChange={onPageChange}
          columns={trendsColumns}
          rowKey="id"
          scroll={{ x: columns?.length * 190 }}
          dataSource={data}
          bordered
        />
      </TableWrapDiv>
      <TableWrapDiv>
        <div style={{ background: `rgba(37, 98, 157, 0.1)`, padding: '10px', borderRadius: '10px' }}>
          <Chart forceFit height={500} data={dataCree} padding={[50, 50, 0, 100]} width={800}>
            {/* 条形图 */}
            <Coord type="rect" direction="LT" />
            {/* 是否需要提示框 */}
            <Tooltip />
            {/* 是否支持下方筛选 */}
            <Legend />
            <Axis dataKey="label" position="right" />
            <Axis dataKey="label" label={{ offset: 12 }} />
            <Bar position="label*value" color="type" adjust={[{ type: 'dodge', marginRatio: 1 / 32 }]} />
          </Chart>
        </div>
      </TableWrapDiv>
      <Modal
        width={'60wh'}
        title={<div style={{ textAlign: 'center', fontWeight: 'bold' }}>供应商原因被退回账单明细（数量原因）</div>}
        visible={isSupplementSub}
        onCancel={onCloseSub}
        footer
      >
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          pagination={
            {
              // showSizeChanger: true,
            }
          }
          columns={supColumns}
          dataSource={subData}
          bordered
          scroll={{ x: columns?.length * 160, y: 535 }}
        />
      </Modal>
    </div>
  );
});
