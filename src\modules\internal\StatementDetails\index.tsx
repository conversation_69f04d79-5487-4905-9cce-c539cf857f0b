import {
  Button,
  Upload,
  Row,
  Col,
  Space,
  Form,
  Input,
  Select,
  DatePicker,
  Table,
  message,
  InputNumber,
  Popconfirm,
  Tooltip,
} from 'antd';
import React, { memo, useState, useEffect, useRef } from 'react';
import moment from 'moment';
import { ButtonFooter, CardForm, CardTable, SectionTitle, HeaderTitle } from '../../supplier/initstatement/style';
import { queryInnerHomeBillingById } from '../../../app/request/requestApi';
import { useLocation } from 'umi';
import TableTitle from '../../../components/TableTitle';
import { CopyOutlined, DeleteOutlined, PlusSquareOutlined } from '@ant-design/icons';
import { showOptionLabel, classData } from '@/components/StateVerification';
const { Option } = Select;

export default memo(function (props) {
  const [form] = Form.useForm();
  const tableRef = useRef<any>();
  const location = useLocation<any>();
  const [data, setdata] = useState();
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 24 },
  };
  const objectToFormData = (obj, form) => {
    const fd = form || new FormData();
    for (let property in obj) {
      if (obj.hasOwnProperty(property)) {
        if (typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
          objectToFormData(obj[property], fd);
        } else if (obj[property] instanceof File) {
          fd.append('file', obj[property]);
        }
      }
    }
    return fd;
  };

  const columns: any = [
    {
      title: '日期',
      align: 'center',
      key: 'billingDate',
      dataIndex: 'billingDate',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式', //必填项
      align: 'center',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      width: 100,
    },
    {
      title: '货源点/客户', //必填项
      align: 'center',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      width: 150,
    },
    {
      title: '车牌号', //必填/非必填-根据运输方式
      align: 'center',
      key: 'carNo',
      dataIndex: 'carNo',
      width: 100,
    },
    {
      title: '产品名称', //必填项
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
      width: 100,
    },
    {
      title: '数量', //必填/非必填？
      align: 'center',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 3,
              maximumFractionDigits: 3,
            }).format(text)
          : '';
      },
    },
    {
      title: '单位', //必填项,选择了TO或M3则最多3位小数，否则必须为整数
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
    },
    {
      title: '不含税单价', //必填项
      align: 'center',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 6,
              maximumFractionDigits: 6,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税单价', //必填项
      align: 'center',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4,
            }).format(text)
          : '';
      },
    },
    {
      title: '不含税金额', //计算得出
      align: 'center',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税金额', //计算得出
      align: 'center',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币',
      align: 'center',
      dataIndex: 'currency',
      key: 'currency',
      width: 100,
      render: (data, record) => {
        return data ? <div>{data}</div> : <div>CNY</div>;
      },
    },
    {
      title: '单据尾号',
      align: 'center',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      width: 100,
    },
    {
      title: '备注',
      align: 'center',
      dataIndex: 'remarks',
      width: 100,
      key: 'remarks',
    },
    {
      title: '账单进度',
      align: 'center',
      dataIndex: 'status',
      width: 100,
      key: 'status',
      render: (text, record) => {
        return (
          <div
            style={{
              background:
                record.status == '1'
                  ? 'orange'
                  : record.status == '-1'
                  ? 'red'
                  : record.status == '-2'
                  ? '#cccccc'
                  : 'green',
              color: '#fff',
              cursor: 'pointer',
              maxWidth: 150,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            }}
          >
            <Tooltip placement="top" title={text}>
              {showOptionLabel(classData, text)}
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const Upperlevel = () => {
    window.history.back();
  };

  const getDataById = () => {
    queryInnerHomeBillingById(location?.state?.id)
      .then((res) => {
        if (res.data) {
          setdata(res.data.entryList);
          console.log(res.data);
          form.setFieldsValue({
            statementNumber: res.data.statementNumber,
            region: res.data.region,
            phone: res.data.phone,
            contacts: res.data.contacts,
            supplierName: res.data.supplierName,
            supplierCode: res.data.supplierCode,
            billingEndDate: moment(res.data.billingEndDate).format('YYYY-MM-DD'),
            billingStartDate: moment(res.data.billingStartDate).format('YYYY-MM-DD'),
            submissionDate: moment(res.data.submissionDate).format('YYYY-MM-DD'),
            lindeClearingCompany: res.data.lindeClearingCompany,
          });
        } else {
          // message.error();
        }
      })
      .catch((e) => {
        console.log(e);
      });
  };
  useEffect(() => {
    if (location?.state?.id) {
      getDataById();
    }
    console.log(window.location.search.substring(1), '详情id');
  }, [location]);

  return (
    <>
      <HeaderTitle>查看对账单</HeaderTitle>
      <CardForm>
        <TableTitle title="账单基本信息"></TableTitle>
        <Form form={form} {...layout} layout="vertical">
          <Row gutter={20}>
            <Col span={6}>
              <Form.Item name="statementNumber" label="对账单号">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="submissionDate" label="提交日期">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="supplierName" label="供应商名称">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="supplierCode" label="供应商代码">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="billingStartDate" label="账单开始日期">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="billingEndDate" label="账单截至日期">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="lindeClearingCompany" label="林德结算公司">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="region" label="区域">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="contacts" label="联系人">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="phone" label="联系电话">
                <Input allowClear disabled />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </CardForm>
      <CardTable>
        <TableTitle title="账单明细信息"></TableTitle>
        <div ref={tableRef}>
          <Table
            pagination={{
              showSizeChanger: true,
            }}
            columns={columns}
            dataSource={data}
            rowKey={(record) => record?.key}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            scroll={{ x: columns?.length * 190, y: 380 }}
          />
        </div>
      </CardTable>
      <ButtonFooter>
        <Space size="large">
          <Button type="primary" onClick={() => Upperlevel()}>
            关闭
          </Button>
        </Space>
      </ButtonFooter>
    </>
  );
});
