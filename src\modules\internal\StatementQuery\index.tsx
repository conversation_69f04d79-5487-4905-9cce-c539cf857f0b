import React, { memo, useState, useEffect } from 'react';
import TableTitle from '@/components/TableTitle';
import { uploadAttachment } from '@/app/request/requestApi';
import { CloudUploadOutlined, DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  InputNumber,
  message,
  Upload,
} from 'antd';
import { history } from 'umi';
import { Operation, SearchDiv, TableWrapDiv } from '../Statement/style';
import useServices from './useServices';
import AuthorityComponent from '@/components/AuthorityComponent';
import moment from 'moment';

export default memo(function (props) {
  const {
    data,
    supplierNameData,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    btns,
    form,
    orderType,
    scrollY,
    getTable,
    uploadAttachments,
    onDeleteAttachment,
    onDownload,
  } = useServices(props);
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '70px' },
  };
  const [outlineForm] = Form.useForm();
  const [fileList, setFile] = useState<any>();
  const { RangePicker } = DatePicker;
  useEffect(() => {}, []);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [supplierNameKye, setSupplierNameKye] = useState();
  const [documentName, setDocumentName] = useState(null);
  const [FileBase64, setFileBase64] = useState(null);
  const [isDelete, setDelete] = useState(true);
  //上传附件
  const uploadFiles = () => {
    setIsModalVisible(true);
  };
  const onDetails = (url: string, text) => {
    onDownload(url, text);
  };
  //提交表单触发回调事件
  const onFinish = (values: any) => {
    console.log(outlineForm.getFieldValue('file'));
    if (outlineForm.getFieldValue('file').fileList.length > 0) {
      handleCancel();
      uploadAttachments(values, supplierNameKye, documentName, FileBase64);
    } else {
      message.warning('请选择文件上传');
    }
  };
  //删除
  const onDelete = (id) => {
    onDeleteAttachment(id);
  };
  const handleCancel = () => {
    setIsModalVisible(false);
    outlineForm.resetFields();
    setDocumentName('');
    setFileBase64('');
    setDelete(null);
  };
  //删除附件
  const onAttachmentDeletion = () => {
    setFile(null);
  };

  const handleProjectImport = (e) => {
    e.onSuccess(e.file);
    const formData = new FormData();
    objectToFormData(fileList, formData);
    uploadAttachment(formData).then((res) => {
      if (res.response.status === 200) {
        message.success('上传成功');
        console.log(res);
        setDocumentName(res.data.item2);
        setFileBase64(res.data.item1);
      } else {
      }
    });
  };
  const objectToFormData = (obj, form) => {
    const fd = form || new FormData();
    for (let property in obj) {
      if (obj.hasOwnProperty(property)) {
        // if the property is an object, but not a File, use recursivity.
        if (typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
          objectToFormData(obj[property], fd);
        } else if (obj[property] instanceof File) {
          // if it's a string or a File object
          // fd.append('file', obj[property]);
          fd.append('file', obj[property]);
        }
      }
    }
    return fd;
  };
  //删除附件校验
  const Delete = (rule, value, callback) => {
    if (value && value.fileList.length > 0) {
      callback();
    } else {
      callback('请选择文件上传');
    }
    callback();
  };
  const columns: any = [
    {
      title: '附件名称',
      dataIndex: 'fileName',
      key: 'fileName',
      align: 'center',
      width: 200,
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.filePath, text)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '年份',
      dataIndex: 'year',
      key: 'year',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY') : '';
      },
    },
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('MM') : '';
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '供应商编号',
      dataIndex: 'supplierNo',
      key: 'supplierNo',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '上传日期',
      dataIndex: 'created',
      key: 'created',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, record) => (
        <AuthorityComponent type="StatementQuery-Delete">
          <Popconfirm
            key="del"
            title="确定删除该条目？"
            icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
            onConfirm={() => onDelete(record.id)}
          >
            <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
          </Popconfirm>
        </AuthorityComponent>
      ),
    },
  ];
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="uploadDate" label="上传日期">
                <RangePicker separator="-" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item label="年份" name="year">
                <DatePicker picker="year" placeholder="请选则年份" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item label="月份" name="month">
                <DatePicker picker="month" format="MM" placeholder="请选则月份" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="keywords" label="关键字">
                <Input placeholder="关键字" className="keywordInput" allowClear />
              </Form.Item>
            </Col>
            <Col span={7}></Col>
            <Col span={8}>
              <Operation>
                <AuthorityComponent type="StatementQuery-Search">
                  <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                    搜索
                  </Button>
                </AuthorityComponent>
                <AuthorityComponent type="StatementQuery-Upload">
                  <Button type="primary" className="searchBut" onClick={() => uploadFiles()}>
                    上传附件
                  </Button>
                </AuthorityComponent>
              </Operation>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          dataSource={data}
          pagination={{
            total: total,
            current: current,
            pageSize: pageSize,
            showSizeChanger: true,
          }}
          onChange={onPageChange}
          columns={columns}
          scroll={{ x: columns?.length * 160 }}
          rowKey="id"
        />
        <Modal
          title={<div style={{ textAlign: 'center', fontWeight: 700 }}>上传附件</div>}
          footer
          visible={isModalVisible}
          onCancel={handleCancel}
          width={600}
        >
          <Form {...layout} onFinish={onFinish} form={outlineForm}>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  labelCol={{ push: 0 }}
                  label={
                    <>
                      <span style={{ color: 'red', paddingRight: '4px' }}>*</span>
                      <span>附件选择</span>
                    </>
                  }
                  name="file"
                  rules={[
                    {
                      validator: Delete,
                    },
                  ]}
                >
                  <Upload
                    accept=".xls,.xlsx"
                    customRequest={(e) => handleProjectImport(e)}
                    showUploadList={true}
                    name="file"
                    maxCount={1}
                    onChange={({ file: newFileList }) => setFile(newFileList)}
                    onRemove={() => {
                      onAttachmentDeletion();
                    }}
                  >
                    <Button>
                      <CloudUploadOutlined />
                    </Button>
                  </Upload>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item label="年份" name="year" rules={[{ required: true }]}>
                  <DatePicker picker="year" placeholder="请选则年份" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="月份" name="month" rules={[{ required: true }]}>
                  <DatePicker picker="month" placeholder="请选则月份" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="供应商" name="supplierName" rules={[{ required: true }]}>
                  <Select
                    placeholder="供应商"
                    onChange={(e, label) => {
                      setSupplierNameKye(label.key);
                    }}
                  >
                    {supplierNameData.map((x, index) => {
                      return (
                        <Select.Option key={x.supplierNo} value={x.supplierName}>
                          {x.supplierName}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="备注" name="remarks">
                  <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={11} push={5}>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    提交
                  </Button>
                </Form.Item>
              </Col>
              <Col span={11} push={5}>
                <Form.Item>
                  <Button onClick={handleCancel}>取消</Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
      </TableWrapDiv>
    </div>
  );
});
