import { useRef, useState, useEffect } from 'react';
import {
  queryInnerBillingList,
  querySupplierNameInfo,
  queryAttachmentPageList,
  insertAttachmentDetail,
  deleteAttachmentDetail,
  downloadAttachment,
  uploadAttachment,
} from '@/app/request/requestApi';
import { message, Form } from 'antd';
import { getTableScroll } from '@/tools/utils';
import moment from 'moment';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [orderStatus, setOrderStatus] = useState([]);
  const [supplierNameData, setSupplierNameData] = useState(<any>[]);
  const [search, setSearch] = useState(<any>{
    status: '',
    orderType: '',
    productCategory: '',
    keyword: '',
    searchFromDate: '',
    searchToDate: '',
  });
  const [btns, setBtns] = useState([]);

  const getTable = () => {
    console.log(form.getFieldsValue());
    const { uploadDate, year, month, keywords } = form.getFieldsValue();
    console.log(year);
    let time_year = moment(year).format('YYYY');
    let time_month = moment(month).format('MM');
    queryAttachmentPageList({
      // ...form.getFieldsValue(),
      uploadDate,
      month: month ? time_month : '',
      keywords,
      year: year ? time_year : '',
      pageIndex: current,
      pageSize: pageSize,
    }).then((res) => {
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
  };
  //上传附件
  const uploadAttachments = (value, supplierNameKye, documentName, FileBase64) => {
    console.log(value);
    const { file, month, remarks, supplierName, year } = value;
    let time_year = moment(year).format('YYYY');
    let time_month = moment(month).format('MM');
    insertAttachmentDetail({
      file,
      remarks,
      supplierName,
      month: month ? time_month : '',
      year: year ? time_year : '',
      supplierNo: supplierNameKye,
      fileName: documentName,
      filePath: FileBase64,
    }).then((res) => {
      console.log(res);
      if (res.success) {
        getTable();
      }
    });
  };
  //下载附件
  const onDownload = (url, text) => {
    downloadAttachment({
      filePath: url,
    }).then((res: any) => {
      console.log(res);
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = `${text}`;
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  //删除
  const onDeleteAttachment = (id) => {
    deleteAttachmentDetail(id).then((res) => {
      if (res.success) {
        getTable();
      }
    });
  };
  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      // 清除订阅
      window.removeEventListener('resize', resize);
    };
  }, []);
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  useEffect(() => {
    querySupplierNameInfo('').then((res) => {
      if (res.success) {
        setSupplierNameData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
  }, []);
  //翻页：重置current
  const onPageChange = (pagination, filters, sorter) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const onValuesChange = (changedValues, allValues) => {
    setSearch({ ...allValues });
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };

  return {
    search,
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onValuesChange,
    onPageChange,
    getTable,
    tableHeight,
    btns,
    form,
    scrollY,
    supplierNameData,
    uploadAttachments,
    onDeleteAttachment,
    onDownload,
  };
};
