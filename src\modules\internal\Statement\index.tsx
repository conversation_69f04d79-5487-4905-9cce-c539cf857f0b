import React, { memo, useState, useEffect } from 'react';
import TableTitle from '@/components/TableTitle';
import { classList, showOptionLabel } from '../../../components/StateVerification';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  InputNumber,
  message,
  Tooltip,
  Upload,
} from 'antd';
import { history } from 'umi';
import { Operation, SearchDiv, TableWrapDiv } from './style';
import moment from 'moment';
import useServices from './useServices';
import AuthorityComponent from '@/components/AuthorityComponent';

export default memo(function (props) {
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    btns,
    form,
    orderType,
    scrollY,
    supplierNameData,
    supplierTypeData,
    queryClearingData,
    queryProductData,
    setFile,
    importExcel,
    exportReport,
  } = useServices(props);
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '70px' },
  };
  const [outlineForm] = Form.useForm();
  const { RangePicker } = DatePicker;
  //账单进度
  const BillingProgress = [
    { name: '待结算', code: '5' },
    { name: '已结算', code: '2' },
    { name: 'MTOP待确认', code: '4' },
  ];
  //运输方式
  const gettransport = [
    { name: '自提', code: '1' },
    { name: '送货', code: '2' },
  ];
  //结算公司
  const settlement = [{ name: '微钉科技', code: '1' }];
  useEffect(() => {}, []);
  //点击对账单详情
  const onDetails = (id: string) => {
    console.log(id);
    history.push({
      pathname: '/pto/internal/StatementDetails',
      state: { id: id },
    });
  };
  const formatDetails = (id: string) => {
    console.log(id);
    history.push({
      pathname: '/pto/internal/FormatpoDetails',
      state: { id: id },
    });
  };
  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const rowSelection: any = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const datasource = [
    {
      statementNumber: 'SA4143151',
      formatNo: 'PTO432542',
      supplierName: '吉林柏崎城气体',
      clearingCompanyCode: '9100',
      billingDate: '2021-4-26',
      modeTransport: '自提',
      sourcePoint: '货源点',
      productName: '食品型二氧化碳',
      productQuantity: '100',
      unit: 'TO',
      unitPriceIncludingTax: '10.00',
      amountIncludingTax: '1000.00',
      currency: 'CNY',
      billProgress: '',
      billingId: '2315d432542w35',
      status: '5',
    },
  ];
  const columns: any = [
    {
      title: '格式单号',
      dataIndex: 'formatPoNo',
      key: 'formatPoNo',
      align: 'center',
      width: 200,
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => formatDetails(record.formatPoNo)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '订单号',
      dataIndex: 'poNo',
      key: 'poNo',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '订单条目',
      dataIndex: 'poItem',
      key: 'poItem',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '结算公司代码',
      dataIndex: 'lindeClearingCompany',
      key: 'lindeClearingCompany',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '交货日期',
      dataIndex: 'documentDate',
      key: 'documentDate',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 3,
              maximumFractionDigits: 3,
            }).format(text)
          : '';
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '不含税单价',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 6,
              maximumFractionDigits: 6,
            }).format(text)
          : '';
      },
    },
    {
      title: '不含税金额',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '结算状态',
      dataIndex: 'billingStatusLabel',
      key: 'billingStatusLabel',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, record) => {
        return (
          <div
            style={{
              background:
                record.status == '1'
                  ? 'orange'
                  : record.status == '-1'
                  ? 'red'
                  : record.status == '-2'
                  ? '#cccccc'
                  : 'green',
              color: '#fff',
              cursor: 'pointer',
              maxWidth: 150,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            }}
          >
            <Tooltip placement="top" title={text}>
              {text}
            </Tooltip>
          </div>
        );
      },
    },
  ];
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="billingDate" label="账期年月">
                <DatePicker picker="month" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierType" label="供应商类型">
                <Select placeholder="供应商类型" allowClear>
                  <Select.Option value="">所有</Select.Option>
                  {supplierTypeData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierType}>
                        {x.supplierType}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierName" label="供应商名称">
                <Select placeholder="供应商名称" allowClear showSearch>
                  <Select.Option value="">所有</Select.Option>
                  {supplierNameData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierName}>
                        {x.supplierName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="lindeClearingCompany" label="结算公司">
                <Select placeholder="结算公司" showSearch allowClear>
                  <Select.Option value="">所有</Select.Option>
                  {queryClearingData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.name}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="productName" label="产品名称">
                <Select placeholder="产品名称" allowClear>
                  <Select.Option value="">所有</Select.Option>
                  {queryProductData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.productName}>
                        {x.productName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="billStatus" label="结算状态">
                <Select placeholder="结算状态" allowClear>
                  <Select.Option value="">所有</Select.Option>
                  {BillingProgress.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.code}>
                        {x.name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={7}>
              <Form.Item name="poNo" label="订单号">
                <Input placeholder="订单号" allowClear />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="keywords" label="关键字">
                <Input placeholder="关键字" className="keywordInput" allowClear />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Operation>
                <AuthorityComponent type="Statement-Search">
                  <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                    搜索
                  </Button>
                </AuthorityComponent>
                <AuthorityComponent type="Statement-Export">
                  <Button onClick={() => exportReport()} type="primary" className="searchBut">
                    导出
                  </Button>
                  {/* <Upload
                    accept=".xls,.xlsx"
                    customRequest={() => importExcel()}
                    showUploadList={false}
                    name="file"
                    onChange={({ file: newFileList }) => setFile(newFileList)}
                  >
                    <Button className="searchBot">导入</Button>
                  </Upload> */}
                </AuthorityComponent>
              </Operation>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <Table
          style={{ width: '100%' }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          dataSource={data}
          pagination={{
            total: total,
            current: current,
            pageSize: pageSize,
            showSizeChanger: true,
          }}
          onChange={onPageChange}
          columns={columns}
          scroll={{ x: columns?.length * 160, y: 520 }}
          rowKey="id"
        />
      </TableWrapDiv>
    </div>
  );
});
