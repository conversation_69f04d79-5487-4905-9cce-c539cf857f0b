import moment from 'moment';
import React from 'react';

export const columns: any = (onDetails, onStatementDetails) => [
  {
    title: '账单序列号',
    dataIndex: 'billingSerialNumber',
    key: 'billingSerialNumber',
    ellipsis: true,
    align: 'center',
    width: 120,
  },
  {
    title: '供应商类型',
    dataIndex: 'supplierType',
    key: 'supplierType',
    ellipsis: true,
    align: 'center',
    width: 120,
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    key: 'supplierName',
    ellipsis: true,
    align: 'center',
    width: 200,
  },
  {
    title: '结算公司名称',
    dataIndex: 'lindeClearingCompany',
    key: 'lindeClearingCompany',
    align: 'center',
    ellipsis: true,
    width: 200,
  },
  {
    title: '区域',
    dataIndex: 'region',
    key: 'region',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '账期开始日期',
    dataIndex: 'billingStartDate',
    key: 'billingStartDate',
    align: 'center',
    ellipsis: true,
    width: 120,
    render: (text, _record) => {
      return text ? moment(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '账期结束日期',
    dataIndex: 'billingEndDate',
    key: 'billingEndDate',
    align: 'center',
    ellipsis: true,
    width: 120,
    render: (text, _record) => {
      return text ? moment(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '提交条目数',
    dataIndex: 'submitItem',
    key: 'submitItem',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (text, record) => {
      return text == '0' ? (
        <span>-</span>
      ) : (
        <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.id, '', record)}>
          {text}
        </span>
      );
    },
  },
  {
    title: '已退回供应商',
    dataIndex: 'returnCount',
    key: 'returnCount',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (text, record) => {
      return text == '0' ? (
        <span>-</span>
      ) : (
        <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.id, '-2', record)}>
          {text}
        </span>
      );
    },
  },
  {
    title: '待比对',
    dataIndex: 'toCompareCount',
    key: 'toCompareCount',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (text, record) => {
      return text == '0' ? (
        <span>-</span>
      ) : (
        <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.id, '1', record)}>
          {text}
        </span>
      );
    },
  },
  {
    title: '比对失败',
    dataIndex: 'comparisonFailCount',
    key: 'comparisonFailCount',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (text, record) => {
      return text == '0' ? (
        <span>-</span>
      ) : (
        <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.id, '-1', record)}>
          {text}
        </span>
      );
    },
  },
  {
    title: '待生成格式PO',
    dataIndex: 'toBeFormatPOCount',
    key: 'toBeFormatPOCount',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (text, record) => {
      return text == '0' ? (
        <span>-</span>
      ) : (
        <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.id, '3', record)}>
          {text}
        </span>
      );
    },
  },
  {
    title: 'MTOP待确认',
    dataIndex: 'toBeConfirmMTOPCount',
    key: 'toBeConfirmMTOPCount',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (_text, record) => {
      return record.mtopStatementCount == '0' && record.toBeConfirmMTOPCount == '0' ? (
        <span>-</span>
      ) : (
        <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onStatementDetails(record.id, '4', record)}>
          {`${record.mtopStatementCount}/${record.toBeConfirmMTOPCount}`}
        </span>
      );
    },
  },
  {
    title: '待结算',
    dataIndex: 'toBeSettlCount',
    key: 'toBeSettlCount',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (_text, record) => {
      return record.toBeSettlCount == '0' && record.toBeSettlStatementCount == '0' ? (
        <span>-</span>
      ) : (
        <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onStatementDetails(record.id, '5', record)}>
          {`${record.toBeSettlCount}/${record.toBeSettlStatementCount}`}
        </span>
      );
    },
  },
  {
    title: '已结算',
    dataIndex: 'settlCount',
    key: 'settlCount',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (_text, record) => {
      return record.settlCount == '0' && record.settlStatementCount == '0' ? (
        <span>-</span>
      ) : (
        <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onStatementDetails(record.id, '2', record)}>
          {`${record.settlCount}/${record.settlStatementCount}`}
        </span>
      );
    },
  },
];
//账单筛选
export const options = [
  {
    label: '被退回',
    value: '-2',
  },
  {
    label: '待比对',
    value: '1',
  },
  {
    label: '比对失败',
    value: '-1',
  },
  {
    label: 'MTOP待确认',
    value: '4',
  },
  {
    label: '待生成格式PO',
    value: '3',
  },
  {
    label: '待结算',
    value: '5',
  },
  {
    label: '已结算',
    value: '2',
  },
];

//查看对账单详情 账单进度
export const BillingProgress = [
  { name: '被退回', code: '-2' },
  { name: '待比对', code: '1' },
  { name: '比对失败', code: '-1' },
  { name: '待生成格式PO', code: '3' },
];
//查看结算单详情 账单进度
export const StatementList = [
  { name: 'MTOP待确认', code: '4' },
  { name: '待结算', code: '5' },
  { name: '已结算', code: '2' },
];
//运输方式
export const gettransport = [
  { name: '自提', code: '1' },
  { name: '送货', code: '2' },
];
