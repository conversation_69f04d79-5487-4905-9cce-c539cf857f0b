import AuthorityComponent from '@/components/AuthorityComponent';
import { Tooltip, Tag } from 'antd';
import moment from 'moment';
import React from 'react';

export const detailsColumns: any = (viewStatement) => [
  {
    title: '对账单号',
    dataIndex: 'statementNumber',
    key: 'statementNumber',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, record) => {
      return (
        <AuthorityComponent type="InternalHome-SupplierAccount">
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => viewStatement(record.id)}>
            {text}
          </span>
        </AuthorityComponent>
      );
    },
  },
  {
    title: '序列号',
    dataIndex: 'serialNumber',
    key: 'serialNumber',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'created',
    key: 'created',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text ? moment(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '日期',
    dataIndex: 'billingDate',
    key: 'billingDate',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text ? moment(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '运输方式',
    dataIndex: 'modeTransport',
    key: 'modeTransport',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '货源点/客户',
    dataIndex: 'sourcePoint',
    key: 'sourcePoint',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '车牌号',
    dataIndex: 'carNo',
    key: 'carNo',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '数量',
    dataIndex: 'productQuantity',
    key: 'productQuantity',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat(undefined, {
            minimumFractionDigits: 3,
            maximumFractionDigits: 3,
          }).format(text)
        : '';
    },
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: 'STOP ID / CONFIRMATION',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return _record.goldId ? _record.goldId : _record.goldConfirmationNum;
    },
  },
  {
    title: 'airwave',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return _record.airWaveJVNo ? _record.airWaveJVNo : _record.airWaveOSNo;
    },
  },
  {
    title: '不含税单价',
    dataIndex: 'unitPriceExcludingTax',
    key: 'unitPriceExcludingTax',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 6,
            maximumFractionDigits: 6,
          }).format(text)
        : '';
    },
  },
  {
    title: '含税单价',
    dataIndex: 'unitPriceIncludingTax',
    key: 'unitPriceIncludingTax',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 4,
            maximumFractionDigits: 4,
          }).format(text)
        : '';
    },
  },
  {
    title: '不含税金额',
    dataIndex: 'amountExcludingTax',
    key: 'amountExcludingTax',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(text)
        : '';
    },
  },
  {
    title: '含税金额',
    dataIndex: 'amountIncludingTax',
    key: 'amountIncludingTax',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(text)
        : '';
    },
  },
  {
    title: '货币',
    dataIndex: 'currency',
    key: 'currency',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '单据尾号/DN#',
    dataIndex: 'documentEndNumber',
    key: 'documentEndNumber',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    key: 'remarks',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '账单进度',
    dataIndex: 'billingStatusLabel',
    key: 'billingStatusLabel',
    align: 'center',
    ellipsis: true,
    fixed: 'right',
    width: 150,
    render: (text, record) => {
      return (
        <div>
          <Tooltip placement="top" title={text}>
            <Tag
              style={{
                background:
                  record.status == '1'
                    ? 'orange'
                    : record.status == '-1'
                    ? 'red'
                    : record.status == '-2'
                    ? '#cccccc'
                    : 'green',
                color: '#fff',
                cursor: 'pointer',
                maxWidth: 150,
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
                margin: '3px',
              }}
            >
              {text}
            </Tag>
          </Tooltip>
        </div>
      );
    },
  },
];
