import React, { memo, useState } from 'react';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  DatePicker,
  Checkbox,
  Upload,
  message,
  Popconfirm,
} from 'antd';
import moment from 'moment';
import {
  opearteEntryStatus,
  getOperateList,
  importPoList,
  SubmitFormatMergePo,
  CancelFormatMergePo,
  UpdatePgrPostingDate,
} from '@/app/request/requestApi';
import { columns, options, gettransport, StatementList, BillingProgress } from './columns';
import { TableModal, SearchDiv, TableWrapDiv, Actionkey, AccTable } from './style';
import useServices from './useServices';
import StatementDetails from './StatementDetails';
import FormatpoDetails from './FormatpoDetails';
import AuthorityComponent from '@/components/AuthorityComponent';
import { detailsColumns } from './detailsColumns';
import { StatementColumns } from './statementColumns';
import { PreviewColumns } from './previewColumns';
import { transformKeySubmitValue } from '@ant-design/pro-utils';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { previewFOColumns } from './previewFOColumns';
import { MergeFormatPo, GetFormatMergePoPreview } from '@/app/request/requestApi';
import { values } from 'lodash';

export default memo(function (props) {
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    getTable,
    onSearch,
    form,
    scrollY,
    operationMtop,
    mtopData,
    mtopCount,
    InformationPO,
    FormatPoDraft,
    onCancelFormatPo,
    previewData,
    onCommitPreview,
    Statement,
    detailstotal,
    AccountStatement,
    onChangeChecked,
    supplierNameData,
    supplierTypeData,
    queryClearingData,
    queryProductData,
    accountPageSize,
    onAccountPageChange,
    onSettlementPageChange,
    MtopCurent,
    mtopPageSize,
    onToBeSettl,
    previewNot,
    exportInnerHome,
    exportFinList,
    specialStotal,
    onSpecialPageChange,
    specialCurrent,
    specialPageSize,
    previewTotal,
    previewCurrent,
    previewPageSize,
    onPreviewPageChange,
    specialData,
    pageParams,
    setPageParams,
    onEmailToFleet,
    onEmailToPurchase,
  } = useServices(props);
  const [formPO] = Form.useForm();
  const [formPgr] = Form.useForm();
  const [formDetails] = Form.useForm();
  const [FormSettlement] = Form.useForm();
  const [outlineForm] = Form.useForm();
  const [returnForm] = Form.useForm();
  ///////////////////////////
  //勾选框内容id
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  //EJV勾选
  const [selectRow, setSelectRow] = useState([]);
  //结算单详情勾选
  const [settlementKeys, setsettlementKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  //控制查看对账单弹框
  const [isModalOpen, setIsModalOpen] = useState(false);
  //查看结算单详情弹框
  const [isStatementModal, setIsStatementModal] = useState(false);
  //控制查看预览框
  const [preview, setPreview] = useState(null);
  //控制待结算按钮操作权限
  const [settled, isSettled] = useState(false);
  //区分mto 待结算 已结算 表格操作项
  const [mtop, setMtop] = useState('');
  //控制补充PO信息
  const [isSupplement, setSupplement] = useState(false);
  //控制回写PGRdata
  const [isPgrData, setPgrData] = useState(false);
  //保存查看对账单参数
  const [queryInnerHomeId, setQueryInnerHomeId] = useState();
  const [InnerHomeIdText, setInnerHomeIdText] = useState(null);
  //保存查看结算单参数
  const [StatementID, setStatementID] = useState();
  const [StatementText, setStatementText] = useState();
  //保存补充po信息id 用于提交
  const [PoInformationId, setPoInformationId] = useState();
  //保存补充信息type 区分待解算已结算
  const [InformationType, setInformationType] = useState();
  //查看对账单弹框
  const [isStatementDetails, setIsStatementDetails] = useState(false);
  //保存对账单号id
  const [OddNumbersId, setOddNumbersId] = useState('1');
  //查看结算单弹框
  const [isSettlementDocId, setIssSettlementDocId] = useState(false);
  const [isSettlementDoc, setisSettlementDoc] = useState(null);
  const [seeId, setSeeId] = useState(null);
  const [isSettlementDocText, setSettlementDocText] = useState(null);
  //数量价格比对通过理由
  const [isQuantityReason, setQuantityReason] = useState(null);
  //退回至供应商理由备注
  const [isReturnSupplier, setisReturnSupplier] = useState(null);
  //比对通过理由
  const [isPriceReason, setPriceReason] = useState(null);
  //勾选框内容
  const [Values, steValues] = useState([]);
  //接口按钮权限
  const [collections, setCollections] = useState([]);
  //只有EJV和待生成格式po下显示
  const [ishideTable, setHideTable] = useState(null);
  //record
  const [isRecord, setRecord] = useState([]);
  //上传
  const [fileList, setFile] = useState<any>();
  //结算record
  const [semRecord, setSemRecord] = useState(null);
  //对账单record
  const [homeRecord, setHomeRecord] = useState(null);
  //保存供应商类型
  let supplierType = sessionStorage.getItem('supplierType');
  //对账单详情分页
  const [ApageParams, setApageParams] = useState({ pageIndex: 1, pageSize: 10 });
  //预览FO弹框控制
  const [isPreviewFO, setPreviewFO] = useState(false);
  const [returnType, setreturnType] = useState(null);
  //预览FOdata
  const [FOdata, setFOdata] = useState([]);
  // 格式单号
  const [datalist, setdatalist] = useState(null);
  // let supplierType = 'EJV';
  //查看对账单 退出时 清空已选中的条目id 重置操作按钮
  const handleCancel = () => {
    getTable(Values);
    formDetails.resetFields();
    setSelectedRowKeys([]);
    setSelectRow([]);
    setIsModalOpen(false);
    setCollections([]);
    setHideTable(false);
    setSemRecord('');
    setHomeRecord('');
    setPreview(false);
    //对账单详情重置成第一页
    let params = { ...pageParams };
    params.pageIndex = 1;
    setPageParams(params);
  };
  //查看对账单详情
  const onDetails = (id, text, record) => {
    setHomeRecord(record);
    text == '3' && record.supplierType == 'EJV' && setHideTable(true);
    setRecord(record);
    //回显表头
    formDetails.setFieldsValue({
      billingStatus: text,
      accountPeriod: moment(record.accountPeriodStr).add(12, 'hours'),
      supplierType: record.supplierType,
      supplierName: record.supplierName,
    });
    setQueryInnerHomeId(id);
    setInnerHomeIdText(text);
    //AccountStatement(id, record.lindeClearingCompanyId, text, formDetails.getFieldValue([]), 1);
    AccountStatement(id, record, text, formDetails.getFieldValue([]));
    setIsModalOpen(true);
  };
  //查看结算单 退出时
  const onCloseStatement = () => {
    setHideTable(false);
    getTable(Values);
    setIsStatementModal(false);
    FormSettlement.resetFields();
    setsettlementKeys([]);
    setMtop('');
  };
  //查看结算单详情
  const onStatementDetails = (id, text, record) => {
    setSemRecord(record);
    //控制待结算下的操作按键
    text == '5' ? isSettled(true) : isSettled(false);
    FormSettlement.setFieldsValue({
      accountPeriod: moment(record.accountPeriodStr).add(12, 'hours'),
      supplierType: record.supplierType,
      supplierName: record.supplierName,
      billingStatus: text,
    });
    operationMtop(id, record, text, FormSettlement.getFieldValue([]), 1);
    setIsStatementModal(true);
    setMtop(text);
    setStatementID(id);
    setStatementText(text);
  };
  //点击对账单号
  const viewStatement = (id: string) => {
    setOddNumbersId(id);
    setIsStatementDetails(true);
  };
  //关闭对账单
  const onStatement = () => {
    setIsStatementDetails(false);
    let params = { ...ApageParams };
    params.pageIndex = 1;
    setApageParams(params);
  };
  const FormatPOid = () => {
    operationMtop(StatementID, semRecord, StatementText, FormSettlement.getFieldValue([]), '');
    setIssSettlementDocId(false);
  };
  //根据id查询结算单详情
  const seeStatement = (id: string, seeId: string, text: string) => {
    console.log(seeId);
    setisSettlementDoc(id);
    setSettlementDocText(text);
    setIssSettlementDocId(true);
    setSeeId(seeId);
  };
  //关闭预览
  const handlePreview = () => {
    onCancelFormatPo([...selectedRowKeys, ...selectRow], '3');
    setPreview(false);
  };
  //生成格式po
  const onGenerateFormat = () => {
    FormatPoDraft([...selectedRowKeys, ...selectRow], '3');
    setPreview(true);
  };
  //退回至供应商 数量 价格 比对通过
  const returnSupplier = (text) => {
    text == 'Return' && setisReturnSupplier(true);
    setreturnType(text);
  };
  const onreturn = (_values: any) => {
    console.log(_values, '65343352');
    onBtureturnSupplier(_values);
    setisReturnSupplier(false);
    setreturnType(null);
    returnForm.resetFields();
  };
  // 提交
  const onBtureturnSupplier = (_values) => {
    opearteEntryStatus({
      operateType: returnType,
      entryIdList: [...selectedRowKeys, ...selectRow],
      remarks: _values?.remarks ? _values.remarks : null,
      ...outlineForm.getFieldValue([]),
    }).then((res) => {
      if (res.success) {
        setQuantityReason(false);
        onDetails(queryInnerHomeId, InnerHomeIdText, isRecord);
        getOperateList({
          entryIdList: selectedRowKeys,
        }).then((res) => {
          if (res.success) {
            setCollections(res.data);
            res.data.length == 0 ? setSelectedRowKeys([]) : null;
          } else {
            setCollections([]);
          }
        });
      }
    });
  };
  const handleProjectImport = () => {
    const formData = new FormData();
    objectToFormData(fileList, formData);
    importPoList(formData)
      .then((res) => {
        if (res.success) {
          // getTable();
          message.success(res.msg);
        } else {
          message.error(res.msg);
        }
      })
      .catch((e) => {
        message.error(e);
      });
  };
  const objectToFormData = (obj, form) => {
    const fd = form || new FormData();
    for (let property in obj) {
      if (obj.hasOwnProperty(property)) {
        // if the property is an object, but not a File, use recursivity.
        if (typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
          objectToFormData(obj[property], fd);
        } else if (obj[property] instanceof File) {
          // if it's a string or a File object
          // fd.append('file', obj[property]);
          fd.append('file', obj[property]);
        }
      }
    }
    return fd;
  };
  //补充PO信息
  const editDetail = (id, type) => {
    setPoInformationId(id);
    setInformationType(type);
    setSupplement(true);
  };
  //关闭补充PO信息
  const onClose = () => {
    setSupplement(false);
    formPO.resetFields();
  };
  //关闭Pgrdata
  const onClosePgrdata = () => {
    setPgrData(false);
    formPgr.resetFields();
  };
  //点击提交补充的po信息
  const onSubmitPO = (value) => {
    InformationPO(value, PoInformationId, InformationType);
    formPO.resetFields();
    setSupplement(false);
  };
  //点击提交回写pgrdata
  const onSubmitPgr = (value) => {
    // InformationPO(value, PoInformationId, InformationType);
    UpdatePgrPostingDate({
      formatPoNo: datalist,
      ...value,
    }).then((res) => {
      if (res.success) {
        message.success('回写成功');
        operationMtop(StatementID, semRecord, StatementText, FormSettlement.getFieldValue([]), 1);
      } else {
        message.warning(res.msg);
      }
    });
    formPgr.resetFields();
    setPgrData(false);
  };
  //
  const onPgrdataList = (value) => {
    setdatalist(value);
    setPgrData(true);
  };
  // 确定生成
  const CommitPreview = () => {
    onCommitPreview(selectedRowKeys, '3', queryInnerHomeId, InnerHomeIdText);
    setPreview(false);
    getOperateList({
      entryIdList: selectedRowKeys,
    }).then((res) => {
      if (res.success) {
        setCollections(res.data);
        res.data.length == 0 ? setSelectedRowKeys([]) : null;
      } else {
        setCollections([]);
      }
    });
  };
  //首页勾选框
  const onChange = (checkedValues) => {
    steValues(checkedValues);
    onChangeChecked(checkedValues);
  };
  //数量比对通过理由
  const primaryReason = (value) => {
    setreturnType(value);
    setQuantityReason(true);
    value == 'QuantityPass' ? setPriceReason('QuantityPass') : setPriceReason('PricePass');
  };
  //关闭理由
  const onReasonsClosure = () => {
    setQuantityReason(false);
    outlineForm.resetFields();
  };
  // 退回关闭弹框
  const onReturn = () => {
    setisReturnSupplier(false);
    returnForm.resetFields();
  };
  const onReasonFrom = (_values: any) => {
    onBtureturnSupplier(_values);
    returnSupplier(isPriceReason);
    outlineForm.resetFields();
  };
  // 监听 EJV 下的Tatle
  const rowSelections = {
    selectedRowKeys: selectRow,
    onChange: (selectRow, _) => {
      setSelectRow(selectRow);
      getOperateList({
        entryIdList: [...selectedRowKeys, ...selectRow],
      }).then((res) => {
        if (res.success) {
          setCollections(res.data);
        } else {
          setCollections([]);
        }
      });
    },
    selections: [Table.SELECTION_NONE, Table.SELECTION_ALL],
  };
  //监听 内部平台 下的Table
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
      getOperateList({
        entryIdList: [...selectedRowKeys, ...selectRow],
      }).then((res) => {
        if (res.success) {
          setCollections(res.data);
        } else {
          setCollections([]);
        }
      });
    },
    onSelect: (record, selected) => {
      console.log(record, selected);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      console.log(selected, selectedRows, changeRows);
    },
    selections: [Table.SELECTION_NONE, Table.SELECTION_ALL],
  };
  //结算单详情勾选
  const onSettlementKeys = {
    selectedRowKeys: settlementKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      // 过滤掉被禁用的行
      const filteredSelectedRows = selectedRows.filter(
        (row) => row.poInvokeStatus !== 0 && row.poInvokeStatus !== 2 && row.poInvokeStatus !== 3,
      );
      setsettlementKeys(filteredSelectedRows.map((row) => row.formatPoNo));
    },
    getCheckboxProps: (record) => ({
      disabled: record.poInvokeStatus === 0 || record.poInvokeStatus === 2 || record.poInvokeStatus === 3,
      name: record.poInvokeStatus,
    }),
    selections: [Table.SELECTION_NONE, Table.SELECTION_ALL],
  };
  //下载
  const onDownload = () => {
    onToBeSettl(FormSettlement.getFieldValue([]), semRecord);
  };
  //预览FO
  const onPreview = () => {
    GetFormatMergePoPreview({
      formatPoList: settlementKeys,
      supplierType: semRecord.supplierType,
    }).then((res) => {
      if (res.success) {
        setFOdata(res.data);
      } else {
        // message.error(res.msg);
      }
    });
    setPreviewFO(true);
  };
  //关闭预览FO
  const onPreviewFO = () => {
    setPreviewFO(false);
    setsettlementKeys([]);
    CancelFormatMergePo({
      formatPoList: settlementKeys,
      supplierType: semRecord.supplierType,
    });
  };
  //生成Fo
  const onGenerateFO = () => {
    SubmitFormatMergePo({
      formatPoList: settlementKeys,
      supplierType: semRecord.supplierType,
    }).then((res) => {
      if (res.success) {
        message.success(res.msg);
        setPreviewFO(false);
        setsettlementKeys([]);
        operationMtop(StatementID, semRecord, StatementText, FormSettlement.getFieldValue([]), 1);
      } else {
      }
    });
  };
  //重新选择
  const onReselect = () => {
    setPreviewFO(false);
    setsettlementKeys([]);
    CancelFormatMergePo({
      formatPoList: settlementKeys,
      supplierType: semRecord.supplierType,
    });
  };
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={24}>
            <Col span={5}>
              <Form.Item name="accountPeriod" label="账期年月">
                <DatePicker
                  picker="month"
                  style={{ width: '100%' }}
                  onKeyDown={(e) => {
                    e.preventDefault();
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item name="supplierType" label="供应商类型">
                <Select placeholder="供应商类型" allowClear>
                  {supplierTypeData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierType}>
                        {x.supplierType}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="supplierName" label="供应商名称">
                <Select placeholder="供应商名称" allowClear showSearch>
                  {supplierNameData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.supplierName}>
                        {x.supplierName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={2} push={2}>
              <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                搜索
              </Button>
            </Col>
          </Row>
          <Row>
            <Col span={15}>
              <Form.Item label="账单筛选">
                <Checkbox.Group options={options} onChange={onChange} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <Table
          style={{ width: '100%' }}
          rowClassName={(_record, index) => (index % 2 == 0 ? '' : 'stripe')}
          dataSource={data}
          pagination={{
            total: total,
            current: current,
            pageSize: pageSize,
          }}
          onChange={onPageChange}
          columns={columns(onDetails, onStatementDetails)}
          scroll={{ y: scrollY }}
        />
        <Modal
          className="edit_box"
          title={
            <div style={{ textAlign: 'center', color: ' #005293', fontSize: '18px', fontWeight: 700 }}>
              查看对账单详情
            </div>
          }
          visible={isModalOpen}
          onCancel={handleCancel}
          width={'90%'}
          footer
          style={{
            height: '44px',
          }}
        >
          <TableModal>
            <Form labelCol={{ flex: '100px' }} form={formDetails}>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="accountPeriod"
                    label={
                      <>
                        <span>账期年月</span>
                      </>
                    }
                  >
                    <DatePicker
                      picker="month"
                      onChange={(text) => {
                        AccountStatement(queryInnerHomeId, homeRecord, text, formDetails.getFieldValue([]));
                      }}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="supplierType" label="供应商类型">
                    <Select
                      placeholder="供应商类型"
                      defaultValue="所有"
                      onChange={(text) => {
                        AccountStatement(queryInnerHomeId, homeRecord, text, formDetails.getFieldValue([]));
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {supplierTypeData.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.supplierType}>
                            {x.supplierType}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="supplierName" label="供应商名称">
                    <Select
                      showSearch
                      placeholder="供应商名称"
                      defaultValue="所有"
                      onChange={(text) => {
                        AccountStatement(queryInnerHomeId, homeRecord, text, formDetails.getFieldValue([]));
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {supplierNameData.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.supplierName}>
                            {x.supplierName}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                {/* <Col span={4}>
                  <Form.Item label="对账单号">
                    <Checkbox
                      onChange={(e) => {
                        e.target.checked ? setSelectAll(true) : setSelectAll(false);
                      }}
                    >
                      所有
                    </Checkbox>
                  </Form.Item>
                </Col> */}
                <Col span={8}>
                  <Form.Item name="modeTransport" label="运输方式">
                    <Select
                      placeholder="运输"
                      defaultValue="所有"
                      onChange={(text) => {
                        AccountStatement(queryInnerHomeId, homeRecord, text, formDetails.getFieldValue([]));
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {gettransport.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.name}>
                            {x.name}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="productName" label="产品名称">
                    <Select
                      placeholder="产品名称"
                      defaultValue="所有"
                      onChange={(text) => {
                        AccountStatement(queryInnerHomeId, homeRecord, text, formDetails.getFieldValue([]));
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {queryProductData.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.productName}>
                            {x.productName}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="billingStatus" label="账单进度">
                    <Select
                      placeholder="账单进度"
                      onChange={(text) => {
                        // sessionStorage.getItem('supplierType') == 'EJV'
                        text == '3' && supplierType == 'EJV' ? setHideTable(true) : setHideTable(false);
                        AccountStatement(queryInnerHomeId, homeRecord, text, formDetails.getFieldValue([]));
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {BillingProgress.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.code}>
                            {x.name}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </TableModal>
          <AccTable>
            <Table
              style={{ width: '100%' }}
              rowClassName={(_record, index) => (index % 2 == 0 ? '' : 'stripe')}
              dataSource={Statement}
              // pagination={{
              //   total: detailstotal,
              //   current: accountCurrent,
              //   pageSize: accountPageSize,
              //   showSizeChanger: true,
              // }}
              onChange={(pagination, filters, sorter) => {
                onAccountPageChange(pagination);
              }}
              pagination={{
                current: pageParams.pageIndex,
                pageSize: pageParams.pageSize,
                total: Statement?.length,
                showSizeChanger: true,
              }}
              columns={detailsColumns(viewStatement)}
              scroll={{ y: scrollY }}
              rowKey={'id'}
              rowSelection={rowSelection}
            />
            {/* 特殊的 */}
            {specialData?.length > 0 && ishideTable && (
              <Table
                style={{ width: '100%' }}
                rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
                columns={detailsColumns(viewStatement)}
                scroll={{ y: scrollY }}
                // pagination={{
                //   total: specialStotal,
                //   current: specialCurrent,
                //   pageSize: specialPageSize,
                //   showSizeChanger: true,
                // }}
                // onChange={onSpecialPageChange}
                rowKey="id"
                dataSource={specialData}
                rowSelection={rowSelections}
              />
            )}
          </AccTable>
          <Actionkey>
            <Button></Button>
            {collections.includes('Return') ? (
              <AuthorityComponent type="InternalHome-SupplierReturn">
                <Button type="primary" onClick={() => returnSupplier('Return')}>
                  退回至供应商
                </Button>
              </AuthorityComponent>
            ) : (
              ''
            )}
            {collections.includes('emailToFleet') && (
              <AuthorityComponent type="InternalHome-SupplierEmailToFleet">
                <Popconfirm
                  key="del"
                  title="确定发送邮件？"
                  icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                  onConfirm={() => onEmailToFleet(selectedRowKeys, selectRow)}
                >
                  <Button type="primary">邮件至车队</Button>
                </Popconfirm>
              </AuthorityComponent>
            )}
            {collections.includes('emailToProcurement') && (
              <Popconfirm
                key="del"
                title="确定发送邮件？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => onEmailToPurchase(selectedRowKeys, selectRow)}
              >
                <Button type="primary">邮件至采购</Button>
              </Popconfirm>
            )}
            {collections.includes('quantity') && (
              <AuthorityComponent type="InternalHome-SupplierQuantityPass">
                <Button onClick={() => primaryReason('QuantityPass')} type="primary">
                  数量比对通过
                </Button>
              </AuthorityComponent>
            )}
            {collections.includes('price') && (
              <AuthorityComponent type="InternalHome-SupplierPricePass">
                <Button type="primary" onClick={() => primaryReason('PricePass')}>
                  {' '}
                  价格比对通过
                </Button>
              </AuthorityComponent>
            )}
            {collections.includes('formatPo') && (
              <AuthorityComponent type="InternalHome-GenerateFormatPo">
                <Button type="primary" onClick={onGenerateFormat}>
                  生成格式PO
                </Button>
              </AuthorityComponent>
            )}
            <AuthorityComponent type="InternalHome-SupplierExport">
              <Button
                type="primary"
                className="export"
                onClick={() =>
                  exportInnerHome(homeRecord, InnerHomeIdText, selectedRowKeys, formDetails.getFieldValue([]))
                }
              >
                导出
              </Button>
            </AuthorityComponent>
          </Actionkey>
          <Modal
            title={
              <div style={{ textAlign: 'center' }}>
                {isPriceReason == 'QuantityPass' ? '数量比对通过备注' : '价格比对通过备注'}
              </div>
            }
            visible={isQuantityReason}
            onCancel={onReasonsClosure}
            footer
          >
            <Form onFinish={onReasonFrom} form={outlineForm}>
              <Form.Item label="备注" name="remarks" rules={[{ required: true }]}>
                <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
              </Form.Item>
              <Row>
                <Col span={11} push={5}>
                  <Form.Item>
                    <Button type="primary" htmlType="submit">
                      提交
                    </Button>
                  </Form.Item>
                </Col>
                <Col span={11} push={5}>
                  <Form.Item>
                    <Button onClick={onReasonsClosure}>取消</Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Modal>
          <Modal title="退回至供应商备注" visible={isReturnSupplier} onCancel={onReturn} footer>
            <Form onFinish={onreturn} form={returnForm}>
              <Form.Item label="备注" name="remarks" rules={[{ required: true }]}>
                <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
              </Form.Item>
              <Row>
                <Col span={11} push={5}>
                  <Form.Item>
                    <Button type="primary" htmlType="submit">
                      提交
                    </Button>
                  </Form.Item>
                </Col>
                <Col span={11} push={5}>
                  <Form.Item>
                    <Button onClick={onReturn}>取消</Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Modal>
        </Modal>
        <Modal
          title={<div style={{ textAlign: 'center', fontWeight: 700 }}>预览生成格式单号</div>}
          visible={preview}
          onCancel={handlePreview}
          width={'70%'}
          footer
        >
          <Table
            rowClassName={(_record, index) => (index % 2 == 0 ? '' : 'stripe')}
            columns={PreviewColumns()}
            dataSource={previewData}
            // pagination={{
            //   total: previewTotal,
            //   current: previewCurrent,
            //   pageSize: previewPageSize,
            //   showSizeChanger: true,
            // }}
            // onChange={onPreviewPageChange}
            rowKey="id"
            scroll={{ x: columns?.length * 160 }}
          />
          <Button
            type="primary"
            style={{ marginLeft: '40%', marginRight: '10%', marginTop: '10px' }}
            onClick={CommitPreview}
          >
            确认提交
          </Button>
          <Button onClick={handlePreview}>重新选择</Button>
        </Modal>
        <Modal
          title={<div style={{ textAlign: 'center', fontWeight: 700 }}>查看结算单详情</div>}
          visible={isStatementModal}
          onCancel={onCloseStatement}
          footer
          width={'90%'}
        >
          <TableModal>
            <Form labelCol={{ flex: '100px' }} form={FormSettlement}>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="accountPeriod"
                    label={
                      <>
                        <span>账期年月</span>
                      </>
                    }
                  >
                    <DatePicker
                      picker="month"
                      onChange={(text) => {
                        operationMtop(StatementID, semRecord, text, FormSettlement.getFieldValue([]), MtopCurent);
                      }}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="supplierType" label="供应商类型">
                    <Select
                      defaultValue="所有"
                      placeholder="产品名称"
                      onChange={(text) => {
                        operationMtop(StatementID, semRecord, text, FormSettlement.getFieldValue([]), MtopCurent);
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {supplierTypeData.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.supplierType}>
                            {x.supplierType}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="supplierName" label="供应商名称">
                    <Select
                      showSearch
                      defaultValue="所有"
                      placeholder="产品名称"
                      onChange={(text) => {
                        operationMtop(StatementID, semRecord, text, FormSettlement.getFieldValue([]), MtopCurent);
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {supplierNameData.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.supplierName}>
                            {x.supplierName}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item name="lindeClearingCompany" label="结算公司">
                    <Select
                      placeholder="结算公司"
                      defaultValue="所有"
                      onChange={(text) => {
                        operationMtop(StatementID, semRecord, text, FormSettlement.getFieldValue([]), MtopCurent);
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {queryClearingData.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.name}>
                            {x.name}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="productName" label="产品名称">
                    <Select
                      defaultValue="所有"
                      placeholder="产品名称"
                      onChange={(text) => {
                        operationMtop(StatementID, semRecord, text, FormSettlement.getFieldValue([]), MtopCurent);
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {queryProductData.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.productName}>
                            {x.productName}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="billingStatus" label="结算状态">
                    <Select
                      defaultValue="所有"
                      placeholder="结算状态"
                      onChange={(text) => {
                        operationMtop(StatementID, semRecord, text, FormSettlement.getFieldValue([]), MtopCurent);
                      }}
                    >
                      <Select.Option value="">所有</Select.Option>
                      {StatementList.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.code}>
                            {x.name}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </TableModal>
          <AccTable>
            <Table
              rowClassName={(_record, index) => (index % 2 == 0 ? '' : 'stripe')}
              pagination={{
                current: MtopCurent,
                pageSize: mtopPageSize,
                showSizeChanger: true,
              }}
              columns={StatementColumns(seeStatement, editDetail, mtop, onPgrdataList)}
              dataSource={mtopData}
              onChange={onSettlementPageChange}
              scroll={{ y: scrollY }}
              rowKey="formatPoNo"
              rowSelection={mtop == '5' ? onSettlementKeys : null}
            />
          </AccTable>
          <Actionkey>
            {/* 结算单显示 3.16 隐藏  */}
            {settled ? (
              <>
                <AuthorityComponent type="InternalHome-ToBeSettlCountDownload">
                  <Button type="primary" onClick={() => onDownload()}>
                    下载
                  </Button>
                </AuthorityComponent>
                <AuthorityComponent type="InternalHome-ToBeSettlCountEcho">
                  <Upload
                    accept=".xls,.xlsx"
                    customRequest={() => handleProjectImport()}
                    showUploadList={false}
                    name="file"
                    onChange={({ file: newFileList }) => setFile(newFileList)}
                  >
                    <Button type="primary">回传订单号</Button>
                  </Upload>
                </AuthorityComponent>
                <Button
                  style={{ marginLeft: '5px' }}
                  type="primary"
                  href={`${process.env.WEB_URL}${'/template/回传模板.xlsx'}`}
                >
                  回传模板下载
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    onPreview();
                  }}
                  disabled={settlementKeys?.length <= 0}
                >
                  预览FO
                </Button>
              </>
            ) : (
              <>
                <Button></Button>
              </>
            )}
            <AuthorityComponent type={'InternalHome-MtopExport'}>
              <Button
                type="primary"
                className="export"
                onClick={() => exportFinList(semRecord, StatementText, FormSettlement.getFieldValue([]))}
              >
                导出
              </Button>
            </AuthorityComponent>
            <Modal title="补充PO信息" visible={isSupplement} onCancel={onClose} footer>
              <Form labelCol={{ flex: '100px' }} onFinish={onSubmitPO} form={formPO}>
                <Row>
                  <Col span={11}>
                    <Form.Item name="poNo" label="订单号" rules={[{ required: true }]}>
                      <Input placeholder="订单号" />
                    </Form.Item>
                  </Col>
                  <Col span={11}>
                    {/* {InformationType == '4' && ( */}
                    <Form.Item name="poItem" label="订单条目" rules={[{ required: true }]}>
                      <Input placeholder="订单条目" />
                    </Form.Item>
                    {/* )} */}
                  </Col>
                  <Col span={11}>
                    <Form.Item name="pgrPostingDate" label="Pgr日期" rules={[{ required: true }]}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={10} push={5}>
                    <Button type="primary" htmlType="submit">
                      提交
                    </Button>
                  </Col>
                  <Col span={10} push={5} onClick={onClose}>
                    <Button>取消</Button>
                  </Col>
                </Row>
              </Form>
            </Modal>
            <Modal title="回写PgrData" visible={isPgrData} onCancel={onClosePgrdata} footer>
              <Form labelCol={{ flex: '100px' }} onFinish={onSubmitPgr} form={formPgr}>
                <Row>
                  <Col span={15}>
                    {/* {InformationType == '4' && ( */}
                    <Form.Item name="pgrPostingDate" label="PgrData" rules={[{ required: true }]}>
                      <DatePicker style={{ width: '100%' }}></DatePicker>
                    </Form.Item>
                    {/* )} */}
                  </Col>
                </Row>
                <Row>
                  <Col span={10} push={5}>
                    <Button type="primary" htmlType="submit">
                      提交
                    </Button>
                  </Col>
                  <Col span={10} push={5} onClick={onClose}>
                    <Button>取消</Button>
                  </Col>
                </Row>
              </Form>
            </Modal>
          </Actionkey>
        </Modal>
        {/* 查看对账单详情框 */}
        <Modal visible={isStatementDetails} footer width={'90%'} onCancel={onStatement}>
          <StatementDetails
            OddNumbersId={OddNumbersId}
            onStatement={onStatement}
            ApageParams={ApageParams}
            setApageParams={setApageParams}
          />
        </Modal>
        {/* 查看结算单详情框 */}
        <Modal footer width={'90%'} onCancel={FormatPOid} visible={isSettlementDocId}>
          <FormatpoDetails
            FormatPOid={FormatPOid}
            isSettlementDoc={isSettlementDoc}
            isSettlementDocText={isSettlementDocText}
            seeId={seeId}
            operationMtop={operationMtop}
            StatementID={StatementID}
            StatementText={StatementText}
            semRecord={semRecord}
            FormSettlement={FormSettlement.getFieldValue([])}
            MtopCurent={MtopCurent}
          />
        </Modal>
        <Modal
          footer
          width={'90%'}
          title={
            <div style={{ textAlign: 'center', color: ' #005293', fontSize: '18px', fontWeight: 700 }}>预览FO</div>
          }
          visible={isPreviewFO}
          onCancel={onPreviewFO}
        >
          <Table
            columns={previewFOColumns()}
            dataSource={FOdata}
            // onChange={onSettlementPageChange}
            scroll={{ x: columns?.length * 160 }}
            rowKey="formatPoNo"
            // rowSelection={onSettlementKeys}
          />
          <Row gutter={24}>
            <Col push={9}>
              <Button type="primary" onClick={() => onGenerateFO()}>
                生成FO
              </Button>
            </Col>
            <Col push={12}>
              <Button onClick={() => onReselect()}>重新选择</Button>
            </Col>
          </Row>
        </Modal>
      </TableWrapDiv>
    </div>
  );
});
