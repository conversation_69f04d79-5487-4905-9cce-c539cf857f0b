import moment from 'moment';

export const PreviewColumns: any = () => [
  {
    title: '格式单号',
    dataIndex: 'formatPoNo',
    key: 'formatPoNo',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    key: 'supplierName',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '结算公司代码',
    dataIndex: 'supplieCode',
    key: 'supplieCode',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '交货日期',
    dataIndex: 'documentDate',
    key: 'documentDate',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text ? moment(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '产品代码',
    dataIndex: 'productName',
    key: 'productName',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '数量',
    dataIndex: 'settlementQuantity',
    key: 'settlementQuantity',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat(undefined, {
            minimumFractionDigits: 3,
            maximumFractionDigits: 3,
          }).format(text)
        : '';
    },
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '不含税单价',
    dataIndex: 'valnPrice',
    key: 'valnPrice',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 6,
            maximumFractionDigits: 6,
          }).format(text)
        : '';
    },
  },
  {
    title: '不含税金额',
    dataIndex: 'totalValue',
    key: 'totalValue',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(text)
        : '';
    },
  },
  {
    title: '货币',
    dataIndex: 'currency',
    key: 'currency',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
];
