import moment from 'moment';

export const previewFOColumns: any = () => [
  {
    title: 'FO单号',
    dataIndex: 'ptoFoNo',
    key: 'ptoFoNo',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '行号',
    dataIndex: 'formatPoItem',
    key: 'formatPoItem',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '对账识别号',
    dataIndex: 'ptoCheckNo',
    key: 'ptoCheckNo',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '凭证日期',
    dataIndex: 'documentDatePreview',
    key: 'documentDatePreview',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text ? moment(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '格式单号',
    dataIndex: 'formatPoNo',
    key: 'formatPoNo',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    key: 'supplierName',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '结算公司代码',
    dataIndex: 'lindeClearingCompanyCode',
    key: 'lindeClearingCompanyCode',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '交货日期',
    dataIndex: 'documentDate',
    key: 'documentDate',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text ? moment(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '本月MTOP数量',
    dataIndex: 'quantity',
    key: 'quantity',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat(undefined, {
            minimumFractionDigits: 3,
            maximumFractionDigits: 3,
          }).format(text)
        : '';
    },
  },
  {
    title: '实际数量',
    dataIndex: 'actualQuantity',
    key: 'actualQuantity',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat(undefined, {
            minimumFractionDigits: 3,
            maximumFractionDigits: 3,
          }).format(text)
        : '';
    },
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: 'STOP ID / CONFIRMATION',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return _record.goldId ? _record.goldId : _record.goldConfirmationNum;
    },
  },
  {
    title: 'airwave',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return _record.airWaveJVNo ? _record.airWaveJVNo : _record.airWaveOSNo;
    },
  },
  {
    title: '不含税单价',
    dataIndex: 'valnPrice',
    key: 'valnPrice',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 6,
            maximumFractionDigits: 6,
          }).format(text)
        : '';
    },
  },
  {
    title: '不含税金额',
    dataIndex: 'totalValue',
    key: 'totalValue',
    align: 'center',
    ellipsis: true,
    width: 150,
    render: (text, _record) => {
      return text
        ? new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(text)
        : '';
    },
  },
  {
    title: '货币',
    dataIndex: 'currency',
    key: 'currency',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
];
