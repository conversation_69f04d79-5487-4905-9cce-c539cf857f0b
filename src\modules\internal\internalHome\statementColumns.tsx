import AuthorityComponent from '@/components/AuthorityComponent';
import { classData, PoInvokeStatus, showOptionLabel } from '@/components/StateVerification';
import { EditOutlined, EyeOutlined, LoginOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Space, Tag } from 'antd';
import moment from 'moment';
import React from 'react';

export const StatementColumns: any = (seeStatement, editDetail, mtop, onPgrdataList) =>
  //此处代码有待优化
  mtop == 4
    ? [
        {
          title: '格式单号',
          dataIndex: 'formatPoNo',
          key: 'formatPoNo',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, record) => {
            return (
              <span
                style={{ color: 'blue', cursor: 'pointer' }}
                onClick={() => seeStatement(record.formatPoNo, record.id, 'see')}
              >
                {text}
              </span>
            );
          },
        },
        {
          title: '供应商名称',
          dataIndex: 'supplierName',
          key: 'supplierName',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '结算公司代码',
          dataIndex: 'lindeClearingCompanyCode',
          key: 'lindeClearingCompanyCode',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '交货日期',
          dataIndex: 'documentDate',
          key: 'documentDate',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text ? moment(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          key: 'productName',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '本月MTOP数量',
          dataIndex: 'quantity',
          key: 'quantity',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat(undefined, {
                  minimumFractionDigits: 3,
                  maximumFractionDigits: 3,
                }).format(text)
              : '';
          },
        },
        {
          title: '实际数量',
          dataIndex: 'actualQuantity',
          key: 'actualQuantity',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat(undefined, {
                  minimumFractionDigits: 3,
                  maximumFractionDigits: 3,
                }).format(text)
              : '';
          },
        },
        {
          title: '单位',
          dataIndex: 'unit',
          key: 'unit',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: 'STOP ID / CONFIRMATION',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return _record.goldId ? _record.goldId : _record.goldConfirmationNum;
          },
        },
        {
          title: 'airwave',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return _record.airWaveJVNo ? _record.airWaveJVNo : _record.airWaveOSNo;
          },
        },
        {
          title: '不含税单价',
          dataIndex: 'valnPrice',
          key: 'valnPrice',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat('en-US', {
                  minimumFractionDigits: 6,
                  maximumFractionDigits: 6,
                }).format(text)
              : '';
          },
        },
        {
          title: '不含税金额',
          dataIndex: 'totalValue',
          key: 'totalValue',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }).format(text)
              : '';
          },
        },
        {
          title: '货币',
          dataIndex: 'currency',
          key: 'currency',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '结算状态',
          dataIndex: 'billingStatus',
          key: 'billingStatus',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (value, record, index) => {
            return (
              <Tag
                style={{
                  background:
                    record.status == '1'
                      ? 'orange'
                      : record.status == '-1'
                      ? 'red'
                      : record.status == '-2'
                      ? '#cccccc'
                      : 'green',
                  color: '#fff',
                  cursor: 'pointer',
                  maxWidth: 150,
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  margin: '3px',
                }}
              >
                {showOptionLabel(classData, value)}
              </Tag>
            );
          },
        },
        {
          title: '操作',
          dataIndex: 'oper',
          key: 'oper',
          align: 'center',
          ellipsis: true,
          fixed: 'right',
          width: 150,
          render: (_, record) => (
            <Space size="middle">
              {mtop == '4' ? (
                <>
                  <AuthorityComponent type="InternalHome-MtopDetails">
                    <Button type="link" onClick={() => seeStatement(record.formatPoNo, 'see')}>
                      <EyeOutlined style={{ cursor: 'pointer', fontSize: 20, color: 'orange' }} />
                    </Button>
                  </AuthorityComponent>
                  <AuthorityComponent type="InternalHome-MtopConfirm">
                    <Button
                      type="link"
                      onClick={() => {
                        seeStatement(record.formatPoNo, '', 'confirm');
                      }}
                    >
                      <LoginOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                    </Button>
                  </AuthorityComponent>
                </>
              ) : (
                <>
                  <AuthorityComponent type="InternalHome-ToBeSettlCountDetails">
                    <Button type="link" onClick={() => seeStatement(record.formatPoNo, 'see')}>
                      <EyeOutlined style={{ cursor: 'pointer', fontSize: 20, color: 'orange' }} />
                    </Button>
                  </AuthorityComponent>
                  {mtop != '2' ? (
                    <AuthorityComponent type="InternalHome-ToBeSettlCountEdit">
                      <Button type="link" onClick={() => editDetail(record.formatPoNo, '4')}>
                        <EditOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                      </Button>
                    </AuthorityComponent>
                  ) : (
                    <AuthorityComponent type="InternalHome-ToBeSettlCountEdit">
                      <Button type="link" onClick={() => editDetail(record.formatPoNo, '2')}>
                        <EditOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                      </Button>
                    </AuthorityComponent>
                  )}
                </>
              )}
            </Space>
          ),
        },
      ]
    : mtop == 2
    ? [
        {
          title: '格式单号',
          dataIndex: 'formatPoNo',
          key: 'formatPoNo',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, record) => {
            return (
              <span
                style={{ color: 'blue', cursor: 'pointer' }}
                onClick={() => seeStatement(record.formatPoNo, record.id, 'see')}
              >
                {text}
              </span>
            );
          },
        },
        {
          title: '供应商名称',
          dataIndex: 'supplierName',
          key: 'supplierName',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '结算公司代码',
          dataIndex: 'lindeClearingCompanyCode',
          key: 'lindeClearingCompanyCode',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '交货日期',
          dataIndex: 'documentDate',
          key: 'documentDate',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text ? moment(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '凭证日期',
          dataIndex: 'documentDatePreview', //和预览FO共用
          key: 'documentDatePreview',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text ? moment(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: 'PGRPostingDate',
          dataIndex: 'pgrPostingDate',
          key: 'pgrPostingDate',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text ? moment(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          key: 'productName',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '实际数量',
          dataIndex: 'actualQuantity',
          key: 'actualQuantity',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat(undefined, {
                  minimumFractionDigits: 3,
                  maximumFractionDigits: 3,
                }).format(text)
              : '';
          },
        },
        {
          title: '订单号',
          dataIndex: 'poNo',
          key: 'poNo',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '订单条目',
          dataIndex: 'poItem',
          key: 'poItem',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '单位',
          dataIndex: 'unit',
          key: 'unit',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: 'STOP ID / CONFIRMATION',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return _record.goldId ? _record.goldId : _record.goldConfirmationNum;
          },
        },
        {
          title: 'airwave',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return _record.airWaveJVNo ? _record.airWaveJVNo : _record.airWaveOSNo;
          },
        },
        {
          title: '不含税单价',
          dataIndex: 'valnPrice',
          key: 'valnPrice',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat('en-US', {
                  minimumFractionDigits: 6,
                  maximumFractionDigits: 6,
                }).format(text)
              : '';
          },
        },
        {
          title: '不含税金额',
          dataIndex: 'totalValue',
          key: 'totalValue',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }).format(text)
              : '';
          },
        },
        {
          title: '货币',
          dataIndex: 'currency',
          key: 'currency',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '结算状态',
          dataIndex: 'billingStatus',
          key: 'billingStatus',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (value, record, index) => {
            return (
              <Tag
                style={{
                  background:
                    record.status == '1'
                      ? 'orange'
                      : record.status == '-1'
                      ? 'red'
                      : record.status == '-2'
                      ? '#cccccc'
                      : 'green',
                  color: '#fff',
                  cursor: 'pointer',
                  maxWidth: 150,
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  margin: '3px',
                }}
              >
                {showOptionLabel(classData, value)}
              </Tag>
            );
          },
        },
        {
          title: '操作',
          dataIndex: 'oper',
          key: 'oper',
          align: 'center',
          ellipsis: true,
          fixed: 'right',
          width: 150,
          render: (_, record) => (
            <Space size="middle">
              {mtop == '4' ? (
                <>
                  <AuthorityComponent type="InternalHome-MtopDetails">
                    <Button type="link" onClick={() => seeStatement(record.formatPoNo, 'see')}>
                      <EyeOutlined style={{ cursor: 'pointer', fontSize: 20, color: 'orange' }} />
                    </Button>
                  </AuthorityComponent>
                  <AuthorityComponent type="InternalHome-MtopConfirm">
                    <Button
                      type="link"
                      onClick={() => {
                        seeStatement(record.formatPoNo, 'confirm');
                      }}
                    >
                      <LoginOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                    </Button>
                  </AuthorityComponent>
                </>
              ) : (
                <>
                  <AuthorityComponent type="InternalHome-ToBeSettlCountDetails">
                    <Button type="link" onClick={() => seeStatement(record.formatPoNo, 'see')}>
                      <EyeOutlined style={{ cursor: 'pointer', fontSize: 20, color: 'orange' }} />
                    </Button>
                  </AuthorityComponent>
                  {mtop != '2' ? (
                    <AuthorityComponent type="InternalHome-ToBeSettlCountEdit">
                      <Button type="link" onClick={() => editDetail(record.formatPoNo, '4')}>
                        <EditOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                      </Button>
                    </AuthorityComponent>
                  ) : (
                    <AuthorityComponent type="InternalHome-ToBeSettlCountEdit">
                      <Button type="link" onClick={() => editDetail(record.formatPoNo, '2')}>
                        <EditOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                      </Button>
                    </AuthorityComponent>
                  )}
                </>
              )}
            </Space>
          ),
        },
      ]
    : [
        {
          title: '格式单号',
          dataIndex: 'formatPoNo',
          key: 'formatPoNo',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, record) => {
            return (
              <span
                style={{ color: 'blue', cursor: 'pointer' }}
                onClick={() => seeStatement(record.formatPoNo, record.id, 'see')}
              >
                {text}
              </span>
            );
          },
        },
        {
          title: '供应商名称',
          dataIndex: 'supplierName',
          key: 'supplierName',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '结算公司代码',
          dataIndex: 'lindeClearingCompanyCode',
          key: 'lindeClearingCompanyCode',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '交货日期',
          dataIndex: 'documentDate',
          key: 'documentDate',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text ? moment(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          key: 'productName',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '实际数量',
          dataIndex: 'actualQuantity',
          key: 'actualQuantity',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat(undefined, {
                  minimumFractionDigits: 3,
                  maximumFractionDigits: 3,
                }).format(text)
              : '';
          },
        },
        {
          title: '单位',
          dataIndex: 'unit',
          key: 'unit',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: 'STOP ID / CONFIRMATION',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return _record.goldId ? _record.goldId : _record.goldConfirmationNum;
          },
        },
        {
          title: 'airwave',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return _record.airWaveJVNo ? _record.airWaveJVNo : _record.airWaveOSNo;
          },
        },
        {
          title: '不含税单价',
          dataIndex: 'valnPrice',
          key: 'valnPrice',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat('en-US', {
                  minimumFractionDigits: 6,
                  maximumFractionDigits: 6,
                }).format(text)
              : '';
          },
        },
        {
          title: '不含税金额',
          dataIndex: 'totalValue',
          key: 'totalValue',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return text
              ? new Intl.NumberFormat('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }).format(text)
              : '';
          },
        },
        {
          title: '货币',
          dataIndex: 'currency',
          key: 'currency',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '订单号',
          dataIndex: 'poNo',
          key: 'poNo',
          align: 'center',
          ellipsis: true,
          width: 150,
        },
        {
          title: '结算状态',
          dataIndex: 'billingStatus',
          key: 'billingStatus',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (value, record, index) => {
            return (
              <Tag
                style={{
                  background:
                    record.status == '1'
                      ? 'orange'
                      : record.status == '-1'
                      ? 'red'
                      : record.status == '-2'
                      ? '#cccccc'
                      : 'green',
                  color: '#fff',
                  cursor: 'pointer',
                  maxWidth: 150,
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  margin: '3px',
                }}
              >
                {showOptionLabel(classData, value)}
              </Tag>
            );
          },
        },
        {
          title: '订单同步状态',
          dataIndex: 'poInvokeStatus',
          key: 'poInvokeStatus',
          align: 'center',
          ellipsis: true,
          width: 150,
          render: (text, _record) => {
            return <Tag>{showOptionLabel(PoInvokeStatus, text)}</Tag>;
          },
        },
        {
          title: '操作',
          dataIndex: 'oper',
          key: 'oper',
          align: 'center',
          ellipsis: true,
          fixed: 'right',
          width: 200,
          render: (_, record) => (
            <>
              {mtop == '4' ? (
                <>
                  <AuthorityComponent type="InternalHome-MtopDetails">
                    <Button type="link" onClick={() => seeStatement(record.formatPoNo, 'see')}>
                      <EyeOutlined style={{ cursor: 'pointer', fontSize: 20, color: 'orange' }} />
                    </Button>
                  </AuthorityComponent>
                  <AuthorityComponent type="InternalHome-MtopConfirm">
                    <Button
                      type="link"
                      onClick={() => {
                        seeStatement(record.formatPoNo, 'confirm');
                      }}
                    >
                      <LoginOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                    </Button>
                  </AuthorityComponent>
                </>
              ) : (
                <>
                  <AuthorityComponent type="InternalHome-ToBeSettlCountDetails">
                    <Button type="link" onClick={() => seeStatement(record.formatPoNo, 'see')}>
                      <EyeOutlined style={{ cursor: 'pointer', fontSize: 20, color: 'orange' }} />
                    </Button>
                  </AuthorityComponent>
                  {/* <Button type="link">回写</Button> */}
                  {mtop != '2' ? (
                    <>
                      <AuthorityComponent type="InternalHome-ToBeSettlCountEdit">
                        <Button type="link" onClick={() => editDetail(record.formatPoNo, '4')}>
                          <EditOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                        </Button>
                      </AuthorityComponent>
                      {record.poInvokeStatus == '4' && (
                        <Button type="link" onClick={() => onPgrdataList(record.formatPoNo)}>
                          <ReloadOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#F04848' }} />
                        </Button>
                      )}
                    </>
                  ) : (
                    <>
                      <AuthorityComponent type="InternalHome-ToBeSettlCountEdit">
                        <Button type="link" onClick={() => editDetail(record.formatPoNo, '2')}>
                          <EditOutlined style={{ cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                        </Button>
                      </AuthorityComponent>
                    </>
                  )}
                </>
              )}
            </>
          ),
        },
      ];
