import styled from 'styled-components';

export const TableModal = styled.div`
  padding: 10px 0px;
`;
export const SearchDiv = styled.div`
  padding: 30px 0px 0px;
  border-radius: 10px;
  background-color: #fff;
  border-bottom: 1px solid #eaf4f6;
`;
export const TableWrapDiv = styled.div`
  margin-top: 20px;
  border-radius: 10px;
  background: #fff;
  .stripe,
  .stripe .ant-table-cell-fix-right,
  .stripe .ant-table-cell-fix-left {
    background-color: #f9f9f9;
  }
`;
export const Actionkey = styled.div`
  .export {
    float: right;
  }
  Button {
    margin-top: 25px;
    margin-left: 5px;
  }
`;
export const AccTable = styled.div`
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td,
  .ant-table tfoot > tr > th,
  .ant-table tfoot > tr > td {
    padding: 2px;
  }
  .ant-table-body {
    max-height: 350px !important;
    overflow-y: scroll;
  }
`;
