import { useRef, useState, useEffect } from 'react';
import {
  queryInnerHomeList,
  queryInnerHomeFinList,
  SubmitPoInfo,
  SubmitFormatPo,
  generateFormatPo,
  getFormatPoDraftList,
  CancelFormatPo,
  queryInnerHomeEntryList,
  querySupplierNameInfo,
  querySupplierTypeInfo,
  queryClearingInfo,
  queryProductInfo,
  downLoadToBeSettl,
  exportInnerHomeEntryList,
  queryInnerHomeSpecialEntryList,
  exportInnerHomeFinList,
  submitSettlPoInfo,
  EmailToFleet,
  EmailToPurchase,
  MergeFormatPo,
} from '@/app/request/requestApi';
import { message, Form } from 'antd';
import { useLocation } from 'umi';
import { getTableScroll } from '@/tools/utils';
import moment from 'moment';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const [data, setTableData] = useState(<any>[]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [accountCurrent, setAccountCurrent] = useState(1); //对账单详情
  const [accountPageSize, setAccountPageSize] = useState(20); //对账单详情
  const [detailstotal, setdetailsTotal] = useState(2); //对账单详情
  const [pageParams, setPageParams] = useState({ pageIndex: 1, pageSize: 10 }); //对账单自定义

  const [specialCurrent, setSpecialCurrent] = useState(1); //特殊
  const [specialPageSize, setSpecialPageSize] = useState(20); //特殊
  const [specialStotal, setSpecialStotal] = useState(2); //特殊
  const [specialData, setSpecialData] = useState(); //特殊数据

  const [previewCurrent, setPreviewCurrent] = useState(1); //预览
  const [previewPageSize, setPreviewPageSize] = useState(20);
  const [previewTotal, setPreviewTotal] = useState(2); //预览

  const [MtopCurent, setMtopCurent] = useState(1); //结算单详情
  const [mtopPageSize, setMtopPageSize] = useState(20); //结算单详情
  const [mtopCount, setMtopCount] = useState(2); //结算单详情
  const [pageSize, setPageSize] = useState(20);
  const [mtopData, setMtopData] = useState([]); //

  const [previewData, setPreviewData] = useState([]);
  const [Statement, setStatement] = useState([]);
  const [values, setValues] = useState([]);
  const [supplierNameData, setSupplierNameData] = useState(<any>[]);
  const [supplierTypeData, setSupplierTypeData] = useState(<any>[]);
  // const [supplierFrom, setSupplierFrom] = useState()
  const [queryClearingData, setQueryClearingData] = useState(<any>[]);
  const [queryProductData, setQueryProductData] = useState(<any>[]);
  const location = useLocation<any>();
  const [id, setid] = useState(); //查对账单id
  const [lindeCompanyId, setCompanyId] = useState(null);
  const [Text, setText] = useState(); //查询对账单状态
  const [supplierFrom, setSupplierFrom] = useState(); //查询对账单From
  const [Mtopid, setMtopid] = useState(); //结算单id
  const [MtopText, setMtopText] = useState(); //结算单text
  const [MtopFrom, setMtopFrom] = useState(); //结算单from
  const [record, setRecord] = useState(null); //结算单record
  const [AccRecord, setAccRecord] = useState(null); //对账单record
  const [previewNot, setPreviewNot] = useState(null); //
  const [previewText, setPreviewText] = useState({
    entryIdList: [],
    operateType: '',
  });

  const [anCurrent, setanCurrent] = useState(null); //对账单详情的下拉框
  //MTOP待确认 待结算 已结算
  const operationMtop = (id, record, text, from, mCurent) => {
    //结算id
    let lindeClearingCompanyId = record?.lindeClearingCompanyId;
    //供应商类型
    let supplierType = record?.supplierType;
    //供应商名称
    let supplierName = record?.supplierName;
    //结算公司名称
    let lindeClearingCompany = record?.lindeClearingCompany;
    //区域
    let region = record?.region;
    //账期开始日期
    let billingStartDate = record?.billingStartDate;
    //账期结束时间
    let billingEndDate = record?.billingEndDate;
    setRecord(record);
    setMtopid(id), setMtopText(text), setMtopFrom(from), setMtopCurent(mCurent != 1 && mCurent != '' ? 1 : MtopCurent);
    queryInnerHomeFinList({
      lindeClearingCompanyId,
      supplierType,
      supplierName,
      lindeClearingCompany,
      region,
      billingStartDate,
      billingEndDate,
      id,
      billingStatus: text,
      ...from,
      pageIndex: 1,
      pageSize: 10000,
    }).then((res) => {
      if (res.success) {
        setMtopData(res.data);
        setMtopCount(res.totalCount);
      }
    });
  };
  //查看对账单
  const AccountStatement = (id, record, text, from) => {
    setAccRecord(record);
    //结算id
    let lindeClearingCompanyId = record?.lindeClearingCompanyId;
    //供应商类型
    let supplierType = record?.supplierType;
    //供应商名称
    let supplierName = record?.supplierName;
    //结算公司名称
    let lindeClearingCompany = record?.lindeClearingCompany;
    //区域
    let region = record?.region;
    //账期开始日期
    let billingStartDate = record?.billingStartDate;
    //账期结束时间
    let billingEndDate = record?.billingEndDate;
    setSupplierFrom(from);
    setid(id);
    setCompanyId(record?.lindeClearingCompanyId), setText(text);
    queryInnerHomeEntryList({
      id,
      lindeClearingCompanyId,
      supplierType,
      supplierName,
      region,
      billingStartDate,
      billingEndDate,
      lindeClearingCompany,
      billingStatus: text,
      ...from,
      pageIndex: 1,
      pageSize: 10000,
    }).then((res) => {
      if (res.success) {
        setdetailsTotal(res.totalCount);
        setStatement(res.data);
      }
    });
    //特殊条目列表
    queryInnerHomeSpecialEntryList({
      id,
      lindeClearingCompanyId,
      supplierType,
      supplierName,
      region,
      billingStartDate,
      billingEndDate,
      lindeClearingCompany,
      billingStatus: text,
      ...from,
      pageIndex: 1,
      pageSize: 10000,
    }).then((res) => {
      setSpecialStotal(res.totalCount);
      setSpecialData(res.data);
    });
  };
  //邮件至车队
  const onEmailToFleet = (valueid, EJVid) => {
    EmailToFleet({
      entryIdList: [...valueid, ...EJVid],
    }).then((res) => {
      if (res.success) {
        AccountStatement(id, AccRecord, Text, supplierFrom);
        message.success('发送成功');
      } else {
        message.warning(res.msg);
      }
    });
  };
  //邮件至采购
  const onEmailToPurchase = (valueid, EJVid) => {
    EmailToPurchase({
      entryIdList: [...valueid, ...EJVid],
    }).then((res) => {
      if (res.success) {
        AccountStatement(id, AccRecord, Text, supplierFrom);
        message.success('发送成功');
      } else {
        message.warning(res.msg);
      }
    });
  };
  //提交PO信息
  const InformationPO = (value, id, type) => {
    type == '4'
      ? SubmitPoInfo({
          formatPoId: id,
          ...value,
        }).then((res) => {
          if (res.success) {
            operationMtop(Mtopid, record, MtopText, MtopFrom, 1);
            message.success(res.msg);
          } else {
            message.warning(res.msg);
          }
        })
      : submitSettlPoInfo({
          formatPoId: id,
          ...value,
        }).then((res) => {
          if (res.success) {
            operationMtop(Mtopid, record, MtopText, MtopFrom, 1);
            message.success(res.msg);
          } else {
            message.warning(res.msg);
          }
        });
  };
  //查看对账单详情导出
  const exportInnerHome = (record, text, selectedRowKeys, from) => {
    let { accountPeriod, supplierType, supplierName, modeTransport, productName, billingStatus } = from;
    let time = moment(accountPeriod).format('YYYY-MM-DD');
    console.log(from);
    exportInnerHomeEntryList({
      billingStatus,
      ...record,
      pageIndex: 1,
      pageSize: 10000,
      entryIdList: selectedRowKeys,
      accountPeriod: time,
      modeTransport,
      productName,
    }).then((res: any) => {
      if (res.response.status == 200) {
        let data = res.response.headers['content-disposition'];
        var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        let elink = document.createElement('a');
        // 设置下载文件名
        var str = decodeURIComponent(filenameRegex.exec(data)[1]);
        var result = str.replace('"', '').replace('"', '');
        elink.download = `${result}`;
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  //查看结算单详情导出
  const exportFinList = (record, text, from) => {
    let { accountPeriod, supplierType, supplierName, modeTransport, productName, billingStatus, lindeClearingCompany } =
      from;
    let time = moment(accountPeriod).format('YYYY-MM-DD');
    exportInnerHomeFinList({
      billingStatus,
      pageIndex: 1,
      pageSize: 10000,
      accountPeriod: time,
      lindeClearingCompany: lindeClearingCompany,
      productName,
      ...record,
    }).then((res: any) => {
      if (res.response.status == 200) {
        // decodeURIComponent(uri)
        let data = res.response.headers['content-disposition'];
        var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        let elink = document.createElement('a');
        // 设置下载文件名
        var str = decodeURIComponent(filenameRegex.exec(data)[1]);
        var result = str.replace('"', '').replace('"', '');
        elink.download = `${result}`;
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  //下载
  const onToBeSettl = (from, record) => {
    let { accountPeriod, supplierType, supplierName, modeTransport, productName, billingStatus, lindeClearingCompany } =
      from;
    let time = moment(accountPeriod).format('YYYY-MM-DD');
    downLoadToBeSettl({
      billingStatus,
      ...record,
      pageIndex: 1,
      pageSize: 10000,
      accountPeriod: time,
      lindeClearingCompany: lindeClearingCompany,
      productName,
    }).then((res: any) => {
      if (res.response.status == 200) {
        // decodeURIComponent(uri)
        let data = res.response.headers['content-disposition'];
        var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = `${decodeURIComponent(filenameRegex.exec(data)[1])}`;
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  //点击生成格式PO调
  const FormatPoDraft = (value, type) => {
    setPreviewText({ entryIdList: value, operateType: type });
    generateFormatPo({
      entryIdList: value,
      operateType: type,
    }).then((res) => {
      if (res.success) {
        setPreviewNot(res.success);
        getFormatPoDraftList({
          entryIdList: value,
          operateType: type,
          pageIndex: previewCurrent,
          pageSize: previewPageSize,
        }).then((res) => {
          if (res.success) {
            setPreviewData(res.data);
            setPreviewTotal(res.totalCount);
          } else {
            setPreviewData(res.data);
          }
        });
      } else {
        // message.warning(res.msg);
      }
    });
  };
  //首页勾选
  const onChangeChecked = (checkedValues) => {
    setValues(checkedValues);
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable(checkedValues);
    }
  };
  //点击确定生成
  const onCommitPreview = (value, type, id, text) => {
    SubmitFormatPo({
      entryIdList: value,
      operateType: type,
    }).then((res) => {
      if (res.success) {
        AccountStatement(id, AccRecord, text, supplierFrom);
        message.success(res.msg);
      }
    });
  };
  //重新选择 待生成格式po取消
  const onCancelFormatPo = (value, type) => {
    CancelFormatPo({
      entryIdList: value,
      operateType: type,
    });
  };
  const getTable = (checkedValues) => {
    queryInnerHomeList({
      ...form.getFieldsValue(),
      pageIndex: current,
      pageSize: pageSize,
      statusList: checkedValues,
    }).then((res) => {
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
  };

  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      // 清除订阅
      window.removeEventListener('resize', resize);
    };
  }, []);
  useEffect(() => {
    //获取供应商名称
    querySupplierNameInfo('').then((res) => {
      if (res.success) {
        setSupplierNameData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取供应商状态
    querySupplierTypeInfo().then((res) => {
      if (res.success) {
        setSupplierTypeData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取结算公司代码
    queryClearingInfo().then((res) => {
      console.log(res);
      if (res.success) {
        setQueryClearingData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    //获取产品信息名称
    queryProductInfo('').then((res) => {
      if (res.success) {
        setQueryProductData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
  }, [location]);
  useEffect(() => {
    //勾选账筛选时必传勾选id 否则翻页时重新调用列表接口 你将无法拿到你筛选后的值
    getTable(values);
  }, [current, pageSize]);
  //对账单详情
  useEffect(() => {
    AccountStatement(id, AccRecord, Text, supplierFrom);
  }, []);
  //结算单详情
  useEffect(() => {
    operationMtop(Mtopid, record, MtopText, MtopFrom, 1);
  }, [mtopPageSize, mtopCount]);

  // useEffect(() => {
  //   FormatPoDraft(previewText.entryIdList, previewText.operateType);
  // }, [previewCurrent, previewPageSize]);

  //翻页：重置current
  const onPageChange = (pagination, filters, sorter, value) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  //对账单：翻页详情 重置
  // const onAccountPageChange = (pagination, filters, sorter, value) => {
  //   setAccountCurrent(pagination.current);
  //   setAccountPageSize(pagination.pageSize);
  // };
  const onAccountPageChange = (pagination) => {
    let params = { ...pageParams };
    params.pageSize = pagination.pageSize;
    params.pageIndex = pagination.current;
    setPageParams(params);
  };
  //对账单：特殊表格翻页重置
  const onSpecialPageChange = (pagination) => {
    setSpecialCurrent(pagination.current);
    setSpecialPageSize(pagination.pageSize);
  };
  //预览格式PO
  const onPreviewPageChange = (pagination) => {
    setPreviewCurrent(pagination.current);
    setPreviewPageSize(pagination.pageSize);
  };
  //结算单：翻页详情 重置
  const onSettlementPageChange = (pagination) => {
    setMtopCurent(pagination.current);
    setMtopPageSize(pagination.pageSize);
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable(values);
    }
  };

  return {
    onSearch,
    data,
    total,
    current,
    pageSize,
    onPageChange,
    getTable,
    form,
    mtopData,
    mtopCount,
    scrollY,
    operationMtop,
    InformationPO,
    FormatPoDraft,
    onCancelFormatPo,
    previewData,
    onCommitPreview,
    Statement,
    detailstotal,
    AccountStatement,
    onChangeChecked,
    supplierNameData,
    supplierTypeData,
    queryClearingData,
    queryProductData,
    accountPageSize,
    onAccountPageChange,
    onSettlementPageChange,
    MtopCurent,
    mtopPageSize,
    onToBeSettl,
    previewNot,
    exportInnerHome,
    exportFinList,
    specialStotal,
    onSpecialPageChange,
    specialCurrent,
    specialPageSize,
    previewTotal,
    previewCurrent,
    previewPageSize,
    onPreviewPageChange,
    specialData,
    pageParams,
    setPageParams,
    onEmailToFleet,
    onEmailToPurchase,
  };
};
