import React, { ReactNode } from 'react';
import { Form } from 'antd';
// import useService from './useService';
import TableTitle from '@/components/TableTitle';

export interface formProps {
  title?: string;
  form?: any;
  children?: ReactNode;
  [restProps: string]: any;
}

export default (props: formProps) => {
  const { title, children, form, initialValues } = props;
  // const {} = useService(props);
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
    labelAlign: 'left',
  };
  return (
    <div>
      <TableTitle title={title}></TableTitle>
      <Form form={form} {...layout} initialValues={initialValues}>
        {children}
      </Form>
    </div>
  );
};
