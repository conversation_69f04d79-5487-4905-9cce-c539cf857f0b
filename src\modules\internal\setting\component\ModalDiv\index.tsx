import { But<PERSON>, Card, Col, Form, Row, Input, message, Modal, Popconfirm, Select } from 'antd';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import { ModalWrapper } from './style';

export default (props) => {
  const { title, width = 1000, visible, handleModalOk, handleModalCancel, children } = props;

  return (
    <ModalWrapper
      title={<div style={{ textAlign: 'center', fontSize: 18, fontWeight: 700 }}>{title}</div>}
      visible={visible}
      width={width}
      okText="提交"
      onOk={handleModalOk}
      onCancel={handleModalCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
    >
      {children}
    </ModalWrapper>
  );
};
