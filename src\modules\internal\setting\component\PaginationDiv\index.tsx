import { Button, Pagination, Space, Table } from 'antd';
import React, { ReactChild, ReactNode } from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { PaginationDiv } from './style';

export interface paginationProps {
  title?: string;
  align?: string;
  key?: string;
  pageIndex?: number;
  pageCount?: number;
  render?: ReactNode;
  [restProps: string]: any;
}

export default (props: paginationProps) => {
  const { pageIndex = 1, pageCount = 0, handlePrev, handleNext, children, ...restProps } = props;
  return pageCount >= 1 ? (
    <PaginationDiv>
      <Button className="paginButton" onClick={handlePrev} disabled={pageIndex === 1}>
        <LeftOutlined />
        Prev
      </Button>
      <div className="page">{`${pageIndex} / ${pageCount}`}</div>
      <Button className="paginButton" onClick={handleNext} disabled={pageIndex === pageCount}>
        Next
        <RightOutlined />
      </Button>
    </PaginationDiv>
  ) : (
    <></>
  );
};
