import { Button, Pagination, Space, Table } from 'antd';
import React, { ReactChild, ReactNode } from 'react';
import { TableWrapper, ButtonSpace } from './style';

export interface tableColumns {
  title?: string;
  align?: string;
  key?: string;
  render?: ReactNode;
  [restProps: string]: any;
}
export interface tableProps {
  rightTop?: ReactNode;
  rowKey?: (record: any) => any;
  columns?: tableColumns;
  dataSource?: Array<any>;
  children?: ReactNode;
  [restProps: string]: any;
}

export default function index(props: tableProps) {
  const { rightTop, columns, dataSource, rowKey, pagination, rowClassName, scroll, children, ...restProps } = props;
  return (
    <TableWrapper>
      <ButtonSpace>
        <Space>{rightTop}</Space>
      </ButtonSpace>
      <Table
        columns={columns}
        dataSource={dataSource}
        rowKey={rowKey}
        rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
        pagination={false}
        scroll={scroll}
      ></Table>
    </TableWrapper>
  );
}
