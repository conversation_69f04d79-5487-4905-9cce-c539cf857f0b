import {
  But<PERSON>,
  Card,
  Col,
  Form,
  Row,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Table,
  Checkbox,
  Tree,
  Upload,
} from 'antd';
import { DeleteOutlined, PlusSquareOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  citySubNodeJoin,
  deleteFleetEmailDelivery,
  emailDeliveryFind,
  importUserEmailSh,
  insertFleetEmailSelfInfo,
  modifyEmailDeliveryInfo,
  queryFleetEmailDeliveryList,
  queryProductInfo,
  querySupplierNameInfo,
  exportFleetEmailDeliveryList,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';
import DebounceSelect from '@/components/useDebounceSelect';

const { Item } = Form;
const modalDataSource = { flag: Math.random(), supplierName: '' };

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [fileList, setFile] = useState<any>();
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [supplierName, setSupplierName] = useState([]);
  const [supplierNameOrigin, setSupplierNameOrigin] = useState([]);
  const [productCheckBox, setProductCheckBox] = useState([]);
  const [cityCheckBox, setCityCheckBox] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [modalData, setModalData] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: any = [
    {
      title: '负责的供应商',
      align: 'center',
      dataIndex: 'suppliers',
      key: 'suppliers',
      width: 250,
    },
    {
      title: '负责的产品',
      align: 'center',
      dataIndex: 'products',
      key: 'products',
      width: 200,
    },
    {
      title: '负责的城市',
      align: 'center',
      dataIndex: 'cities',
      key: 'cities',
      width: 150,
    },
    {
      title: '负责人名称',
      align: 'center',
      dataIndex: 'userName',
      key: 'userName',
      width: 100,
    },
    {
      title: '负责人邮箱地址',
      align: 'center',
      dataIndex: 'userEmail',
      key: 'userEmail',
      width: 150,
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="FleetDeliver-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="FleetDeliver-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const modalColumns: any = [
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>负责的供应商</span>
        </>
      ),
      align: 'center',
      dataIndex: 'supplierName',
      key: 'supplierName',
      render: (data, record) => {
        return (
          <DebounceSelect
            showSearch
            style={{ width: '100%' }}
            value={{ label: data, value: '' }}
            placeholder="请输入负责的供应商"
            initOptions={supplierName}
            fetchOptions={handleSupplierSearch}
            onChange={(newValue) => handleSupplierInfo(record, 'edit', 'suppliers', newValue)}
          />
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 200,
      render: (item, record) => {
        return (
          <>
            <Popconfirm
              title="确定要删除？"
              onConfirm={() => handleSupplierInfo(record, 'delete')}
              okText="确定"
              cancelText="取消"
            >
              <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
            </Popconfirm>
            <PlusSquareOutlined
              onClick={() => handleSupplierInfo(record, 'add')}
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
            />
          </>
        );
      },
    },
  ];
  const handleSupplierSearch = useCallback(
    (value) => {
      return querySupplierNameInfo(value).then((res) => {
        if (res.success) {
          return res?.data?.map((item) => ({ label: item.supplierName, value: item.id }));
        } else {
          message.error(res?.msg);
        }
      });
    },
    [supplierName],
  );
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const handleEdit = useCallback(
    (record) => {
      emailDeliveryFind(record.id)
        .then((res) => {
          if (res.success) {
            const temp = supplierNameOrigin?.filter((item) => res?.data?.suppliers?.includes(item?.id));
            setShowModal(true);
            setCheckedKeys(res?.data?.cities);
            setModalData(
              temp?.map((item) => {
                item.flag = Math.random();
                item.supplierId = item.id;
                return item;
              }),
            );
            setModalTitle('编辑车队邮箱');
            modalForm.setFieldsValue({
              ...record,
              products: res.data?.products,
              cities: res.data?.cities,
            });
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    [showModal, modalData, modalTitle, modalForm, checkedKeys],
  );
  const handleAdd = useCallback(() => {
    setShowModal(true);
    setModalData([]);
    setCheckedKeys([]);
    setModalTitle('新增车队邮箱');
    modalForm.resetFields();
  }, [showModal, modalData, modalTitle, modalForm, checkedKeys]);
  const handleDelete = useCallback((record) => {
    deleteFleetEmailDelivery(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleSupplierInfo = useCallback(
    (record?, status?, field?, value?) => {
      if (status === 'singleAdd') {
        setModalData([{ ...modalDataSource }]);
      }
      if (status === 'delete') {
        const temp = [...modalData];
        let result = temp.filter((item) => item.flag !== record.flag);
        result.forEach((item) => {
          item.flag = Math.random();
        });
        setModalData([...result]);
      }
      if (status === 'add') {
        const temp = [...modalData];
        let result = [...temp, { supplierName: '', supplierId: '', flag: Math.random() }];
        setModalData([...result]);
      }
      if (status === 'edit') {
        const temp = [...modalData];
        let result = temp.map((item) => {
          if (item.flag == record.flag) {
            item.supplierName = value?.label;
            item.supplierId = value?.value;
            item.flag = Math.random();
          }
          return item;
        });
        setModalData([...result]);
      }
    },
    [modalData, modalDataSource],
  );
  const handleModalOk = useCallback(() => {
    const supplierNumber = modalData?.map((item) => item.supplierId)?.filter((item) => !!item);
    if (supplierNumber?.length <= 0) {
      message.warning('负责的供应商不可为空');
    } else {
      modalForm.validateFields().then(() => {
        if (modalTitle === '新增车队邮箱') {
          insertFleetEmailSelfInfo({
            ...modalForm.getFieldsValue(),
            userType: 3,
            cities: checkedKeys,
            suppliers: modalData?.map((item) => item.supplierId)?.filter((item) => !!item),
          })
            .then((res) => {
              if (res.success) {
                message.success(`${modalTitle}成功`);
                getTable();
                setShowModal(false);
              } else {
                message.error(res?.msg);
              }
            })
            .catch((e) => {
              console.log(e);
            });
        } else {
          modifyEmailDeliveryInfo({
            ...modalForm.getFieldsValue(),
            userType: 3,
            cities: checkedKeys,
            suppliers: modalData?.map((item) => item.supplierId)?.filter((item) => !!item),
          })
            .then((res) => {
              if (res.success) {
                message.success(`${modalTitle}成功`);
                getTable();
                setShowModal(false);
              } else {
                message.error(res?.msg);
              }
            })
            .catch((e) => {
              console.log(e);
            });
        }
      });
    }
  }, [modalForm, modalTitle, modalData, checkedKeys]);
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    queryFleetEmailDeliveryList({
      ...form.getFieldsValue(),
      userType: 3,
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(
            res?.data?.map((item) => {
              item.suppliers = item.suppliers?.join('，');
              item.products = item.products?.join('，');
              item.cities = item.cities?.join('，');
              return item;
            }),
          );
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [pageParams, dataSource]);
  const getSelectFields = useCallback(() => {
    queryProductInfo('').then((res) => {
      if (res.success) {
        setProductCheckBox(
          res?.data?.map((item) => {
            return { label: item.productName, value: item.id };
          }),
        );
      } else {
        message.error(res?.msg);
      }
    });
    querySupplierNameInfo().then((res) => {
      if (res.success) {
        setSupplierNameOrigin(res?.data);
        setSupplierName(res?.data?.map((item) => ({ label: item.supplierName, value: item.id })));
      } else {
        message.error(res?.msg);
      }
    });
    citySubNodeJoin([]).then((res) => {
      if (res.success) {
        const formatCity = (data) => {
          return data?.map((item) => {
            return {
              title: item.label,
              key: item.label,
              children: item?.cityTreeDos?.length > 0 ? formatCity(item.cityTreeDos) : [],
            };
          });
        };
        const result = formatCity(res.data);
        setCityCheckBox(result);
      } else {
        message.error(res?.msg);
      }
    });
  }, [productCheckBox, cityCheckBox, supplierName]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);
  const onCheck = useCallback(
    (checkedKeysValue) => {
      setCheckedKeys(checkedKeysValue);
    },
    [checkedKeys],
  );
  const objectToFormData = (obj, form) => {
    const fd = form || new FormData();
    for (let property in obj) {
      if (obj.hasOwnProperty(property)) {
        // if the property is an object, but not a File, use recursivity.
        if (typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
          objectToFormData(obj[property], fd);
        } else if (obj[property] instanceof File) {
          // if it's a string or a File object
          // fd.append('file', obj[property]);
          fd.append('File', obj[property]);
        }
      }
    }
    return fd;
  };
  const importExcel = () => {
    const formData = new FormData();
    objectToFormData(fileList, formData);
    importUserEmailSh(formData).then((res) => {
      if (res.success) {
        message.success('导入成功');
        getTable();
      } else {
        message.error(res.msg);
      }
    });
  };
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  const exportFleetEmailDelivery = () => {
    exportFleetEmailDeliveryList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999,
    }).then((res) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '车队邮箱地址信息(送货).xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="车队邮箱地址信息(送货)" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={18}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="FleetDeliver-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <>
              <Button type="primary" onClick={() => exportFleetEmailDelivery()} style={{ marginRight: '5px' }}>
                导出
              </Button>
              <AuthorityComponent type="FleetDeliver-Import">
                <Upload
                  accept=".xls,.xlsx"
                  customRequest={() => importExcel()}
                  showUploadList={false}
                  name="file"
                  onChange={({ file: newFileList }) => setFile(newFileList)}
                >
                  <Button>导入车队邮箱</Button>
                </Upload>
              </AuthorityComponent>
              <AuthorityComponent type="FleetDeliver-Newlyadded">
                <Button type="primary" onClick={() => handleAdd()}>
                  新增车队邮箱
                </Button>
              </AuthorityComponent>
            </>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm}>
          <Item name="id" hidden></Item>
          <Row gutter={32}>
            <Col span={24}>
              <Item
                name="products"
                required
                label="负责的产品"
                labelCol={{ span: 3 }}
                wrapperCol={{ span: 21 }}
                labelAlign="left"
                rules={[{ required: true, message: '请勾选负责的产品' }]}
              >
                <Checkbox.Group options={productCheckBox} />
              </Item>
            </Col>
            <Col span={24}>
              <Item
                name="cities"
                required
                label="负责的城市"
                labelCol={{ span: 3 }}
                wrapperCol={{ span: 21 }}
                labelAlign="left"
                rules={[
                  {
                    validator: (rule, value) => {
                      if (checkedKeys?.length <= 0) {
                        return Promise.reject('请勾选负责的城市');
                      } else {
                        return Promise.resolve();
                      }
                    },
                  },
                ]}
              >
                <div style={{ maxHeight: 150, overflowY: 'auto' }}>
                  <Tree
                    checkable
                    defaultExpandAll={true}
                    onCheck={onCheck}
                    checkedKeys={checkedKeys}
                    treeData={cityCheckBox}
                  />
                </div>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="userName"
                required
                label="负责人名称"
                labelAlign="left"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                rules={[{ required: true, message: '请输入负责人名称' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="userEmail"
                required
                label="负责人邮箱地址"
                labelAlign="left"
                labelCol={{ span: 7 }}
                wrapperCol={{ span: 17 }}
                rules={[
                  { required: true, message: '请输入负责人邮箱地址' },
                  {
                    pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                    message: '邮箱格式不正确',
                  },
                ]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
          </Row>
        </Form>
        <TableTitle
          icon={false}
          title="供应商信息"
          right={
            modalData?.length == 0 && (
              <Button onClick={() => handleSupplierInfo(modalDataSource, 'singleAdd')}>新增供应商</Button>
            )
          }
        ></TableTitle>
        <Table
          rowKey="flag"
          columns={modalColumns}
          dataSource={modalData}
          pagination={false}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          scroll={{ y: 200 }}
        ></Table>
      </ModalDiv>
    </>
  );
};
