import { useLocation } from 'umi';
import { Button, Card, Col, Form, Row, Input, message, Modal, Popconfirm, Select, Table, Upload } from 'antd';
import { DeleteOutlined, PlusSquareOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv, { formProps, tableColumns } from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  deleSupplier,
  deleteFleetEmailSelf,
  deleteSupplier,
  emailSelfFind,
  importUserEmailZt,
  insertFleetEmailSelfInfo,
  insertQuerySupplier,
  modifyEmailSelfInfo,
  queryFleetEmailSelfList,
  querySupplierList,
  querySupplierTypeInfo,
  recoverSupplier,
  exportFleetEmailSelList,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';

const { Item } = Form;
const { Option } = Select;

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [fileList, setFile] = useState<any>();
  const location = useLocation<any>();
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [supplierType, setSupplierType] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [modalData, setModalData] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: tableColumns = [
    {
      title: '负责的车牌号',
      align: 'center',
      dataIndex: 'carNos',
      key: 'carNos',
      width: 300,
    },
    {
      title: '负责人名称',
      align: 'center',
      dataIndex: 'userName',
      key: 'userName',
      width: 150,
    },
    {
      title: '负责人邮箱地址',
      align: 'center',
      dataIndex: 'userEmail',
      key: 'userEmail',
      width: 150,
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="FleetDeliver-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="FleetDeliver-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const modalColumns: tableColumns = [
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>负责的车牌号</span>
        </>
      ),
      align: 'center',
      dataIndex: 'carNo',
      key: 'carNo',
      render: (value, record) => {
        return (
          <div style={{ textAlign: 'center' }}>
            <Input
              defaultValue={value}
              onBlur={(e) => handleCarInfo(record, 'edit', 'carNo', e.target.value)}
              onPressEnter={(e) => handleCarInfo(record, 'edit', 'carNo', e.target.value)}
            ></Input>
          </div>
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 200,
      render: (item, record) => {
        return (
          <>
            <Popconfirm
              title="确定要删除？"
              onConfirm={() => handleCarInfo(record, 'delete')}
              okText="确定"
              cancelText="取消"
            >
              <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
            </Popconfirm>
            <PlusSquareOutlined
              onClick={() => handleCarInfo(record, 'add')}
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
            />
          </>
        );
      },
    },
  ];
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const handleEdit = useCallback(
    (record) => {
      emailSelfFind(record.id)
        .then((res) => {
          if (res.success) {
            setShowModal(true);
            setModalData(
              res?.data?.carNos?.map((item) => {
                return { flag: Math.random(), carNo: item };
              }),
            );
            setModalTitle('编辑车队邮箱');
            modalForm.setFieldsValue(record);
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    [modalTitle, showModal, modalData, modalForm],
  );
  const handleAdd = useCallback(() => {
    setShowModal(true);
    setModalData([]);
    setModalTitle('新增车队邮箱');
    modalForm.resetFields();
  }, [modalTitle, showModal, modalData, modalForm]);
  const handleDelete = useCallback((record) => {
    deleteFleetEmailSelf(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleCarInfo = useCallback(
    (record?, status?, field?, value?) => {
      if (status === 'singleAdd') {
        setModalData([{ flag: Math.random(), carNo: '' }]);
      }
      if (status === 'delete') {
        const temp = [...modalData];
        let result = temp.filter((item) => item.flag != record.flag);
        setModalData([...result]);
      }
      if (status === 'add') {
        const temp = [...modalData];
        let result = [...temp, { flag: Math.random(), carNo: '' }];
        result.forEach((item) => {
          item.flag = Math.random();
        });
        setModalData([...result]);
      }
      if (status === 'edit') {
        const temp = [...modalData];
        let result = temp.map((item) => {
          if (item.flag == record.flag) {
            item.carNo = value;
            item.flag = Math.random();
          }
          return item;
        });
        setModalData([...result]);
      }
    },
    [modalData],
  );
  const handleModalOk = useCallback(() => {
    const carNumber = modalData?.map((item) => item.carNo)?.filter((item) => !!item);
    if (carNumber?.length <= 0) {
      message.warning('负责的车牌号不可为空');
    } else {
      modalForm.validateFields().then(() => {
        if (modalTitle === '新增车队邮箱') {
          insertFleetEmailSelfInfo({
            ...modalForm.getFieldsValue(),
            userType: 2,
            carNos: modalData?.map((item) => item.carNo)?.filter((item) => !!item),
          })
            .then((res) => {
              if (res.success) {
                message.success(`${modalTitle}成功`);
                getTable();
                setShowModal(false);
              } else {
                message.error(res?.msg);
              }
            })
            .catch((e) => {
              console.log(e);
            });
        } else {
          modifyEmailSelfInfo({
            ...modalForm.getFieldsValue(),
            userType: 2,
            carNos: modalData?.map((item) => item.carNo)?.filter((item) => !!item),
          })
            .then((res) => {
              if (res.success) {
                message.success(`${modalTitle}成功`);
                getTable();
                setShowModal(false);
              } else {
                message.error(res?.msg);
              }
            })
            .catch((e) => {
              console.log(e);
            });
        }
      });
    }
  }, [modalForm, modalTitle, modalData]);
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    queryFleetEmailSelfList({
      ...form.getFieldsValue(),
      userType: 2,
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(
            res?.data?.map((item) => {
              item.carNos = item.carNos?.join('，');
              return item;
            }),
          );
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [pageParams, dataSource]);
  const getSelectFields = useCallback(() => {
    querySupplierTypeInfo()
      .then((res) => {
        if (res.success) {
          setSupplierType(res?.data);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [supplierType]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);
  const objectToFormData = (obj, form) => {
    const fd = form || new FormData();
    for (let property in obj) {
      if (obj.hasOwnProperty(property)) {
        // if the property is an object, but not a File, use recursivity.
        if (typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
          objectToFormData(obj[property], fd);
        } else if (obj[property] instanceof File) {
          // if it's a string or a File object
          // fd.append('file', obj[property]);
          fd.append('File', obj[property]);
        }
      }
    }
    return fd;
  };
  const importExcel = () => {
    const formData = new FormData();
    objectToFormData(fileList, formData);
    importUserEmailZt(formData).then((res) => {
      if (res.success) {
        message.success('导入成功');
        getTable();
      } else {
        message.error(res.msg);
      }
    });
  };
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
    labelAlign: 'left',
  };
  const exportFleetEmailSel = () => {
    exportFleetEmailSelList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999,
    }).then((res) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '车队邮箱地址信息(自提).xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="车队邮箱地址信息(自提)" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={18}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="FleetPick-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <>
              <Button type="primary" onClick={() => exportFleetEmailSel()} style={{ marginRight: '5px' }}>
                导出
              </Button>
              <AuthorityComponent type="FleetPick-Import">
                <Upload
                  accept=".xls,.xlsx"
                  customRequest={() => importExcel()}
                  showUploadList={false}
                  name="file"
                  onChange={({ file: newFileList }) => setFile(newFileList)}
                >
                  <Button>导入车队邮箱</Button>
                </Upload>
              </AuthorityComponent>
              <AuthorityComponent type="FleetPick-Newlyadded">
                <Button type="primary" onClick={() => handleAdd()}>
                  新增车队邮箱
                </Button>
              </AuthorityComponent>
            </>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm} {...layout}>
          <Item name="id" hidden></Item>
          <Row gutter={32}>
            <Col span={12}>
              <Item
                name="userName"
                required
                label="负责人名称"
                rules={[{ required: true, message: '请输入负责人名称' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="userEmail"
                required
                label="负责人邮箱地址"
                rules={[
                  { required: true, message: '请输入负责人邮箱地址' },
                  {
                    pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                    message: '邮箱格式不正确',
                  },
                ]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
          </Row>
        </Form>
        <TableTitle
          icon={false}
          title="车牌号信息"
          right={modalData?.length == 0 && <Button onClick={() => handleCarInfo('', 'singleAdd')}>新增车牌号</Button>}
        ></TableTitle>
        <Table
          rowKey="flag"
          columns={modalColumns}
          dataSource={modalData}
          pagination={false}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
          scroll={{ y: 400 }}
        ></Table>
      </ModalDiv>
    </>
  );
};
