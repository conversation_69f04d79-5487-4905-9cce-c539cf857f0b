import { useLocation } from 'umi';
import { But<PERSON>, Card, Col, Form, Row, Input, message, Modal, Popconfirm, Select, Table } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv, { formProps, tableColumns } from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  deleSupplier,
  deleteSourceManage,
  deleteSupplier,
  insertQuerySupplier,
  insertSourceManageInfo,
  modifySourceManageInfo,
  querySourceManageList,
  querySupplierList,
  querySupplierNameInfo,
  querySupplierTypeInfo,
  recoverSupplier,
  exportSourceManageList,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';

const { Item } = Form;
const { Option } = Select;

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const location = useLocation<any>();
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [supplierName, setSupplierName] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: tableColumns = [
    {
      title: '供应商代码',
      align: 'center',
      dataIndex: 'supplierCode',
      key: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      align: 'center',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 200,
    },
    {
      title: '货源点城市',
      align: 'center',
      dataIndex: 'sourceCity',
      key: 'sourceCity',
      width: 150,
    },
    {
      title: '货源点名称',
      align: 'center',
      dataIndex: 'sourceName',
      key: 'sourceName',
      width: 150,
    },
    {
      title: '比对字符串',
      align: 'center',
      key: 'compareStr',
      dataIndex: 'compareStr',
      width: 150,
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="MutiSupply-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="MutiSupply-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const handleEdit = useCallback(
    (record) => {
      setShowModal(true);
      setModalTitle('编辑条目');
      modalForm.setFieldsValue({
        ...record,
        supplierName: { label: record.supplierName, value: record.id },
      });
    },
    [showModal, modalTitle, modalForm],
  );
  const handleAdd = useCallback(() => {
    setShowModal(true);
    setModalTitle('新增条目');
    modalForm.resetFields();
  }, [showModal, modalTitle, modalForm]);
  const handleDelete = useCallback((record) => {
    deleteSourceManage(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleModalOk = useCallback(() => {
    modalForm.validateFields().then(() => {
      if (modalTitle === '新增条目') {
        insertSourceManageInfo({
          ...modalForm.getFieldsValue(),
          supplierName: modalForm.getFieldValue('supplierName').label,
          supplierId: modalForm.getFieldValue('supplierName').value,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        modifySourceManageInfo({
          ...modalForm.getFieldsValue(),
          supplierName: modalForm.getFieldValue('supplierName').label,
          supplierId: modalForm.getFieldValue('supplierName').value,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    });
  }, [modalForm, modalTitle, showModal]);
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    querySourceManageList({
      ...form.getFieldsValue(),
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(res?.data);
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [pageParams, dataSource]);
  const getSelectFields = useCallback(() => {
    querySupplierNameInfo()
      .then((res) => {
        if (res.success) {
          setSupplierName(res?.data);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [supplierName]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
    labelAlign: 'left',
  };
  const exportSourceManage = () => {
    exportSourceManageList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999,
    }).then((res) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '供应商同城市多货源点.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="供应商同城市多货源点管理" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={18}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="MutiSupply-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <>
              <Button type="primary" onClick={() => exportSourceManage()} style={{ marginRight: '5px' }}>
                导出
              </Button>
              <AuthorityComponent type="MutiSupply-Newlyadded">
                <Button type="primary" onClick={handleAdd}>
                  新增条目
                </Button>
              </AuthorityComponent>
            </>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm} {...layout}>
          <Item name="id" hidden></Item>
          <Item name="supplierId" hidden></Item>
          <Row gutter={32}>
            <Col span={12}>
              <Item
                name="supplierName"
                required
                label="供应商名称"
                rules={[{ required: true, message: '请选择供应商名称' }]}
              >
                <Select allowClear optionFilterProp="children" showSearch labelInValue>
                  {supplierName?.map((item, index) => {
                    return (
                      <Option key={index} value={item.id}>
                        {item.supplierName}
                      </Option>
                    );
                  })}
                </Select>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="sourceCity"
                required
                label="货源点城市"
                rules={[{ required: true, message: '请输入货源点城市' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="sourceName"
                required
                label="货源点名称"
                rules={[{ required: true, message: '请输入货源点名称' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="compareStr"
                required
                label="比对字符串"
                rules={[{ required: true, message: '请输入比对字符串' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
          </Row>
        </Form>
      </ModalDiv>
    </>
  );
};
