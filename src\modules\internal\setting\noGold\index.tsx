import { But<PERSON>, Card, Col, Row, Input, message, Popconfirm, Table, Space, Select } from 'antd';
import { DeleteOutlined, PlusSquareOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState } from 'react';
import FormDiv from '../component/FormDiv';
import { saveNoGoldInfo, queryNoGoldList, queryClearingList, querySupplierList } from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';

const { Option } = Select;
const modalDataSource = {
  flag: Math.random(),
  id: '',
  supplierCode: '',
  customerName: '',
  customerCity: '',
  customerProvince: '',
};

export default () => {
  const [supplierList, setSupplierList] = useState([]);
  const [dataSource, setDataSource] = useState([]);

  const columns: any = [
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>供应商</span>
        </>
      ),
      align: 'center',
      dataIndex: 'supplierCode',
      key: 'supplierCode',
      width: 400,
      render: (value, record) => {
        return (
          // <Select
          //   defaultValue={value}
          //   style={{ width: '100%' }}
          //   onChange={(e) => handleCustomerInfo(record, 'edit', 'supplierCode', e)}
          //   allowClear
          // >
          //   {supplierList?.map((item, index) => {
          //     return (
          //       <Option key={index} value={item.supplierNo}>
          //         {item.supplierName}
          //       </Option>
          //     );
          //   })}
          // </Select>

          <Select
            optionFilterProp="children"
            showSearch
            allowClear
            defaultValue={value}
            style={{ width: '100%' }}
            onChange={(e) => handleCustomerInfo(record, 'edit', 'supplierCode', e)}
          >
            {supplierList?.map((item, index) => {
              return (
                <Select.Option key={index} value={item.supplierNo}>
                  {item.supplierName}
                </Select.Option>
              );
            })}
          </Select>
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>客户名称</span>
        </>
      ),
      align: 'center',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 400,
      render: (value, record) => {
        return (
          <Input
            allowClear
            defaultValue={value}
            onBlur={(e) => handleCustomerInfo(record, 'edit', 'customerName', e.target.value)}
            onPressEnter={(e) => handleCustomerInfo(record, 'edit', 'customerName', e.target.value)}
          ></Input>
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>客户城市</span>
        </>
      ),
      align: 'center',
      dataIndex: 'customerCity',
      key: 'customerCity',
      render: (value, record) => {
        return (
          <Input
            allowClear
            defaultValue={value}
            onBlur={(e) => handleCustomerInfo(record, 'edit', 'customerCity', e.target.value)}
            onPressEnter={(e) => handleCustomerInfo(record, 'edit', 'customerCity', e.target.value)}
          ></Input>
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>客户省份</span>
        </>
      ),
      align: 'center',
      dataIndex: 'customerProvince',
      key: 'customerProvince',
      render: (value, record) => {
        return (
          <Input
            allowClear
            defaultValue={value}
            onBlur={(e) => handleCustomerInfo(record, 'edit', 'customerProvince', e.target.value)}
            onPressEnter={(e) => handleCustomerInfo(record, 'edit', 'customerProvince', e.target.value)}
          ></Input>
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 200,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="NoGold-Preservation">
              <Popconfirm
                title="确定要删除？"
                onConfirm={() => handleCustomerInfo(record, 'delete')}
                okText="确定"
                cancelText="取消"
              >
                <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
              </Popconfirm>
            </AuthorityComponent>
            <AuthorityComponent type="NoGold-Preservation">
              <PlusSquareOutlined
                onClick={() => handleCustomerInfo(record, 'add')}
                style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
              />
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const handleCustomerInfo = useCallback(
    (record?, status?, field?, value?) => {
      if (status === 'singleAdd') {
        setDataSource([{ ...modalDataSource }]);
      }
      if (status === 'delete') {
        const temp = [...dataSource];
        let result = temp.filter((item) => item.flag !== record.flag);
        setDataSource([...result]);
      }
      if (status === 'add') {
        const temp = [...dataSource];
        let result = [...temp, { customerName: '', flag: Math.random() }];
        setDataSource([...result]);
      }
      if (status === 'edit') {
        const temp = [...dataSource];
        let result = temp.map((item) => {
          if (item.flag == record.flag) {
            item[field] = value;
          }
          return item;
        });
        result.forEach((item) => {
          item.flag = Math.random();
        });
        setDataSource([...result]);
      }
    },
    [dataSource, modalDataSource],
  );
  const handleSave = useCallback(() => {
    const adddata = dataSource.filter((item) => {
      return (
        item.customerName.trim() == '' ||
        item.supplierCode == undefined ||
        item.supplierCode == null ||
        item.supplierCode == '' ||
        item.customerCity == null ||
        item.customerCity == undefined ||
        item.customerCity == '' ||
        item.customerProvince == null ||
        item.customerProvince == '' ||
        item.customerProvince == undefined
      );
    });
    adddata.length > 0
      ? message.warning('请输入必填项')
      : saveNoGoldInfo(dataSource)
          .then((res) => {
            if (res.success) {
              message.success('保存成功');
              getTable();
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
  }, [dataSource]);
  const getTable = useCallback(() => {
    queryNoGoldList().then((res) => {
      if (res.success) {
        setDataSource(
          res.data.map((item) => {
            item.flag = Math.random();
            return item;
          }),
        );
      } else {
        message.error(res?.msg);
      }
    });
    querySupplierList({
      pageIndex: 1,
      pageSize: 1000,
    }).then((res) => {
      if (res.success) {
        setSupplierList(res.data);
      } else {
        message.error(res?.msg);
      }
    });
  }, [dataSource]);
  useEffect(() => {
    getTable();
  }, []);
  return (
    <Card style={{ minHeight: 800, position: 'relative' }}>
      <FormDiv title="客户信息"></FormDiv>
      <Table
        rowKey="flag"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
      ></Table>
      <Row>
        <Col span={24}>
          <div style={{ textAlign: 'center', marginTop: 40 }}>
            <Space size="large">
              {dataSource?.length == 0 && (
                <Button onClick={() => handleCustomerInfo(modalDataSource, 'singleAdd')}>新增</Button>
              )}
              <AuthorityComponent type="NoGold-Preservation">
                <Button
                  style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                  onClick={handleSave}
                >
                  保存
                </Button>
              </AuthorityComponent>
            </Space>
          </div>
        </Col>
      </Row>
    </Card>
  );
};
