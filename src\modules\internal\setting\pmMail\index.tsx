import { useLocation } from 'umi';
import { But<PERSON>, Card, Col, Form, Row, Input, message, Modal, Popconfirm, Select, Table, Checkbox } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv, { formProps, tableColumns } from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  deleSupplier,
  deleteEmailEJV,
  deleteSupplier,
  emailEJVFind,
  insertEmailEJVInfo,
  insertQuerySupplier,
  modifyEmailEJVInfo,
  queryEJVSupplierNameInfo,
  queryEmailEJVList,
  queryProductInfo,
  querySupplierList,
  querySupplierTypeInfo,
  recoverSupplier,
  exportEmailEJVList,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';

const { Item } = Form;
const { Option } = Select;

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const location = useLocation<any>();
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [ejvCheckBox, setEjvCheckBox] = useState([]);
  const [productCheckBox, setProductCheckBox] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: tableColumns = [
    {
      title: '负责的EJV',
      align: 'center',
      dataIndex: 'ejVs',
      key: 'ejVs',
      width: 300,
    },
    {
      title: '负责的产品',
      align: 'center',
      dataIndex: 'products',
      key: 'products',
      width: 150,
    },
    {
      title: 'PM名称',
      align: 'center',
      dataIndex: 'userName',
      key: 'userName',
      width: 100,
    },
    {
      title: 'PM邮箱地址',
      align: 'center',
      key: 'userEmail',
      dataIndex: 'userEmail',
      width: 100,
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 100,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="PmMail-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="PmMail-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const handleEdit = useCallback(
    (record) => {
      emailEJVFind(record.id)
        .then((res) => {
          if (res.success) {
            setShowModal(true);
            setModalTitle('编辑PM邮箱');
            modalForm.setFieldsValue(res?.data);
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    [showModal, modalTitle],
  );
  const handleAdd = useCallback(() => {
    setShowModal(true);
    setModalTitle('新增PM邮箱');
    modalForm.resetFields();
  }, [showModal, modalTitle, modalForm]);
  const handleDelete = useCallback((record) => {
    deleteEmailEJV(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleModalOk = useCallback(() => {
    modalForm.validateFields().then(() => {
      if (modalTitle === '新增PM邮箱') {
        insertEmailEJVInfo({
          ...modalForm.getFieldsValue(),
          userType: 0,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        modifyEmailEJVInfo({
          ...modalForm.getFieldsValue(),
          userType: 0,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    });
  }, [modalForm, modalTitle, showModal]);
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    queryEmailEJVList({
      ...form.getFieldsValue(),
      userType: 0,
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(
            res?.data?.map((item) => {
              item.ejVs = item.ejVs?.join('，');
              item.products = item.products?.join('，');
              return item;
            }),
          );
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [form, pageParams, dataSource]);
  const getSelectFields = useCallback(() => {
    queryProductInfo('')
      .then((res) => {
        if (res.success) {
          setProductCheckBox(
            res?.data?.map((item) => {
              return { label: item.productName, value: item.id };
            }),
          );
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    queryEJVSupplierNameInfo()
      .then((res) => {
        if (res.success) {
          setEjvCheckBox(
            res?.data?.map((item) => {
              return { label: item.supplierName, value: item.id };
            }),
          );
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [productCheckBox, ejvCheckBox]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
    labelAlign: 'left',
  };
  const exportEmailEJV = () => {
    exportEmailEJVList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999,
    }).then((res) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = 'PM邮箱地址信息(EJV).xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="PM邮箱地址信息(EJV)" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={18}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="PmMail-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <>
              <Button type="primary" onClick={() => exportEmailEJV()} style={{ marginRight: '5px' }}>
                导出
              </Button>
              <AuthorityComponent type="PmMail-Newlyadded">
                <Button type="primary" onClick={() => handleAdd()}>
                  新增PM邮箱
                </Button>
              </AuthorityComponent>
            </>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm} {...layout}>
          <Item name="id" hidden></Item>
          <Row gutter={32}>
            <Col span={24}>
              <Item
                name="ejVs"
                required
                label="负责的EJV"
                rules={[{ required: true, message: '请勾选负责的EJV' }]}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <Checkbox.Group options={ejvCheckBox} />
              </Item>
            </Col>
            <Col span={24}>
              <Item
                name="products"
                required
                label="负责的产品"
                rules={[{ required: true, message: '请勾选负责的产品' }]}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <Checkbox.Group options={productCheckBox} />
              </Item>
            </Col>
            <Col span={12}>
              <Item name="userName" required label="PM名称" rules={[{ required: true, message: '请输入PM名称' }]}>
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="userEmail"
                required
                label="PM邮箱地址"
                rules={[
                  {
                    pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                    message: '邮箱格式不正确',
                  },
                ]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
          </Row>
        </Form>
      </ModalDiv>
    </>
  );
};
