import { useLocation } from 'umi';
import { But<PERSON>, Card, Col, Form, Row, Input, message, Modal, Popconfirm, Select, Table, Checkbox } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv, { formProps, tableColumns } from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  deleSupplier,
  deleteEmailOS,
  deleteSupplier,
  emailOSFind,
  insertEmailOSInfo,
  insertQuerySupplier,
  modifyEmailOSInfo,
  queryAreaInfo,
  queryEmailOSList,
  queryProductInfo,
  querySupplierList,
  querySupplierTypeInfo,
  recoverSupplier,
  exportEmailOSList,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';

const { Item } = Form;
const { Option } = Select;

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const location = useLocation<any>();
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [areaCheckBox, setAreaCheckBox] = useState([]);
  const [productCheckBox, setProductCheckBox] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: tableColumns = [
    {
      title: '负责区域',
      align: 'center',
      dataIndex: 'regions',
      key: 'regions',
      width: 100,
    },
    {
      title: '负责产品',
      align: 'center',
      dataIndex: 'products',
      key: 'products',
      width: 200,
    },
    {
      title: '采购名称',
      align: 'center',
      dataIndex: 'userName',
      key: 'userName',
      width: 100,
    },
    {
      title: '采购邮箱地址',
      align: 'center',
      key: 'userEmail',
      dataIndex: 'userEmail',
      width: 100,
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 100,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="PurchaseMail-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="PurchaseMail-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const handleEdit = useCallback(
    (record) => {
      emailOSFind(record.id)
        .then((res) => {
          if (res.success) {
            setShowModal(true);
            setModalTitle('编辑采购邮箱');
            modalForm.setFieldsValue(res?.data);
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    [showModal, modalTitle, modalForm],
  );
  const handleAdd = useCallback(() => {
    setShowModal(true);
    setModalTitle('新增采购邮箱');
    modalForm.resetFields();
  }, [showModal, modalTitle, modalForm]);
  const handleDelete = useCallback((record) => {
    deleteEmailOS(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleModalOk = useCallback(() => {
    modalForm.validateFields().then(() => {
      if (modalTitle === '新增采购邮箱') {
        insertEmailOSInfo({
          ...modalForm.getFieldsValue(),
          userType: 1,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        modifyEmailOSInfo({
          ...modalForm.getFieldsValue(),
          userType: 1,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    });
  }, [modalForm, modalTitle, showModal]);
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    queryEmailOSList({
      ...form.getFieldsValue(),
      userType: 1,
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(
            res?.data?.map((item) => {
              item.regions = item.regions?.join('，');
              item.products = item.products?.join('，');
              return item;
            }),
          );
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [pageParams, dataSource, form]);
  const getSelectFields = useCallback(() => {
    queryAreaInfo()
      .then((res) => {
        if (res.success) {
          setAreaCheckBox(
            res?.data?.map((item) => {
              return item.supplierType;
            }),
          );
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    queryProductInfo('')
      .then((res) => {
        if (res.success) {
          setProductCheckBox(
            res?.data?.map((item) => {
              return { label: item.productName, value: item.id };
            }),
          );
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [areaCheckBox, productCheckBox]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
    labelAlign: 'left',
  };
  const exportEmailOS = () => {
    exportEmailOSList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999,
    }).then((res) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '采购邮箱地址信息.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="采购邮箱地址信息(OS)" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={18}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="PurchaseMail-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <>
              <Button type="primary" onClick={() => exportEmailOS()} style={{ marginRight: '5px' }}>
                导出
              </Button>
              <AuthorityComponent type="PurchaseMail-Newlyadded">
                <Button type="primary" onClick={() => handleAdd()}>
                  新增采购邮箱
                </Button>
              </AuthorityComponent>
            </>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm} {...layout}>
          <Item name="id" hidden></Item>
          <Row gutter={32}>
            <Col span={24}>
              <Item
                name="regions"
                required
                label="负责的区域"
                rules={[{ required: true, message: '请勾选负责的区域' }]}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <Checkbox.Group options={areaCheckBox} />
              </Item>
            </Col>
            <Col span={24}>
              <Item
                name="products"
                required
                label="负责的产品"
                rules={[{ required: true, message: '请勾选负责的产品' }]}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <Checkbox.Group options={productCheckBox} />
              </Item>
            </Col>
            <Col span={12}>
              <Item name="userName" required label="采购名称" rules={[{ required: true, message: '请输入采购名称' }]}>
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="userEmail"
                required
                label="采购邮箱地址"
                rules={[
                  {
                    pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                    message: '邮箱格式不正确',
                  },
                ]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
          </Row>
        </Form>
      </ModalDiv>
    </>
  );
};
