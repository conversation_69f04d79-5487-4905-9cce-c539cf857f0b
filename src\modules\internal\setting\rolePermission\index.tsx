import { useLocation } from 'umi';
import { Button, Card, Col, Form, Row, Input, message, Modal, Popconfirm, Select, Table, Checkbox, Tree } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv, { formProps, tableColumns } from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  authTreeJoin,
  deleSupplier,
  deleteRole,
  deleteSupplier,
  insertQuerySupplier,
  insertRole,
  modifyRole,
  queryRoleFind,
  queryRolePageInfo,
  querySupplierList,
  querySupplierTypeInfo,
  queryUserInfo,
  recoverSupplier,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';

const { Item } = Form;
const { Option } = Select;

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const location = useLocation<any>();
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [userInfo, setUserInfo] = useState([]);
  const [authTree, setAuthTree] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: tableColumns = [
    {
      title: '角色名称',
      align: 'center',
      dataIndex: 'roleName',
      key: 'roleName',
      width: 200,
    },
    {
      title: '涉及人员列表',
      align: 'center',
      dataIndex: 'userName',
      key: 'userName',
      width: 200,
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="RolePermission-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="RolePermission-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const handleEdit = useCallback(
    (record) => {
      queryRoleFind(record.id)
        .then((res) => {
          if (res.success) {
            setCheckedKeys(res?.data?.authIds);
            setShowModal(true);
            setModalTitle('编辑角色');
            modalForm.setFieldsValue({
              ...record,
              userIds: res?.data?.userIds,
            });
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    [showModal, modalTitle, modalForm, checkedKeys],
  );
  const handleAdd = useCallback(() => {
    setShowModal(true);
    setCheckedKeys([]);
    setModalTitle('新增角色');
    modalForm.resetFields();
  }, [showModal, modalTitle, modalForm, checkedKeys]);
  const handleDelete = useCallback((record) => {
    deleteRole(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleModalOk = useCallback(() => {
    modalForm.validateFields().then(() => {
      if (modalTitle === '新增角色') {
        insertRole({
          ...modalForm?.getFieldsValue(),
          authIds: checkedKeys,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        modifyRole({
          ...modalForm?.getFieldsValue(),
          authIds: checkedKeys,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    });
  }, [modalTitle, modalForm, showModal, userInfo, checkedKeys]);
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    queryRolePageInfo({
      ...form.getFieldsValue(),
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(res?.data);
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [form, pageParams, dataSource]);
  const getSelectFields = useCallback(() => {
    queryUserInfo()
      .then((res) => {
        if (res.success) {
          setUserInfo(
            res?.data?.map((item) => {
              return { label: item.userName, value: item.id };
            }),
          );
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    authTreeJoin()
      .then((res) => {
        if (res.success) {
          const formatAuthTree = (data) => {
            return data?.map((item) => {
              return {
                title: item.authName,
                key: item.id,
                children: item?.children?.length > 0 ? formatAuthTree(item.children) : [],
              };
            });
          };
          const result = formatAuthTree(res.data);
          setAuthTree(result);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [userInfo, authTree]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);

  const layout: any = {
    requiredMark: true,
    labelCol: { span: 10 },
    wrapperCol: { span: 14 },
    labelAlign: 'left',
  };

  const onCheck = (checkedKeysValue) => {
    setCheckedKeys(checkedKeysValue);
  };
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="角色权限管理" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={18}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="RolePermission-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <AuthorityComponent type="RolePermission-Newlyadded">
              <Button type="primary" onClick={() => handleAdd()}>
                新增角色
              </Button>
            </AuthorityComponent>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm} {...layout}>
          <Item name="id" hidden></Item>
          <Row gutter={32}>
            <Col span={8}>
              <Item name="roleName" required label="角色名称" rules={[{ required: true, message: '请输入角色名称' }]}>
                <Input></Input>
              </Item>
            </Col>
            <Col span={24}>
              <Item
                name="userIds"
                required
                label="涉及人员列表"
                rules={[{ required: true, message: '请勾选涉及人员列表' }]}
                labelCol={{ span: 3 }}
                wrapperCol={{ span: 21 }}
              >
                <Checkbox.Group options={userInfo} />
              </Item>
            </Col>
            <Col span={24}>
              <Item name="authIds" required label="角色功能列表" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                <div style={{ border: '1px solid #ccc', minHeight: 450, maxHeight: 450, overflowY: 'auto' }}>
                  <Tree
                    checkable
                    defaultExpandAll={false}
                    onCheck={onCheck}
                    checkedKeys={checkedKeys} // 勾选中
                    treeData={authTree}
                  />
                </div>
              </Item>
            </Col>
          </Row>
        </Form>
      </ModalDiv>
    </>
  );
};
