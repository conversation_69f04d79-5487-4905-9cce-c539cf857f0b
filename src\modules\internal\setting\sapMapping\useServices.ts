import { useRef, useState, useEffect } from 'react';
import { querySalesSapBuCodeList, delSalesSapBuCode, submitSalesSapBuCode } from '@/app/request/apiInternal';
import { history } from 'umi';
import { message, Form } from 'antd';
import moment from 'moment';
import { getTableScroll } from '@/tools/utils';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [orderStatus, setOrderStatus] = useState([]);
  const [search, setSearch] = useState(<any>{
    status: '',
    orderType: '',
    productCategory: '',
    keyword: '',
    searchFromDate: '',
    searchToDate: '',
  });
  const [id, setId] = useState(null);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  //编辑/新增
  const onSubmitVender = (parameter) => {
    submitSalesSapBuCode({
      ...parameter,
      id: id ? id : '',
    }).then((res) => {
      if (res.success) {
        getTable();
        message.success(res.msg);
      } else {
        message.warning(res.msg);
      }
    });
  };
  //删除
  const handleDelete = (id: string) => {
    delSalesSapBuCode(id).then((res) => {
      if (res.success) {
        getTable();
        message.success(res.msg);
      } else {
        message.warning(res.msg);
      }
    });
  };

  const getTable = () => {
    //sap映射管理
    querySalesSapBuCodeList({
      ...form.getFieldsValue(),
      ...pageParams,
    }).then((res) => {
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
        setPageParams({
          pageIndex: res?.pageIndex,
          pageSize: res?.pageSize,
          pageCount: res?.pageCount,
        });
      } else {
        message.warning(res.msg);
      }
    });
  };
  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      // 清除订阅
      window.removeEventListener('resize', resize);
    };
  }, []);
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex, pageParams.pageSize, pageParams.pageCount]);
  //翻页：重置current
  const onPageChange = (pagination, filters, sorter) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const onValuesChange = (changedValues, allValues) => {
    setSearch({ ...allValues });
  };
  const onSearch = () => {
    if (pageParams.pageIndex != 1) {
      setPageParams({
        pageIndex: 1,
      });
    } else {
      getTable();
    }
  };
  return {
    form,
    search,
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onValuesChange,
    onPageChange,
    getTable,
    onSubmitVender,
    handleDelete,
    pageParams,
    setPageParams,
    setId,
  };
};
