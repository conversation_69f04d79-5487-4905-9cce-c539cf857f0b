import { useLocation } from 'umi';
import { But<PERSON>, Card, Col, Form, Row, Input, message, Modal, Popconfirm, Select, Table } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv, { formProps, tableColumns } from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  deleteClearing,
  deleClearing,
  deleSupplier,
  deleteSupplier,
  insertQuerySupplier,
  queryClearingList,
  querySupplierList,
  querySupplierTypeInfo,
  recoverClearing,
  recoverSupplier,
  insertClearingInfo,
  modifyClearingInfo,
  exportClearingList,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';

const { Item } = Form;
const { TextArea } = Input;
const { Option } = Select;

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const location = useLocation<any>();
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [supplierType, setSupplierType] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: tableColumns = [
    {
      title: '结算公司代码',
      align: 'center',
      dataIndex: 'clearingCompanyCode',
      key: 'clearingCompanyCode',
      width: 200,
    },
    {
      title: '结算公司名称',
      align: 'center',
      dataIndex: 'clearingCompanyName',
      key: 'clearingCompanyName',
      width: 150,
    },
    {
      title: '公司代码',
      align: 'center',
      dataIndex: 'companyCode',
      key: 'companyCode',
      width: 150,
    },
    {
      title: '区域',
      align: 'center',
      dataIndex: 'region',
      key: 'region',
      width: 150,
    },
    {
      title: '结算公司状态',
      align: 'center',
      key: 'enable',
      dataIndex: 'enable',
      width: 150,
      render: (item, record) => {
        return <div>{item == 1 ? '启用' : '禁用'}</div>;
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 200,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="Settlement-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="Settlement-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
            <AuthorityComponent type="Settlement-Disable">
              <Button type="link" onClick={() => handleStatus(record)}>
                {record?.enable == 1 ? '禁用' : '启用'}
              </Button>
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const handleEdit = useCallback(
    (record) => {
      setShowModal(true);
      setModalTitle('编辑结算公司');
      modalForm.setFieldsValue(record);
    },
    [showModal, modalTitle, modalForm],
  );
  const handleAdd = useCallback(() => {
    setShowModal(true);
    setModalTitle('新增结算公司');
    modalForm.resetFields();
  }, [showModal, modalTitle, modalForm]);
  const handleDelete = useCallback((record) => {
    deleteClearing(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleStatus = useCallback((record) => {
    if (record.enable == 1) {
      // 禁用
      deleClearing(record.id)
        .then((res) => {
          if (res.success) {
            message.success('禁用成功');
            getTable();
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    } else {
      // 启用
      recoverClearing(record.id)
        .then((res) => {
          if (res.success) {
            message.success('启用成功');
            getTable();
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    }
  }, []);
  const handleModalOk = useCallback(() => {
    modalForm.validateFields().then(() => {
      if (modalTitle === '新增结算公司') {
        insertClearingInfo({
          ...modalForm.getFieldsValue(),
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        modifyClearingInfo({
          ...modalForm.getFieldsValue(),
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    });
  }, [modalForm, modalTitle, showModal]);
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    queryClearingList({
      ...form.getFieldsValue(),
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(res?.data);
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [pageParams, dataSource, form]);
  const getSelectFields = useCallback(() => {
    querySupplierTypeInfo()
      .then((res) => {
        if (res.success) {
          setSupplierType(res?.data);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [supplierType]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
    labelAlign: 'left',
  };
  const exportClearing = () => {
    exportClearingList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999,
    }).then((res) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '结算信息管理.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="结算公司信息管理" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="enable" label="结算公司状态">
                <Select allowClear>
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Item>
            </Col>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={12}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="Settlement-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <>
              <Button type="primary" onClick={() => exportClearing()} style={{ marginRight: '5px' }}>
                导出
              </Button>
              <AuthorityComponent type="Settlement-Newlyadded">
                <Button type="primary" onClick={() => handleAdd()}>
                  新增结算公司
                </Button>
              </AuthorityComponent>
            </>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm} {...layout} initialValues={{ enable: 1 }}>
          <Item name="id" hidden></Item>
          <Row gutter={32}>
            <Col span={12}>
              <Item
                name="clearingCompanyCode"
                required
                label="结算公司代码"
                rules={[{ required: true, message: '请输入结算公司代码' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="clearingCompanyName"
                required
                label="结算公司名称"
                rules={[{ required: true, message: '请输入结算公司名称' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="companyCode"
                required
                label="公司代码"
                rules={[{ required: true, message: '请输入公司代码' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item name="region" required label="区域" rules={[{ required: true, message: '请选择区域' }]}>
                <Select>
                  <Option value="东区">东区</Option>
                  <Option value="北区">北区</Option>
                  <Option value="西南区">西南区</Option>
                </Select>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="enable"
                required
                label="结算公司状态"
                rules={[{ required: true, message: '请选择结算公司状态' }]}
              >
                <Select allowClear>
                  <Option value={1}>已启用</Option>
                  <Option value={0}>已禁用</Option>
                </Select>
              </Item>
            </Col>
            <Col span={24}>
              <Item
                name="invoicingInfo"
                required
                label="开票信息"
                rules={[{ required: true, message: '请输入开票信息' }]}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <TextArea autoSize={{ minRows: 2, maxRows: 6 }} />
              </Item>
            </Col>
          </Row>
        </Form>
      </ModalDiv>
    </>
  );
};
