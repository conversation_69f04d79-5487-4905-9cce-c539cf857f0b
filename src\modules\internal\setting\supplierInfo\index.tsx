import { useLocation } from 'umi';
import { But<PERSON>, Card, Col, Form, Row, Input, message, Popconfirm, Select, Table } from 'antd';
import { DeleteOutlined, PlusSquareOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState } from 'react';
import FormDiv from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  deleSupplier,
  deleteSupplier,
  insertQuerySupplier,
  modifySupplierInfo,
  queryClearingInfo,
  querySupplierFind,
  querySupplierList,
  querySupplierTypeInfo,
  recoverSupplier,
  exportSupplierList,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';
import './style.less';

const { Item } = Form;
const { Option } = Select;

const modalDataSource1 = {
  flag: Math.random(),
  userName: '',
  gid: '',
  email: '',
  phone: '',
  supplierName: '',
  userState: 1,
};
const modalDataSource2 = { flag: Math.random(), compareStr: '' };
const modalDataSource3 = { flag: Math.random(), clearingCompanyName: '', clearingCompanyCode: '' };

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [sapFlag, setSapFlag] = useState(true);
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [supplierType, setSupplierType] = useState([]);
  const [clearing, setClearing] = useState([]);
  const [clearingCompany, setClearingCompany] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [modalData1, setModalData1] = useState([]);
  const [modalData2, setModalData2] = useState([]);
  const [modalData3, setModalData3] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: any = [
    {
      title: '供应商名称',
      align: 'center',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 200,
    },
    {
      title: '供应商代码',
      align: 'center',
      dataIndex: 'supplierNo',
      key: 'supplierNo',
      width: 150,
    },
    {
      title: '供应商类型',
      align: 'center',
      dataIndex: 'supplierType',
      key: 'supplierType',
      width: 150,
    },
    {
      title: '供应商状态',
      align: 'center',
      key: 'enable',
      dataIndex: 'enable',
      width: 150,
      render: (item, record) => {
        return <div>{item == 1 ? '启用' : '禁用'}</div>;
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (item, record) => {
        return (
          <>
            <AuthorityComponent type="SupplierInfo-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="SupplierInfo-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
            <AuthorityComponent type="SupplierInfo-Disable">
              <Button type="link" onClick={() => handleStatus(record)}>
                {record?.enable == 1 ? '禁用' : '启用'}
              </Button>
            </AuthorityComponent>
          </>
        );
      },
    },
  ];
  const modalColumns1: any = [
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>登录账号</span>
        </>
      ),
      align: 'center',
      dataIndex: 'gid',
      key: 'gid',
      width: 150,
      render: (value, record) => {
        return (
          <Input
            defaultValue={value}
            onBlur={(e) => handleAccountInfo(record, 'edit', 'gid', e.target.value)}
            onPressEnter={(e) => handleAccountInfo(record, 'edit', 'gid', e.target.value)}
          ></Input>
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>电子邮件</span>
        </>
      ),
      align: 'center',
      dataIndex: 'email',
      key: 'email',
      width: 150,
      render: (value, record) => {
        return (
          <Input
            defaultValue={value}
            onBlur={(e) => handleAccountInfo(record, 'edit', 'email', e.target.value)}
            onPressEnter={(e) => handleAccountInfo(record, 'edit', 'email', e.target.value)}
          ></Input>
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>联系人</span>
        </>
      ),
      align: 'center',
      dataIndex: 'userName',
      key: 'userName',
      width: 150,
      render: (value, record) => {
        return (
          <Input
            defaultValue={value}
            onBlur={(e) => handleAccountInfo(record, 'edit', 'userName', e.target.value)}
            onPressEnter={(e) => handleAccountInfo(record, 'edit', 'userName', e.target.value)}
          ></Input>
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>联系电话</span>
        </>
      ),
      align: 'center',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      render: (value, record) => {
        return (
          <Input
            defaultValue={value}
            onBlur={(e) => handleAccountInfo(record, 'edit', 'phone', e.target.value)}
            onPressEnter={(e) => handleAccountInfo(record, 'edit', 'phone', e.target.value)}
          ></Input>
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>账号状态</span>
        </>
      ),
      align: 'center',
      key: 'userState',
      dataIndex: 'userState',
      width: 150,
      render: (value, record) => {
        return (
          <Select
            style={{ width: '100%' }}
            defaultValue={value}
            onChange={(value) => handleAccountInfo(record, 'edit', 'userState', value)}
          >
            <Option value={1}>已启用</Option>
            <Option value={0}>已禁用</Option>
          </Select>
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 120,
      render: (item, record) => {
        return (
          <>
            <Popconfirm
              title="确定要删除？"
              onConfirm={() => handleAccountInfo(record, 'delete')}
              okText="确定"
              cancelText="取消"
            >
              <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
            </Popconfirm>
            <PlusSquareOutlined
              onClick={() => handleAccountInfo(record, 'add')}
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
            />
          </>
        );
      },
    },
  ];
  const modalColumns2: any = [
    {
      title: '货源点名称',
      align: 'center',
      dataIndex: 'compareStr',
      key: 'compareStr',
      render: (data, record) => {
        return (
          <div className="input">
            <Input
              disabled={!!record?.isFixed}
              defaultValue={data}
              onBlur={(e) => handleSourceName(record, 'edit', 'compareStr', e.target.value)}
              onPressEnter={(e) => handleSourceName(record, 'edit', 'compareStr', e.target.value)}
            ></Input>
          </div>
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (item, record) => {
        return (
          <>
            <Popconfirm
              title="确定要删除？"
              onConfirm={() => handleSourceName(record, 'delete')}
              okText="确定"
              cancelText="取消"
            >
              <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
            </Popconfirm>
            <PlusSquareOutlined
              onClick={() => handleSourceName(record, 'add')}
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
            />
          </>
        );
      },
    },
  ];
  const modalColumns3: any = [
    {
      title: '涉及结算公司',
      align: 'center',
      dataIndex: 'clearingCompanyName',
      key: 'clearingCompanyName',
      render: (data, record) => {
        return (
          <Select
            defaultValue={data}
            disabled={!!record?.isFixed}
            style={{ width: '100%' }}
            onChange={(value) => handleClearing(record, 'edit', 'clearingCompanyName', value)}
          >
            {clearingCompany?.map((item, index) => {
              return (
                <Option key={index} value={item?.clearingCompanyName}>
                  {item?.clearingCompanyName}
                </Option>
              );
            })}
          </Select>
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (item, record) => {
        return (
          <>
            <Popconfirm
              title="确定要删除？"
              onConfirm={() => handleClearing(record, 'delete')}
              okText="确定"
              cancelText="取消"
            >
              <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
            </Popconfirm>
            <PlusSquareOutlined
              onClick={() => handleClearing(record, 'add')}
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
            />
          </>
        );
      },
    },
  ];
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const handleAccountInfo = useCallback(
    (record?, status?, field?, value?) => {
      if (status === 'singleAdd') {
        setModalData1([{ ...modalDataSource1 }]);
      }
      if (status === 'delete') {
        const temp = [...modalData1];
        let result = temp.filter((item) => item.flag !== record.flag);
        setModalData1([...result]);
      }
      if (status === 'add') {
        const temp = [...modalData1];
        let result = [...temp, { ...modalDataSource1, flag: Math.random() }];
        result.forEach((item) => {
          item.flag = Math.random();
        });
        setModalData1([...result]);
      }
      if (status === 'edit') {
        // if (field == 'email' && !/^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(value)) {
        //   message.warning('电子邮件格式不正确！');
        // }
        // if (field == 'phone' && !/^1[3456789]\d{9}$/.test(value)) {
        //   message.warning('联系电话格式不正确！');
        // }
        const temp = [...modalData1];
        let result = temp.map((item) => {
          if (item.flag == record.flag) {
            item[field] = value;
          }
          return item;
        });
        result.forEach((item) => {
          item.flag = Math.random();
        });
        setModalData1([...result]);
      }
    },
    [modalData1, modalDataSource1],
  );
  const handleSourceName = useCallback(
    (record?, status?, field?, value?) => {
      if (status === 'singleAdd') {
        setModalData2([{ ...modalDataSource2 }]);
      }
      if (status === 'delete') {
        const temp = [...modalData2];
        let result = temp.filter((item) => item.flag !== record.flag);
        setModalData2([...result]);
      }
      if (status === 'add') {
        const temp = [...modalData2];
        let result = [...temp, { compareStr: '', flag: Math.random() }];
        setModalData2([...result]);
      }
      if (status === 'edit') {
        const temp = [...modalData2];
        let result = temp.map((item) => {
          if (item.flag == record.flag) {
            item[field] = value;
          }
          return item;
        });
        result.forEach((item) => {
          item.flag = Math.random();
        });
        setModalData2([...result]);
      }
    },
    [modalData2, modalDataSource2],
  );
  const handleClearing = useCallback(
    (record?, status?, field?, value?) => {
      if (status === 'singleAdd') {
        setModalData3([{ ...modalDataSource3 }]);
      }
      if (status === 'delete') {
        const temp = [...modalData3];
        let result = temp.filter((item) => item.flag !== record.flag);
        setModalData3([...result]);
      }
      if (status === 'add') {
        const temp = [...modalData3];
        let result = [...temp, { clearingCompanyName: '', flag: Math.random() }];
        setModalData3([...result]);
      }
      if (status === 'edit') {
        let temp = [...modalData3].map((item) => {
          if (item.flag == record.flag) {
            item.clearingCompanyName = value;
          }
          return item;
        });
        setModalData3([...temp]);
      }
    },
    [modalData3, modalDataSource3],
  );
  const handleEdit = useCallback(
    (record) => {
      setModalTitle('编辑供应商');
      querySupplierFind(record.id)
        .then((res) => {
          if (res.success) {
            setModalData1(
              res.data?.userList?.map((item) => {
                item.flag = Math.random();
                return item;
              }),
            );
            setModalData2(
              res.data?.sourceSiteList?.map((item) => {
                item.compareStr = item?.name;
                item.flag = Math.random();
                return item;
              }),
            );
            setModalData3(
              res.data?.clearCompanyList?.map((item) => {
                item.clearingCompanyName = item?.name;
                item.clearingCompanyCode = item?.code;
                item.flag = Math.random();
                return item;
              }),
            );
            setShowModal(true);
            modalForm.setFieldsValue({
              ...record,
              supplierNo: res.data?.supplierNo,
              supplierName: res.data?.supplierName,
              supplierType: res.data?.supplierType,
              isAccrual: res.data?.isAccrual,
              defaultClearingCompanyCode: res.data?.defaultClearingCompanyCode,
              enable: res.data?.enable,
              billDueDay: res.data?.billDueDay,
              isSap: record?.supplierType === '3rd Party' ? 0 : res.data?.isSap,
            });
            setSapFlag(record?.supplierType === 'EJV' ? true : false);
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    [modalData1, modalData2, modalData3, showModal, modalTitle, modalForm],
  );
  const handleAdd = useCallback(() => {
    setModalData1([]);
    setModalData2([]);
    setModalData3([]);
    setShowModal(true);
    setSapFlag(true);
    setModalTitle('新增供应商');
    modalForm.resetFields();
  }, [modalData1, modalData2, modalData3, showModal, modalTitle, modalForm]);
  const handleDelete = useCallback((record) => {
    deleteSupplier(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleStatus = useCallback((record) => {
    if (record.enable == 1) {
      // 禁用
      deleSupplier(record.id)
        .then((res) => {
          if (res.success) {
            message.success('禁用成功');
            getTable();
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    } else {
      // 启用
      recoverSupplier(record.id)
        .then((res) => {
          if (res.success) {
            message.success('启用成功');
            getTable();
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    }
  }, []);
  const handleModalOk = useCallback(() => {
    if (modalData1?.length > 0) {
      for (let i = 0; i < modalData1?.length; i++) {
        if (!modalData1[i]?.gid) {
          message.warning('供应商账号->登录账号,不可为空！');
          return;
        }
        if (!modalData1[i]?.email) {
          message.warning('供应商账号->电子邮件,不可为空！');
          return;
        }
        if (
          modalData1[i]?.email &&
          !/^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(modalData1[i]?.email)
        ) {
          message.warning('供应商账号->电子邮件,格式不正确！');
          return;
        }
        if (!modalData1[i]?.userName) {
          message.warning('供应商账号->联系人,不可为空');
          return;
        }
        if (!modalData1[i]?.phone) {
          message.warning('供应商账号->联系电话,不可为空！');
          return;
        }
        if (modalData1[i]?.phone && !/^1[3456789]\d{9}$/.test(modalData1[i]?.phone)) {
          message.warning('供应商账号->联系电话,格式不正确！');
          return;
        }
      }
    }
    modalForm.validateFields().then(() => {
      const temp = modalData3?.map((item) => item.clearingCompanyName);
      if (modalTitle == '新增供应商') {
        insertQuerySupplier({
          ...modalForm.getFieldsValue(),
          // defaultClearingCompanyCode: modalForm.getFieldValue('defaultClearingCompanyCode'),
          userList: modalData1,
          sourceSiteList: modalData2,
          clearingCompanyList: clearing?.filter((item) => temp.includes(item.clearingCompanyName)),
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
              setModalData1([]);
              setModalData2([]);
              setModalData3([]);
              setPageParams({ ...pageParams, pageIndex: 1 });
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        modifySupplierInfo({
          ...modalForm.getFieldsValue(),
          userList: modalData1,
          sourceSiteList: modalData2,
          clearingCompanyList: clearing?.filter((item) => temp.includes(item.clearingCompanyName)),
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
              setModalData1([]);
              setModalData2([]);
              setModalData3([]);
              setPageParams({ ...pageParams, pageIndex: 1 });
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    });
  }, [modalForm, modalData1, modalData2, modalData3]);
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    querySupplierList({
      ...form.getFieldsValue(),
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(res?.data);
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [pageParams, dataSource, form]);
  const getSelectFields = useCallback(() => {
    querySupplierTypeInfo()
      .then((res) => {
        if (res.success) {
          setSupplierType(res?.data);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    queryClearingInfo('')
      .then((res) => {
        if (res.success) {
          setClearingCompany(
            res?.data?.map((item) => {
              item.clearingCompanyName = item.name;
              item.clearingCompanyCode = item.code;
              item.flag = Math.random();
              return item;
            }),
          );
          setClearing(
            res?.data?.map((item) => {
              item.clearingCompanyName = item.name;
              item.clearingCompanyCode = item.code;
              delete item.isFixed;
              return item;
            }),
          );
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [supplierType, clearingCompany]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);
  const handleSupplierType = (v) => {
    if (v === '3rd Party') {
      modalForm.setFieldsValue({ isSap: 0 });
    }
    setSapFlag(v === 'EJV' ? true : false);
  };
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
    labelAlign: 'left',
  };
  //导出
  const exportSupplier = () => {
    exportSupplierList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999,
    }).then((res) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '供应商信息管理.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="供应商信息管理" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="supplierType" label="供应商类型">
                <Select>
                  {supplierType?.map((item, index) => {
                    return (
                      <Option key={index} value={item.supplierType}>
                        {item.supplierType}
                      </Option>
                    );
                  })}
                </Select>
              </Item>
            </Col>
            <Col span={6}>
              <Item name="enable" label="供应商状态">
                <Select>
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Item>
            </Col>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input />
              </Item>
            </Col>
            <Col span={6}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="SupplierInfo-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <AuthorityComponent type="SupplierInfo-Newlyadded">
              <Button type="primary" onClick={() => exportSupplier()} style={{ marginRight: '5px' }}>
                导出
              </Button>
              <Button type="primary" onClick={handleAdd}>
                新增供应商
              </Button>
            </AuthorityComponent>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        width={1200}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm} {...layout}>
          <Item name="id" hidden></Item>
          <Row gutter={32}>
            <Col span={12}>
              <Item
                name="supplierNo"
                required
                label="供应商编号"
                rules={[{ required: true, message: '请输入供应商编号' }]}
              >
                <Input></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="supplierName"
                required
                label="供应商名称"
                rules={[{ required: true, message: '请输入供应商名称' }]}
              >
                <Input></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="supplierType"
                required
                label="供应商类型"
                rules={[{ required: true, message: '请选择供应商类型' }]}
              >
                <Select onChange={handleSupplierType}>
                  {supplierType?.map((item, index) => {
                    return (
                      <Option key={index} value={item.supplierType}>
                        {item.supplierType}
                      </Option>
                    );
                  })}
                </Select>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="isSap"
                required
                label="是否使用SAP"
                rules={[{ required: true, message: '请选择是否使用SAP' }]}
              >
                <Select disabled={!sapFlag}>
                  <Option value={1}>是</Option>
                  <Option value={0}>否</Option>
                </Select>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="isAccrual"
                required
                label="是否参与计提"
                rules={[{ required: true, message: '请选择是否参与计提' }]}
              >
                <Select>
                  <Option value={1}>是</Option>
                  <Option value={0}>否</Option>
                </Select>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="defaultClearingCompanyCode"
                required
                label="默认结算公司"
                rules={[{ required: true, message: '请选择默认结算公司' }]}
              >
                <Select style={{ width: '100%' }}>
                  {clearingCompany?.map((item, index) => {
                    return (
                      <Option key={index} value={item?.clearingCompanyCode}>
                        {item?.clearingCompanyName}
                      </Option>
                    );
                  })}
                </Select>
              </Item>
            </Col>
            <Col span={12}>
              <Item name="billDueDay" label="账期开始日">
                <Input disabled></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item name="enable" required label="供应商状态" rules={[{ required: true, message: '请选择供应商状态' }]}>
                <Select>
                  <Option value={1}>已启用</Option>
                  <Option value={0}>已禁用</Option>
                </Select>
              </Item>
            </Col>
          </Row>
        </Form>
        <TableTitle
          icon={false}
          title="供应商账号"
          right={
            modalData1?.length == 0 && (
              <Button onClick={() => handleAccountInfo(modalDataSource1, 'singleAdd')}>新增账号</Button>
            )
          }
        ></TableTitle>
        <Table
          rowKey="flag"
          columns={modalColumns1}
          dataSource={modalData1}
          pagination={false}
          scroll={{ y: 200 }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
        ></Table>
        <TableTitle
          icon={false}
          title="供应商货源点"
          right={
            modalData2?.length == 0 && (
              <Button onClick={() => handleSourceName(modalDataSource2, 'singleAdd')}>新增货源点</Button>
            )
          }
        ></TableTitle>
        <Table
          rowKey="flag"
          columns={modalColumns2}
          dataSource={modalData2}
          pagination={false}
          scroll={{ y: 200 }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
        ></Table>
        <TableTitle
          icon={false}
          title="供应商涉及结算公司"
          right={
            modalData3?.length == 0 && (
              <Button onClick={() => handleClearing(modalDataSource3, 'singleAdd')}>新增结算公司</Button>
            )
          }
        ></TableTitle>
        <Table
          rowKey="flag"
          columns={modalColumns3}
          dataSource={modalData3}
          pagination={false}
          scroll={{ y: 200 }}
          rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
        ></Table>
      </ModalDiv>
    </>
  );
};
