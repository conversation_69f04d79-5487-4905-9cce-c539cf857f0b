import {
  Button,
  Upload,
  Row,
  Col,
  Space,
  Form,
  Input,
  Select,
  DatePicker,
  Table,
  message,
  InputNumber,
  Popconfirm,
  Tooltip,
  Tag,
  Card,
  Modal,
} from 'antd';
import styled from 'styled-components';
import React, { memo, useState, useEffect, useRef, useCallback } from 'react';
import moment from 'moment';
import { useLocation } from 'umi';
import TableTitle from '../../../../components/TableTitle';
import { Props } from 'ahooks/lib/useControllableValue';
import { showOptionLabel, classList } from '@/components/StateVerification';
import { ButtonFooter, CardForm, CardTable, HeaderTitle } from '../../ReportForm/reportDataMaintenance/style';
import { AccTable } from '../../internalHome/style';
import useServices from './useServices';
import TableDiv from '../component/TableDiv';
import FormDiv from '../component/FormDiv';
import { QuestionCircleOutlined } from '@ant-design/icons';
import PaginationDiv from '../component/PaginationDiv';

const { Item } = Form;

export default memo(function (props) {
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };
  const {
    data,
    pageParams,
    setPageParams,
    setId,
    current,
    pageSize,
    total,
    form,
    onSearch,
    onSubmitVender,
    handleDelete,
  } = useServices(props);
  const [formPO] = Form.useForm();
  const [isSupplement, setSupplement] = useState(false);
  const [title, setTitle] = useState('');

  const columns: any = [
    {
      title: 'VENDOR',
      align: 'center',
      ellipsis: true,
      dataIndex: 'vendor',
      key: 'vendor',
      width: 100,
    },
    {
      title: 'Sales Org', //必填项
      align: 'center',
      ellipsis: true,
      dataIndex: 'salesOrg',
      key: 'salesOrg',
      width: 100,
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (item, record) => {
        return (
          <>
            <Button type="link" onClick={() => onEdit(record)}>
              修改
            </Button>
            <Popconfirm
              key="del"
              title="确定删除该条信息吗？"
              icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() => handleDelete(record.id)}
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];
  const handlePrev = () => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  };
  const handleNext = () => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  };
  const onSubmitPO = (value) => {
    console.log(value);
    onSubmitVender(value);
    formPO.resetFields();
    setSupplement(false);
  };

  const onClose = () => {
    setSupplement(false);
    formPO.resetFields();
  };
  //修改
  const onEdit = (record) => {
    setTitle('修改信息');
    setSupplement(true);
    setId(record.id);
    formPO.setFieldsValue({
      vendor: record.vendor,
      salesOrg: record.salesOrg,
    });
  };
  //添加
  const onAddto = () => {
    setId(null); //清除id
    setTitle('添加信息');
    setSupplement(true);
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="供应商映射管理" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={18}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <Button
                  style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                  onClick={() => onSearch()}
                >
                  搜索
                </Button>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <Button type="primary" onClick={() => onAddto()}>
              新增
            </Button>
          }
          columns={columns}
          dataSource={data}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 190 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
        <Modal
          width={800}
          title={<div style={{ textAlign: 'center', fontSize: 18, fontWeight: 700 }}>{title}</div>}
          visible={isSupplement}
          onCancel={onClose}
          footer
        >
          <Form labelCol={{ flex: '100px' }} onFinish={onSubmitPO} form={formPO}>
            <Row>
              <Col span={11}>
                <Form.Item
                  name="vendor"
                  label="VENDOR"
                  rules={[
                    { required: true },
                    {
                      pattern: /^[^\s]*$/,
                      message: '禁止输入空格',
                    },
                  ]}
                >
                  <Input placeholder="VENDOR" />
                </Form.Item>
              </Col>
              <Col span={11}>
                <Form.Item
                  name="salesOrg"
                  label="Sales Org"
                  rules={[
                    { required: true },
                    {
                      pattern: /^[^\s]*$/,
                      message: '禁止输入空格',
                    },
                  ]}
                >
                  <Input placeholder="Sales Org" />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={10} push={5}>
                <Button type="primary" htmlType="submit">
                  提交
                </Button>
              </Col>
              <Col span={10} push={5} onClick={onClose}>
                <Button>取消</Button>
              </Col>
            </Row>
          </Form>
        </Modal>
      </Card>
    </>
  );
});
