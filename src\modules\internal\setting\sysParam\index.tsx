import { But<PERSON>, Card, Col, Form, Row, Input, message, DatePicker, Checkbox, InputNumber } from 'antd';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv, { formProps, tableColumns } from '../component/FormDiv';
import { insertParamAddInfo, modifyParamInfo, queryParamList } from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import moment from 'moment';

const { Item } = Form;

export default () => {
  const [form] = Form.useForm();

  const handleSave = useCallback(() => {
    form.validateFields().then(() => {
      modifyParamInfo({
        ...form.getFieldsValue(),
        isAutoEmail: form.getFieldValue('isAutoEmail') ? 1 : 0,
        systemClosingDate: form.getFieldValue('systemClosingDate')?.format('YYYY-MM-DD'),
      })
        .then((res) => {
          if (res.success) {
            message.success('保存成功');
            getTable();
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    });
  }, [form]);
  const getTable = useCallback(() => {
    queryParamList()
      .then((res) => {
        if (res.success) {
          form.setFieldsValue({
            ...res.data,
            systemClosingDate: res.data.systemClosingDate && moment(res.data.systemClosingDate),
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  useEffect(() => {
    getTable();
  }, []);

  return (
    <Card style={{ minHeight: 800, position: 'relative' }}>
      <div style={{ textAlign: 'center', fontSize: 22, fontWeight: 700 }}>系统参数设置</div>
      <FormDiv title="参数设置" form={form} initialValues={{ isAutoEmail: true }}>
        <Row gutter={32} justify="center">
          <Col span={10}>
            <Item name="taxRate" required label="税率" rules={[{ required: true, message: '请输入税率!' }]}>
              <InputNumber style={{ width: '100%' }} />
            </Item>
          </Col>
        </Row>
        <Row gutter={32} justify="center">
          <Col span={10}>
            <Item name="systemClosingDate" label="本账期截止日期">
              <DatePicker style={{ width: '100%' }} />
            </Item>
          </Col>
        </Row>
        <Row gutter={32} justify="center">
          <Col span={10}>
            <Item
              name="sapRequistitioner"
              required
              label="SAP Requistitioner"
              rules={[{ required: true, message: '请输入SAP Requistitioner!' }]}
            >
              <Input allowClear />
            </Item>
          </Col>
        </Row>
        <Row gutter={32} justify="center">
          <Col span={10}>
            <Item name="isAutoEmail" valuePropName="checked" label="是否自动发送邮件">
              <Checkbox>开启</Checkbox>
            </Item>
          </Col>
        </Row>
        <Row gutter={32} justify="center">
          <Col span={10}>
            <Item
              name="compareFailedEmail"
              rules={[
                {
                  pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                  message: '邮箱格式不正确',
                },
              ]}
              label="对账失败邮箱抄送人邮箱"
            >
              <Input allowClear />
            </Item>
          </Col>
        </Row>
        <Row gutter={32} justify="center">
          <Col span={10}>
            <Item
              name="notesOnInvoic"
              required
              label="开票注意事项"
              rules={[{ required: true, message: '请输入开票注意事项' }]}
            >
              <Input.TextArea allowClear />
            </Item>
          </Col>
        </Row>
        <Row gutter={32} justify="center">
          <Col span={10}>
            <div style={{ textAlign: 'center', marginTop: 40 }}>
              <AuthorityComponent type="SysParam-Preservation">
                <Button
                  style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                  onClick={handleSave}
                >
                  保存
                </Button>
              </AuthorityComponent>
            </div>
          </Col>
        </Row>
      </FormDiv>
    </Card>
  );
};
