import { useLocation } from 'umi';
import { But<PERSON>, Card, Col, Form, Row, Input, message, Modal, Popconfirm, Select, Table, Radio, Checkbox } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState, useMemo, useRef, ReactNode } from 'react';
import FormDiv, { formProps, tableColumns } from '../component/FormDiv';
import ModalDiv from '../component/ModalDiv';
import TableDiv from '../component/TableDiv';
import {
  deleSupplier,
  deleteSupplier,
  deleteUser,
  deleUser,
  insertQuerySupplier,
  insertUserInfo,
  modifyUserInfo,
  queryRoleInfo,
  querySupplierList,
  querySupplierNameInfo,
  querySupplierTypeInfo,
  queryUserFindInfo,
  queryUserPageInfo,
  recoverSupplier,
  recoverUser,
  resetPass,
  exportUserList,
} from '@/app/request/apiInternal';
import AuthorityComponent from '@/components/AuthorityComponent';
import PaginationDiv from '../component/PaginationDiv';
import TableTitle from '@/components/TableTitle';

const { Item } = Form;
const { Option } = Select;

export default () => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [modalTitle, setModalTitle] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [supplierName, setSupplierName] = useState([]);
  const [supplierType, setSupplierType] = useState([]);
  const [accountType, setAccountType] = useState('Outer');
  const [roleCheckBox, setRoleCheckBox] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [pageParams, setPageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    pageCount: 0,
  });
  const columns: tableColumns = [
    {
      title: '用户账号',
      align: 'center',
      dataIndex: 'gid',
      key: 'gid',
      width: 250,
    },
    {
      title: '账号类型',
      align: 'center',
      dataIndex: 'userType',
      key: 'userType',
      width: 100,
      render: (v) => {
        return v == 'Inner' ? '内部用户' : '外部用户';
      },
    },
    {
      title: '供应商名称',
      align: 'center',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 200,
      render: (text, record) => {
        return <div>{record?.userType == 'Outer' ? text : ''}</div>;
      },
    },
    {
      title: '电子邮件',
      align: 'center',
      dataIndex: 'email',
      key: 'email',
      width: 250,
    },
    {
      title: '姓名/联系人',
      align: 'center',
      dataIndex: 'userName',
      key: 'userName',
      width: 100,
    },
    {
      title: '所属角色',
      align: 'center',
      dataIndex: 'roleName',
      key: 'roleName',
      width: 100,
      render: (data, record) => {
        return record?.userType == 'Inner' ? data : '';
      },
    },
    {
      title: '账号状态',
      align: 'center',
      key: 'userState',
      dataIndex: 'userState',
      width: 80,
      render: (item, record) => {
        return <div>{item == 1 ? '启用' : '禁用'}</div>;
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 230,
      render: (item, record) => {
        return (
          <div style={{ textAlign: 'left' }}>
            <AuthorityComponent type="UserAccount-Modify">
              <Button type="link" onClick={() => handleEdit(record)}>
                修改
              </Button>
            </AuthorityComponent>
            <AuthorityComponent type="UserAccount-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条记录？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleDelete(record)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AuthorityComponent>
            <AuthorityComponent type="UserAccount-Disable">
              <Button type="link" onClick={() => handleStatus(record)}>
                {record?.userState == 1 ? '禁用' : '启用'}
              </Button>
            </AuthorityComponent>
            {record?.userType == 'Outer' && (
              <AuthorityComponent type="UserAccount-Reset">
                <Popconfirm
                  key="reset"
                  title="确定重置密码？"
                  icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                  onConfirm={() => resetPassword(record)}
                >
                  <Button type="link" danger>
                    重置密码
                  </Button>
                </Popconfirm>
              </AuthorityComponent>
            )}
          </div>
        );
      },
    },
  ];
  const handleSearch = useCallback(() => {
    getTable();
  }, []);
  const resetPassword = useCallback((record) => {
    resetPass({ id: record.id })
      .then((res) => {
        if (res.success) {
          message.success('密码重置成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleEdit = useCallback(
    (record) => {
      queryUserFindInfo(record.id)
        .then((res) => {
          if (res.success) {
            setShowModal(true);
            setModalTitle('编辑账号');
            setAccountType(record.userType);
            modalForm.setFieldsValue({
              ...record,
              supplierName:
                record?.userType == 'Outer' ? { label: res?.data?.supplierName, value: res?.data?.supplierId } : null,
              roleIds:
                res?.data?.roleFindInfo?.map((item) => {
                  return item.roleId;
                }) ?? [],
            });
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    [showModal, modalTitle, accountType, modalForm],
  );
  const handleAdd = useCallback(() => {
    setShowModal(true);
    setAccountType('Outer');
    setModalTitle('新增账号');
    modalForm.resetFields();
  }, [showModal, modalTitle, modalForm, accountType]);
  const handleDelete = useCallback((record) => {
    deleteUser(record.id)
      .then((res) => {
        if (res.success) {
          message.success('删除成功');
          getTable();
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  const handleStatus = useCallback((record) => {
    if (record.userState == 1) {
      // 禁用
      deleUser({ id: record.id })
        .then((res) => {
          if (res.success) {
            message.success('禁用成功');
            getTable();
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    } else {
      // 启用
      recoverUser({ id: record.id })
        .then((res) => {
          if (res.success) {
            message.success('启用成功');
            getTable();
          } else {
            message.error(res?.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    }
  }, []);
  const handleModalOk = () => {
    modalForm.validateFields().then(() => {
      if (modalTitle === '新增账号') {
        insertUserInfo({
          ...modalForm.getFieldsValue(),
          supplierName: modalForm?.getFieldValue('supplierName')?.label,
          supplierId: modalForm?.getFieldValue('supplierName')?.value,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        modifyUserInfo({
          ...modalForm.getFieldsValue(),
          supplierName: modalForm.getFieldValue('supplierName')?.label,
          supplierId: modalForm.getFieldValue('supplierName')?.value,
        })
          .then((res) => {
            if (res.success) {
              message.success(`${modalTitle}成功`);
              getTable();
              setShowModal(false);
            } else {
              message.error(res?.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    });
  };
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, [showModal]);
  const getTable = useCallback(() => {
    queryUserPageInfo({
      ...form.getFieldsValue(),
      ...pageParams,
    })
      .then((res) => {
        if (res.success) {
          setDataSource(res?.data);
          setPageParams({
            pageIndex: res?.pageIndex,
            pageSize: res?.pageSize,
            pageCount: res?.pageCount,
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [pageParams, dataSource, form]);
  const getSelectFields = useCallback(() => {
    querySupplierTypeInfo()
      .then((res) => {
        if (res.success) {
          setSupplierType(res?.data);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    querySupplierNameInfo()
      .then((res) => {
        if (res.success) {
          setSupplierName(res?.data);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    queryRoleInfo()
      .then((res) => {
        if (res.success) {
          setRoleCheckBox(
            res.data?.map((item) => {
              return { label: item.roleName, value: item.id };
            }),
          );
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [supplierType, supplierName, roleCheckBox]);
  const handlePrev = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex - 1 });
  }, [pageParams]);
  const handleNext = useCallback(() => {
    setPageParams({ ...pageParams, pageIndex: pageParams?.pageIndex + 1 });
  }, [pageParams]);
  const handleAccountType = useCallback(
    (e) => {
      setAccountType(e.target.value);
    },
    [accountType],
  );
  useEffect(() => {
    getTable();
  }, [pageParams.pageIndex]);
  useEffect(() => {
    getSelectFields();
  }, []);
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    labelAlign: 'left',
  };
  //导出
  const exportUser = () => {
    exportUserList({
      ...form.getFieldsValue(),
      pageIndex: 1,
      pageSize: 9999,
    }).then((res) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '用户账号管理.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };
  return (
    <>
      <Card style={{ minHeight: 800, position: 'relative' }}>
        <FormDiv title="用户账号管理" form={form}>
          <Row gutter={32}>
            <Col span={6}>
              <Item name="userType" label="账号类型">
                <Select allowClear>
                  <Option value="Inner">内部用户</Option>
                  <Option value="Outer">外部用户</Option>
                </Select>
              </Item>
            </Col>
            <Col span={6}>
              <Item name="userState" label="账号状态">
                <Select allowClear>
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Item>
            </Col>
            <Col span={6}>
              <Item name="keyWord" label="关键字">
                <Input allowClear />
              </Item>
            </Col>
            <Col span={6}>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <AuthorityComponent type="UserAccount-Search">
                  <Button
                    style={{ backgroundColor: '#ff8d1a', border: '1px solid #ff8d1a', color: 'white' }}
                    onClick={handleSearch}
                  >
                    搜索
                  </Button>
                </AuthorityComponent>
              </div>
            </Col>
          </Row>
        </FormDiv>
        <TableDiv
          rightTop={
            <>
              <Button type="primary" onClick={() => exportUser()} style={{ marginRight: '5px' }}>
                导出
              </Button>
              <AuthorityComponent type="UserAccount-Newlyadded">
                <Button type="primary" onClick={handleAdd}>
                  新增账号
                </Button>
              </AuthorityComponent>
            </>
          }
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record?.id}
          pagination={pageParams}
          scroll={{ x: columns?.length * 200 }}
        ></TableDiv>
        <PaginationDiv
          pageIndex={pageParams.pageIndex}
          pageCount={pageParams.pageCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        ></PaginationDiv>
      </Card>
      <ModalDiv
        title={modalTitle}
        visible={showModal}
        handleModalCancel={handleModalCancel}
        handleModalOk={handleModalOk}
      >
        <TableTitle icon={false} title="基本信息"></TableTitle>
        <Form form={modalForm} {...layout} initialValues={{ userType: 'Outer', userState: 1 }}>
          <Item name="id" hidden></Item>
          <Item name="supplierId" hidden></Item>
          <Row gutter={32}>
            <Col span={12}>
              <Item name="userType" required label="账号类型" rules={[{ required: true, message: '请选择账号类型' }]}>
                <Radio.Group style={{ width: 500 }} onChange={(e) => handleAccountType(e)}>
                  <Radio value={'Inner'}>内部用户</Radio>
                  <Radio value={'Outer'}>外部用户</Radio>
                </Radio.Group>
              </Item>
            </Col>
            <Col span={12}></Col>
            <Col span={12}>
              <Item name="gid" required label="账号名称" rules={[{ required: true, message: '请输入账号名称' }]}>
                <Input allowClear></Input>
              </Item>
            </Col>
            {accountType == 'Outer' && (
              <Col span={12}>
                <Item
                  name="supplierName"
                  required
                  label="供应商名称"
                  rules={[{ required: true, message: '请选择供应商名称' }]}
                >
                  <Select optionFilterProp="children" showSearch allowClear labelInValue>
                    {supplierName?.map((item, index) => {
                      return (
                        <Select.Option key={index} value={item.id}>
                          {item.supplierName}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Item>
              </Col>
            )}
            <Col span={12}>
              <Item
                name="userName"
                required
                label="姓名/联系人"
                rules={[{ required: true, message: '请输入姓名/联系人' }]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="email"
                required
                label="电子邮箱"
                rules={[
                  {
                    pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                    message: '邮箱格式不正确',
                  },
                ]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name="phone"
                required
                label="联系电话"
                rules={[
                  {
                    pattern: /^1[3456789]\d{9}$/,
                    message: '请输入正确的手机号码',
                  },
                ]}
              >
                <Input allowClear></Input>
              </Item>
            </Col>
            <Col span={12}>
              <Item name="userState" required label="账号状态" rules={[{ required: true, message: '请选择账号状态' }]}>
                <Select allowClear>
                  <Option value={1}>已启用</Option>
                  <Option value={0}>已禁用</Option>
                </Select>
              </Item>
            </Col>
            {accountType == 'Inner' && (
              <Col span={24}>
                <Item
                  name="roleIds"
                  required
                  label="所属角色"
                  rules={[{ required: true, message: '请选择所属角色' }]}
                  labelCol={{ span: 3 }}
                  wrapperCol={{ span: 21 }}
                >
                  <Checkbox.Group options={roleCheckBox} />
                </Item>
              </Col>
            )}
          </Row>
        </Form>
      </ModalDiv>
    </>
  );
};
