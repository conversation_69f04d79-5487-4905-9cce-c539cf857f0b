import React, { useEffect } from 'react';
import {
  Form,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  Space,
  Modal,
  Checkbox,
  Tree,
} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  ContainerDiv,
  WrapperDiv,
  SearchDiv,
  TableWrapDiv,
  TableTopDiv,
  TableTitleDiv,
  TaleTitleIconDiv,
  TableTitleSpanDiv,
  TableBtnDiv,
  BtnBlaWrap,
  BtnOrgWrap,
  OperDiv,
} from '@/assets/style/list';
import { HeaderDiv } from '@/components/Layout/style';
import moment from 'moment';
import Tooltip from 'antd/es/tooltip';
import useService from './useService';
export default (props: any) => {
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '80px' },
    // wrapperCol: { flex: 'auto' },
  };
  const { form, pageData, pageParams, formSearch, onPageChange } =
    useService(props);

  const columns: any = [
    {
      title: '客户代码',
      dataIndex: 'customerCode',
      key: 'customerCode',
      align: 'center',
      width: 260,
    },
    {
      title: '操作人',
      dataIndex: 'createrName',
      key: 'createrName',
      align: 'center',
      width: 260,
    },
    {
      title: '操作时间',
      dataIndex: 'created',
      key: 'created',
      align: 'center',
      width: 260,
      render: (text, record) => {
        return text && moment(text).isValid()
          ? moment(text).format('YYYY-MM-DD HH:mm:ss')
          : '';
      },
    },
    {
      title: '操作详情',
      dataIndex: 'logDetail',
      key: 'logDetail',
      align: 'center',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
  ];

  return (
    <ContainerDiv>
      <HeaderDiv>日志列表</HeaderDiv>
      <WrapperDiv>
        <SearchDiv>
          <Form form={form} {...layout}>
            <Row gutter={20}>
              <Col span={22}>
                <Row gutter={20}>
                  <Col span={8}>
                    <Form.Item name="startTime" label="开始时间">
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="endTime" label="结束时间">
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="customerCode" label="客户代码：">
                      <Input placeholder="客户代码" maxLength={50} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="createrName" label="操作人：">
                      <Input placeholder="操作人" maxLength={50} />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col
                span={2}
                style={{
                  display: 'flex',
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                }}
              >
                <Button
                  type="primary"
                  htmlType="submit"
                  style={{ marginBottom: 24 }}
                  onClick={formSearch}
                >
                  搜索
                </Button>
              </Col>
            </Row>
          </Form>
        </SearchDiv>
        <TableWrapDiv>
          <TableTopDiv>
            <TableTitleDiv style={{ float: 'left' }}>
              <span style={{ verticalAlign: 'middle', paddingRight: 12 }}>
                日志列表
              </span>
              <TableTitleSpanDiv></TableTitleSpanDiv>
            </TableTitleDiv>
          </TableTopDiv>
          <div>
            <Table
              size="middle"
              dataSource={pageData.data.map((x, i) => ({
                ...x,
                key: i,
              }))}
              pagination={{
                current: pageParams.pageIndex,
                total: pageData.totalCount,
              }}
              columns={columns}
              onChange={(pagination, filters, sorter) => {
                onPageChange(pagination);
              }}
              rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            />
          </div>
        </TableWrapDiv>
      </WrapperDiv>
    </ContainerDiv>
  );
};
