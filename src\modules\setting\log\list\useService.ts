import { useRef, useState, useEffect } from 'react';
import { Form } from 'antd';
import { history } from 'umi';
import { message } from 'antd';
import moment from 'moment';

export default (props: any) => {
  const [form] = Form.useForm();
  const [pageData, setPageData] = useState({
    data: Array<any>(),
    totalCount: 0,
  });
  //分页请求参数
  const [pageParams, setPageParams] = useState({
    createrName: '',
    customerCode: '',
    startTime: '',
    endTime: '',
    pageIndex: 1,
    pageSize: 10,
  });

  //搜索
  const formSearch = () => {
    var params = form.getFieldsValue();
    params.startTime = params.startTime?.format('YYYY-MM-DD');
    params.endTime = params.endTime?.format('YYYY-MM-DD');
    params.pageSize = pageParams.pageSize;
    params.pageIndex = 1;
    setPageParams(params);
  };

  // 分页请求
  const pageSearch = () => {};
  const onPageChange = (pagination) => {
    let params = JSON.parse(JSON.stringify(pageParams));
    params.pageSize = pagination.pageSize;
    params.pageIndex = pagination.current;
    setPageParams(params);
  };

  useEffect(() => {
    pageSearch();
  }, [pageParams]);

  useEffect(() => {}, []);

  return {
    form,
    pageData,
    pageParams,
    formSearch,
    onPageChange,
  };
};
