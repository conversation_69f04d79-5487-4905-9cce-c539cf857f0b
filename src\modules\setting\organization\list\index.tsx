import React, { useEffect } from 'react';
import {
  Form,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  Space,
  Modal,
  Checkbox,
  Tree,
  Popover,
} from 'antd';
import { QuestionCircleOutlined, PlusCircleOutlined, PlusSquareOutlined } from '@ant-design/icons';
import {
  ContainerDiv,
  WrapperDiv,
  SearchDiv,
  TableWrapDiv,
  TableTopDiv,
  TableTitleDiv,
  TaleTitleIconDiv,
  TableTitleSpanDiv,
  TableBtnDiv,
  BtnBlaWrap,
  BtnOrgWrap,
  BtnGreenWrap,
  OperDiv,
} from '@/assets/style/list';
import { HeaderDiv } from '@/components/Layout/style';
import { RightPopDiv, RightPopItemDiv } from '@/assets/style/form';
import moment, { relativeTimeRounding } from 'moment';
import Tooltip from 'antd/es/tooltip';
import useService from './useService';
export default (props: any) => {
  const layout: any = {
    requiredMark: true,
    // labelCol: { flex: '80px' },
    wrapperCol: { flex: 'auto' },
  };
  const {
    serachForm,
    form,
    nameForm,
    pageData,
    pageParams,
    isModalVisible,
    modalTitle,
    treeDatas,
    defaultKey,
    clientX,
    clientY,
    opacity,
    isAddVisible,
    parentName,
    isChangeVisible,
    authBtns,
    isRoleModalVisible,
    roleData,
    rolePageParams,
    selectedRowKeys,
    formSearch,
    onPageChange,
    addPerson,
    editPerson,
    handleCancel,
    handleAction,
    onSelect,
    getByParentId,
    onRightClick,
    setOpacity,
    handleAddOk,
    handleAddCancel,
    addChild,
    changeName,
    handleChangeOk,
    handleChangeCancel,
    deleteNode,
    editRole,
    handleRoleOk,
    handleRoleCancel,
    onRolePageChange,
    onRowSelectChange,
  } = useService(props);

  const columns: any = [
    {
      title: '姓名',
      dataIndex: 'userName',
      key: 'userName',
      align: 'center',
    },
    {
      title: 'GID',
      dataIndex: 'gid',
      key: 'gid',
      align: 'center',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'deleFlag',
      key: 'deleFlag',
      align: 'center',
      render: (text, record) => <span>{text == '0' ? '启用' : '禁用'}</span>,
    },
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      width: 250,
      // fixed: 'right',
      render: (text, record) => {
        const _btnJsx = {
          UserEdit: (
            <Button
              key="edit"
              type="link"
              onClick={() => {
                editPerson(record);
              }}
            >
              修改
            </Button>
          ),
          UserDel:
            record.deleFlag == '0' ? (
              <Popconfirm
                key="del"
                title={<span>确定禁用该人员？</span>}
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleAction(record)}
              >
                <Button type="link" danger>
                  禁用
                </Button>
              </Popconfirm>
            ) : (
              ''
            ),
          UserRec:
            record.deleFlag == '1' ? (
              <Popconfirm
                key="rec"
                title={<span>确定启用该人员？</span>}
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleAction(record)}
              >
                <BtnGreenWrap>
                  <Button type="link">启用</Button>
                </BtnGreenWrap>
              </Popconfirm>
            ) : (
              ''
            ),
          UserAccess: (
            <Button
              key="access"
              type="link"
              onClick={() => {
                editRole(record);
              }}
            >
              角色权限
            </Button>
          ),
        };
        return <OperDiv>{authBtns.map((item) => _btnJsx[item])}</OperDiv>;
      },
    },
  ];

  const renderTreeNode = (parentId) => {
    let tmp = getByParentId(parentId);
    if (tmp.length > 0) {
      return tmp.map((item) => {
        return (
          <Tree.TreeNode title={item.title} key={item.key}>
            {renderTreeNode(item.key)}
          </Tree.TreeNode>
        );
      });
    }
  };

  const roleColumns: any = [
    {
      title: '角色',
      dataIndex: 'roleName',
      key: 'roleName',
      align: 'center',
    },
    {
      title: '是否管理',
      dataIndex: 'adminFlag',
      key: 'adminFlag',
      align: 'center',
      render: (text) => (text == '1' ? '是' : '否'),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    hideSelectAll: true,
    onSelect: onRowSelectChange,
  };

  return (
    <ContainerDiv>
      <HeaderDiv>组织架构管理</HeaderDiv>
      <WrapperDiv>
        <Modal
          title={modalTitle}
          visible={isModalVisible}
          width={550}
          onCancel={handleCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Form form={form} labelCol={{ span: 8 }}>
            <Row>
              <Col span={19}>
                <Form.Item name="userName" label="用户姓名" rules={[{ required: true }]}>
                  <Input maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={19}>
                <Form.Item
                  name="email"
                  label="用户邮箱"
                  rules={[
                    { required: true },
                    {
                      pattern: /^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*\.[a-z]{2,}$/,
                      message: '请输入合理的邮箱',
                    },
                  ]}
                >
                  <Input maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={19}>
                <Form.Item name="gid" label="GID" rules={[{ required: true }]}>
                  <Input maxLength={50} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
        <Modal
          title="新建子节点"
          visible={isAddVisible}
          width={550}
          onOk={handleAddOk}
          onCancel={handleAddCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Form form={form} labelCol={{ span: 8 }}>
            <Row>
              <Col span={19}>
                <Form.Item label="所属父节点">
                  <span>{parentName}</span>
                </Form.Item>
              </Col>
              <Col span={19}>
                <Form.Item name="tagType" label="子节点名称">
                  <Input maxLength={50} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
        <Modal
          title="重命名"
          visible={isChangeVisible}
          width={550}
          onOk={handleChangeOk}
          onCancel={handleChangeCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Form form={nameForm} labelCol={{ span: 8 }}>
            <Row>
              <Col span={19}>
                <Form.Item name="oldName" label="原节点名">
                  <Input disabled maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={19}>
                <Form.Item name="newName" label="新节点名">
                  <Input maxLength={50} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
        <Modal
          title="角色权限"
          visible={isRoleModalVisible}
          width={800}
          onOk={handleRoleOk}
          onCancel={handleRoleCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Row>
            <Col span={24}>
              <Table
                size="middle"
                dataSource={roleData.data}
                pagination={{
                  current: rolePageParams.pageIndex,
                  total: roleData.totalCount,
                }}
                rowKey="id"
                columns={roleColumns}
                rowSelection={rowSelection}
                onChange={(pagination, filters, sorter) => {
                  onRolePageChange(pagination);
                }}
              />
            </Col>
          </Row>
        </Modal>
        <SearchDiv>
          <Form form={serachForm} {...layout}>
            <Row gutter={30}>
              <Col span={7}>
                <Form.Item name="userName" label="姓名">
                  <Input placeholder="请输入姓名" maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={7}>
                <Form.Item name="gid" label="GID：">
                  <Input placeholder="请输入GID" maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={7}>
                <Form.Item name="email" label="邮箱：">
                  <Input placeholder="请输入邮箱" maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={3} style={{ textAlign: 'right' }}>
                <Button type="primary" htmlType="submit" onClick={formSearch}>
                  搜索
                </Button>
              </Col>
            </Row>
          </Form>
        </SearchDiv>
        <TableWrapDiv>
          <TableTopDiv>
            <TableTitleDiv style={{ float: 'left' }}>
              <span style={{ verticalAlign: 'middle', paddingRight: 12 }}>组织架构管理列表</span>
              <TableTitleSpanDiv></TableTitleSpanDiv>
            </TableTitleDiv>
            <TableBtnDiv style={{ float: 'right' }}>
              <Space size={10}>
                {authBtns && authBtns.includes('UserAdd') ? (
                  <Button type="primary" icon={<PlusSquareOutlined />} className="iconBtns" onClick={addPerson}>
                    新增人员
                  </Button>
                ) : (
                  ''
                )}
              </Space>
            </TableBtnDiv>
          </TableTopDiv>
          <div>
            <Table
              size="middle"
              dataSource={pageData.data}
              pagination={{
                current: pageParams.pageIndex,
                total: pageData.totalCount,
              }}
              rowKey="id"
              columns={columns}
              onChange={(pagination, filters, sorter) => {
                onPageChange(pagination);
              }}
              rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            />
          </div>
        </TableWrapDiv>
      </WrapperDiv>
    </ContainerDiv>
  );
};
