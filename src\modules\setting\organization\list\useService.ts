import { useRef, useState, useEffect } from 'react';
import { Form, Modal } from 'antd';
import { history } from 'umi';
import { message } from 'antd';
import moment from 'moment';

export default (props: any) => {
  const [serachForm] = Form.useForm();
  const [form] = Form.useForm();
  const [nameForm] = Form.useForm();
  const [pageData, setPageData] = useState({
    data: Array<any>(),
    totalCount: 0,
  });
  //分页请求参数
  const [pageParams, setPageParams] = useState({
    userName: '',
    gid: '',
    email: '',
    pageIndex: 1,
    pageSize: 10,
  });

  const [roleData, setRoleData] = useState({
    data: Array<any>(),
    totalCount: 0,
  });
  //分页请求参数
  const [rolePageParams, setRolePageParams] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [id, setId] = useState('');
  const [treeDatas, setTreeDatas] = useState(Array<any>());
  const [defaultKey, setDefaultKey] = useState(Array<any>());
  const [clientX, setClientX] = useState(0);
  const [clientY, setClientY] = useState(0);
  const [opacity, setOpacity] = useState(0);
  const [isAddVisible, setIsAddVisible] = useState(false);
  const [parentName, setParentName] = useState('');
  const [isChangeVisible, setIsChangeVisible] = useState(false);
  const [authBtns, setAuthBtns] = useState(Array<any>([]));
  const [isRoleModalVisible, setIsRoleModalVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState(Array<any>());

  const tree = [
    { key: '1', title: '西门子医疗', parentId: '-1' },
    { key: '1-1', title: 'East', parentId: '1' },
    { key: '1-1-1', title: '上海', parentId: '1-1' },
    { key: '1-1-1-1', title: 'Team1', parentId: '1-1-1' },
    { key: '1-1-1-2', title: 'Team2', parentId: '1-1-1' },
    { key: '1-1-2', title: '江苏', parentId: '1-1' },
    { key: '1-1-2-1', title: 'Team1', parentId: '1-1-2' },
    { key: '1-1-3', title: '浙江', parentId: '1-1' },
    { key: '1-1-3-1', title: 'Team1', parentId: '1-1-3' },
    { key: '1-1-3-2', title: 'Team2', parentId: '1-1-3' },
    { key: '1-1-3-3', title: 'Team1', parentId: '1-1-3' },
    { key: '1-1-4', title: '安徽', parentId: '1-1' },
    { key: '1-1-4-1', title: 'Team1', parentId: '1-1-4' },
    { key: '1-1-4-2', title: 'Team2', parentId: '1-1-4' },
    { key: '1-2', title: 'West', parentId: '1' },
    { key: '1-2-1', title: '上海', parentId: '1-2' },
    { key: '1-2-2', title: '江苏', parentId: '1-2' },
    { key: '1-2-3', title: '浙江', parentId: '1-2' },
    { key: '1-2-4', title: '安徽', parentId: '1-2' },
    { key: '1-3', title: 'South', parentId: '1' },
    { key: '1-3-1', title: '上海', parentId: '1-3' },
    { key: '1-3-2', title: '江苏', parentId: '1-3' },
    { key: '1-3-3', title: '浙江', parentId: '1-3' },
    { key: '1-3-4', title: '安徽', parentId: '1-3' },
    { key: '1-4', title: 'North', parentId: '1' },
    { key: '1-4-1', title: '上海', parentId: '1-4' },
    { key: '1-4-2', title: '江苏', parentId: '1-4' },
    { key: '1-4-3', title: '浙江', parentId: '1-4' },
    { key: '1-4-4', title: '安徽', parentId: '1-4' },
  ];

  //搜索
  const formSearch = () => {
    var params = serachForm.getFieldsValue();
    params.pageSize = pageParams.pageSize;
    params.pageIndex = 1;
    setPageParams(params);
  };
  // 分页请求
  const pageSearch = () => {};

  const onPageChange = (pagination) => {
    let params = JSON.parse(JSON.stringify(pageParams));
    params.pageSize = pagination.pageSize;
    params.pageIndex = pagination.current;
    setPageParams(params);
  };

  const addPerson = () => {
    setModalTitle('新增人员');
    setId('');
    setIsModalVisible(true);
  };

  const editPerson = (row) => {
    setModalTitle('修改人员');
    setId(row.id);
    setIsModalVisible(true);
    form.setFieldsValue(row);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setId('');
    form.resetFields();
  };
  // 启用/禁用
  const handleAction = (record) => {
    const { id, deleFlag } = record;
    if (deleFlag == '0') {
    } else {
    }
  };
  // 修改用户角色弹窗开
  const editRole = (record) => {
    setId(record.id);
    setIsRoleModalVisible(true);
    roleSearch();
  };

  const handleRoleOk = () => {
    let data = {
      userId: id,
      roleIdList: [...selectedRowKeys],
    };
  };

  const handleRoleCancel = () => {
    setIsRoleModalVisible(false);
    setId('');
  };

  const roleSearch = () => {
    let params = {
      pageSize: rolePageParams.pageSize,
      pageIndex: 1,
    };
    setRolePageParams(params);
    rolePageSearch(params);
  };
  const rolePageSearch = (params) => {};

  const onRolePageChange = (pagination) => {
    let params = JSON.parse(JSON.stringify(rolePageParams));
    params.pageSize = pagination.pageSize;
    params.pageIndex = pagination.current;
    setRolePageParams(params);
    rolePageSearch(params);
  };

  const onRowSelectChange = (record, selected, selectedRows, nativeEvent) => {
    let keys = selectedRowKeys.slice();
    keys = selected ? keys.concat([record.id]) : keys.filter((i) => i != record.id);
    setSelectedRowKeys(keys);
  };

  const onSelect = (selectedKeys: React.Key[], info: any) => {
    const { node, selected } = info;
    if (selected) {
      setPageData({
        data: [],
        totalCount: 3,
      });
    } else {
      setPageData({ data: [], totalCount: 0 });
    }
    form.setFieldsValue({
      organization: `西门子医疗/East/上海/${node.title}`,
    });
  };

  const onRightClick = (e) => {
    const { event, node } = e;
    let x = event.clientX - 208;
    let y = event.clientY - 60;
    setClientX(x);
    setClientY(y);
    setOpacity(1);
    nameForm.setFieldsValue({
      oldName: node.title,
    });
  };

  // 根据父级找到所有子级节点
  const getByParentId = (parentId) => {
    return tree.filter((item) => {
      return item.parentId === parentId;
    });
  };

  const handleAddOk = () => {
    setIsAddVisible(false);
  };

  const handleAddCancel = () => {
    setIsAddVisible(false);
  };

  const addChild = () => {
    setIsAddVisible(true);
    setParentName('上海');
  };

  const changeName = () => {
    setIsChangeVisible(true);
  };

  const handleChangeOk = () => {
    setIsChangeVisible(false);
  };

  const handleChangeCancel = () => {
    setIsChangeVisible(false);
  };

  const deleteNode = () => {
    Modal.confirm({
      title: 'Tips',
      content: '是否删除该节点？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {},
    });
  };

  useEffect(() => {
    pageSearch();
  }, [pageParams]);

  useEffect(() => {
    let _authBtns = JSON.parse(sessionStorage.getItem('authBtns')) || [];
    setAuthBtns(_authBtns);
  }, []);

  return {
    serachForm,
    form,
    nameForm,
    pageData,
    pageParams,
    isModalVisible,
    modalTitle,
    treeDatas,
    defaultKey,
    clientX,
    clientY,
    opacity,
    isAddVisible,
    parentName,
    isChangeVisible,
    authBtns,
    isRoleModalVisible,
    roleData,
    rolePageParams,
    selectedRowKeys,
    formSearch,
    onPageChange,
    addPerson,
    editPerson,
    handleCancel,
    handleAction,
    onSelect,
    getByParentId,
    onRightClick,
    setOpacity,
    handleAddOk,
    handleAddCancel,
    addChild,
    changeName,
    handleChangeOk,
    handleChangeCancel,
    deleteNode,
    editRole,
    handleRoleOk,
    handleRoleCancel,
    onRolePageChange,
    onRowSelectChange,
  };
};
