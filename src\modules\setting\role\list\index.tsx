import React, { useEffect } from 'react';
import {
  Form,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  Space,
  Modal,
  Checkbox,
  Tree,
  Switch,
} from 'antd';
import {
  ContainerDiv,
  WrapperDiv,
  SearchDiv,
  TableWrapDiv,
  TableTopDiv,
  TableTitleDiv,
  TaleTitleIconDiv,
  TableBtnDiv,
  TableTitleSpanDiv,
  BtnBlaWrap,
  BtnOrgWrap,
  OperDiv,
} from '@/assets/style/list';
import { HeaderDiv } from '@/components/Layout/style';
import {
  PanelWrapperDiv,
  PanelHeaderDiv,
  SquareGrayWrapper,
  SquareWrapper,
  SquareTag,
  SquareSelectedTag,
} from '@/assets/style/form';
import moment from 'moment';
import Tooltip from 'antd/es/tooltip';
import useService from './useService';
import { DraggableAreasGroup } from 'react-draggable-tags';
import { CloseCircleOutlined, PlusSquareOutlined } from '@ant-design/icons';
import './index.less';
export default (props: any) => {
  const { Search } = Input;
  const layout: any = {
    requiredMark: true,
    // labelCol: { flex: '80px' },
    wrapperCol: { flex: 'auto' },
  };
  const {
    serachForm,
    form,
    pageData,
    pageParams,
    personPageParams,
    isModalVisible,
    modalTitle,
    isFunModalVisible,
    checkedFunKeys,
    isTagModalVisible,
    checkedTagKeys,
    userData,
    userForm,
    isPersonModalVisible,
    selectedRowKeys,
    labelOptions,
    selectedLabels,
    isAdminFlag,
    funData,
    tagSearchForm,
    authBtns,
    formSearch,
    onPageChange,
    onSwitchChange,
    addRole,
    editRole,
    handleOk,
    handleCancel,
    handleFunOk,
    handleFunCancel,
    onFunCheck,
    onFunSelect,
    funAuth,
    onTagCheck,
    onTagSelect,
    handleTagOk,
    handleTagCancel,
    tagAuth,
    handlePersonOk,
    handlePersonCancel,
    personAuth,
    personSearch,
    onPersonPageChange,
    onRowSelectChange,
    handleTags,
    handleClickDelete,
    onSearch,
  } = useService(props);

  const columns: any = [
    {
      title: '角色',
      dataIndex: 'roleName',
      key: 'roleName',
      align: 'center',
    },
    {
      title: '是否管理',
      dataIndex: 'adminFlag',
      key: 'adminFlag',
      align: 'center',
      render: (text) => (text == '1' ? '是' : '否'),
    },
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      width: 370,
      // fixed: 'right',
      render: (text, record) => {
        const _btnJsx = {
          RoleEdit: (
            <Button
              key="edit"
              type="link"
              onClick={() => {
                editRole(record);
              }}
            >
              修改
            </Button>
          ),
          RoleMapUser: (
            <Button
              key="mapUser"
              type="link"
              onClick={() => {
                personAuth(record);
              }}
            >
              人员维护
            </Button>
          ),
          RoleMapAuth:
            record.adminFlag == '1' ? (
              <Tooltip title="管理员已默认授权所有" key="mapAuth">
                <Button type="link" disabled>
                  功能授权
                </Button>
              </Tooltip>
            ) : (
              <Button
                key="mapAuth"
                type="link"
                onClick={() => {
                  funAuth(record);
                }}
              >
                功能授权
              </Button>
            ),
          RoleMapLabel:
            record.adminFlag == '1' ? (
              <Tooltip title="管理员已默认授权所有" key="labelTool">
                <Button type="link" disabled>
                  标签授权
                </Button>
              </Tooltip>
            ) : (
              <Button
                key="mapLabel"
                type="link"
                onClick={() => {
                  tagAuth(record);
                }}
              >
                标签授权
              </Button>
            ),
        };
        return <OperDiv>{authBtns.map((item) => _btnJsx[item])}</OperDiv>;
      },
    },
  ];

  const personColumns: any = [
    {
      title: '姓名',
      dataIndex: 'userName',
      key: 'userName',
      align: 'center',
    },
    {
      title: 'GID',
      dataIndex: 'gid',
      key: 'gid',
      align: 'center',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      align: 'center',
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    hideSelectAll: true,
    getCheckboxProps: (record) => {
      return {
        disabled: record.deleFlag == '1',
      };
    },
    onSelect: onRowSelectChange,
  };

  const group = new DraggableAreasGroup();
  const DraggableAreaA = group.addArea('areaA');
  const DraggableAreaB = group.addArea('areaB');

  return (
    <ContainerDiv>
      <HeaderDiv>角色管理</HeaderDiv>
      <WrapperDiv>
        <Modal
          title={modalTitle}
          visible={isModalVisible}
          width={550}
          onOk={handleOk}
          onCancel={handleCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Form form={form} labelCol={{ span: 8 }}>
            <Row>
              <Col span={19}>
                <Form.Item
                  name="roleName"
                  label="角色名"
                  rules={[{ required: true }]}
                >
                  <Input maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={19}>
                <Form.Item name="adminFlag" label="是否管理员">
                  <Switch checked={isAdminFlag} onChange={onSwitchChange} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
        <Modal
          title="功能授权"
          visible={isFunModalVisible}
          width={550}
          onOk={handleFunOk}
          onCancel={handleFunCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Row>
            <Col span={22}>
              <Tree
                checkable
                defaultExpandAll
                autoExpandParent={true}
                onCheck={onFunCheck}
                checkedKeys={checkedFunKeys}
                // onSelect={onFunSelect}
                treeData={funData}
              />
            </Col>
          </Row>
        </Modal>
        <Modal
          title="标签授权"
          visible={isTagModalVisible}
          width={900}
          onOk={handleTagOk}
          onCancel={handleTagCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
          className="tagModal"
        >
          <Row gutter={10} style={{ height: '100%' }}>
            <Col span={12}>
              <SquareGrayWrapper>
                <Row>
                  <Col span={24}>
                    <Form form={tagSearchForm}>
                      <Form.Item name="search" className="tagForm">
                        <Search
                          onSearch={(value) => {
                            onSearch(value);
                          }}
                          style={{ width: 200 }}
                        />
                      </Form.Item>
                    </Form>
                  </Col>
                </Row>
                <DraggableAreaA
                  tags={labelOptions}
                  render={({ tag }) => (
                    <SquareTag key={tag.id}>{tag.labelName}</SquareTag>
                  )}
                  onChange={(leftTags, { fromArea, toArea }) => {
                    handleTags(leftTags, { fromArea, toArea });
                  }}
                />
              </SquareGrayWrapper>
            </Col>
            <Col span={12} className="tagDragger">
              <SquareWrapper>
                <DraggableAreaB
                  tags={selectedLabels}
                  render={({ tag }) => (
                    <div className="selectedSquare">
                      <SquareSelectedTag>{tag.labelName}</SquareSelectedTag>
                      <CloseCircleOutlined
                        className="closeIcon"
                        onClick={() => handleClickDelete(tag)}
                      />
                    </div>
                  )}
                  onChange={(tag, { fromArea, toArea }) => {
                    // console.log('right', tag);
                  }}
                />
              </SquareWrapper>
            </Col>
          </Row>
        </Modal>
        <Modal
          title="人员维护"
          visible={isPersonModalVisible}
          width={800}
          onOk={handlePersonOk}
          onCancel={handlePersonCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Row>
            <Col span={24}>
              <Form form={userForm}>
                <Row gutter={10}>
                  <Col span={6}>
                    <Form.Item name="userName">
                      <Input placeholder="请输入姓名" maxLength={50} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      onClick={personSearch}
                    >
                      搜索
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Col>
            <Col span={24}>
              <Table
                size="middle"
                dataSource={userData.data}
                pagination={{
                  current: personPageParams.pageIndex,
                  total: userData.totalCount,
                }}
                rowKey="id"
                columns={personColumns}
                rowSelection={rowSelection}
                onChange={(pagination, filters, sorter) => {
                  onPersonPageChange(pagination);
                }}
              />
            </Col>
          </Row>
        </Modal>
        <SearchDiv>
          <Form form={serachForm} {...layout}>
            <Row gutter={30}>
              <Col span={8}>
                <Form.Item name="roleName" label="角色名称：">
                  <Input placeholder="角色名称" maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="adminFlag" label="是否管理员">
                  <Select allowClear placeholder="是否管理员">
                    <Select.Option value={1}>是</Select.Option>
                    <Select.Option value={0}>否</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8} style={{ textAlign: 'right' }}>
                <Button type="primary" htmlType="submit" onClick={formSearch}>
                  搜索
                </Button>
              </Col>
            </Row>
          </Form>
        </SearchDiv>
        <TableWrapDiv>
          <TableTopDiv>
            <TableTitleDiv style={{ float: 'left' }}>
              <span style={{ verticalAlign: 'middle', paddingRight: 12 }}>
                角色管理列表
              </span>
              <TableTitleSpanDiv></TableTitleSpanDiv>
            </TableTitleDiv>
            <TableBtnDiv style={{ float: 'right' }}>
              <Space>
                {authBtns && authBtns.includes('RoleAdd') ? (
                  <Button
                    type="primary"
                    icon={<PlusSquareOutlined />}
                    className="iconBtns"
                    onClick={addRole}
                  >
                    新增角色
                  </Button>
                ) : (
                  ''
                )}
              </Space>
            </TableBtnDiv>
          </TableTopDiv>
          <div>
            <Table
              size="middle"
              dataSource={pageData.data}
              pagination={{
                current: pageParams.pageIndex,
                total: pageData.totalCount,
              }}
              rowKey="id"
              columns={columns}
              onChange={(pagination, filters, sorter) => {
                onPageChange(pagination);
              }}
              rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            />
          </div>
        </TableWrapDiv>
      </WrapperDiv>
    </ContainerDiv>
  );
};
