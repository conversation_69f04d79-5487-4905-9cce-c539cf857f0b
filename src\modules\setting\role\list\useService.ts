import { useRef, useState, useEffect } from 'react';
import { Form, Modal } from 'antd';
import { history } from 'umi';
import { message } from 'antd';
import moment from 'moment';

export default (props: any) => {
  const [serachForm] = Form.useForm();
  const [form] = Form.useForm();
  const [userForm] = Form.useForm();
  const [tagSearchForm] = Form.useForm();
  const [pageData, setPageData] = useState({
    data: Array<any>(),
    totalCount: 0,
  });
  const [userData, setUserData] = useState({
    data: Array<any>(),
    totalCount: 0,
  });
  //分页请求参数
  const [pageParams, setPageParams] = useState({
    roleName: '',
    adminFlag: '',
    pageIndex: 1,
    pageSize: 10,
  });
  const [personPageParams, setPersonPageParams] = useState({
    userName: '',
    pageIndex: 1,
    pageSize: 10,
  });

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [isFunModalVisible, setIsFunModalVisible] = useState(false);
  const [checkedFunKeys, setCheckedFunKeys] = useState<React.Key[]>();
  const [checkedParentFunKeys, setCheckedParentFunKeys] = useState<React.Key[]>();
  const [isTagModalVisible, setIsTagModalVisible] = useState(false);
  const [checkedTagKeys, setCheckedTagKeys] = useState<React.Key[]>();
  const [isPersonModalVisible, setIsPersonModalVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState(Array<any>());
  const [roleId, setRoleId] = useState('');
  const [allLabels, setAllLabels] = useState(Array<any>());
  const [labelOptions, setLabelOptions] = useState(Array<any>());
  const [selectedLabels, setSelectedLabels] = useState(Array<any>());
  const [isAdminFlag, setIsAdminFlag] = useState(false);
  const [funData, setFunData] = useState(Array<any>());
  const [authBtns, setAuthBtns] = useState(Array<any>([]));

  //搜索
  const formSearch = () => {
    var params = serachForm.getFieldsValue();
    params.pageSize = pageParams.pageSize;
    params.pageIndex = 1;
    setPageParams(params);
  };
  const onPageChange = (pagination) => {
    let params = JSON.parse(JSON.stringify(pageParams));
    params.pageSize = pagination.pageSize;
    params.pageIndex = pagination.current;
    setPageParams(params);
  };
  // 分页请求
  const pageSearch = () => {};

  const onSwitchChange = (value) => {
    if (value)
      Modal.confirm({
        title: 'Tips',
        content: '管理员角色默认拥有所有标签、功能授权，是否确认变更？',
        onOk: () => {
          setIsAdminFlag(value);
        },
      });
    else {
      setIsAdminFlag(value);
    }
  };
  const addRole = () => {
    setModalTitle('新增角色');
    setIsModalVisible(true);
  };
  const editRole = (row) => {
    setRoleId(row.id);
    setModalTitle('修改角色');
    let data = {
      ...row,
      adminFlag: row.adminFlag == 1 ? true : false,
    };
    setIsAdminFlag(row.adminFlag == 1 ? true : false);
    form.setFieldsValue(data);
    setIsModalVisible(true);
  };
  // 新增/修改角色请求
  const handleOk = () => {
    form.validateFields().then(() => {
      if (roleId) {
      } else {
      }
    });
  };
  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setRoleId('');
  };
  const handleFunTreeData = (arr: any) => {
    return arr.map((item) => ({
      key: item.id,
      title: item.authName,
      children: handleFunTreeData(item.subTree),
    }));
  };
  const handleFunCheckedData = (data, arr) => {
    data.forEach((item) => {
      if (item.authFlag == '1') {
        if (item.subTree.length != 0) {
          handleFunCheckedData(item.subTree, arr);
        } else {
          arr.push(item.id);
        }
      }
    });
  };
  // 功能授权弹窗开
  const funAuth = (row) => {
    setRoleId(row.id);
    setIsFunModalVisible(true);
  };
  // 功能授权请求
  const handleFunOk = () => {
    let data = {
      roleId: roleId,
      authList: [...checkedParentFunKeys],
    };
  };
  const handleFunCancel = () => {
    setIsFunModalVisible(false);
    setCheckedFunKeys([]);
    setRoleId('');
  };
  const onFunCheck = (checkedKeysValue, e) => {
    const { halfCheckedKeys } = e;
    let checkedKeys = [...checkedKeysValue, ...halfCheckedKeys];
    setCheckedParentFunKeys(checkedKeys);
    setCheckedFunKeys(checkedKeysValue);
  };
  const onFunSelect = (selectedKeysValue: React.Key[], info: any) => {
    // console.log('onSelect', info);
  };
  const onTagCheck = (checkedKeysValue: React.Key[]) => {
    setCheckedTagKeys(checkedKeysValue);
  };
  const onTagSelect = (selectedKeysValue: React.Key[], info: any) => {
    // console.log('onSelect', info);
  };

  // 标签维护弹窗开
  const tagAuth = (row) => {
    setRoleId(row.id);
    setIsTagModalVisible(true);
    let data: any;
  };
  // 处理标签维护拖拽
  const handleData = (arr, arr1) => {
    let result = [];
    arr1.forEach((item) => {
      let isExist = false;
      arr.forEach((e) => {
        if (e.id == item.id) {
          isExist = true;
        }
      });
      if (!isExist) {
        result.push(item);
      }
    });
    return result;
  };
  const handleTags = (leftTags, { fromArea, toArea }) => {
    const arr = handleData(leftTags, labelOptions);
    if (fromArea.id == 'areaB') {
      setLabelOptions([...labelOptions]);
      setSelectedLabels([...selectedLabels]);
    } else {
      setLabelOptions([...leftTags]);
      setSelectedLabels([...selectedLabels, ...arr]);
    }
  };
  // 标签删除（返回可选列）
  const handleClickDelete = (tag) => {
    const rightTags = selectedLabels.filter((t) => tag.id !== t.id);
    const tags = [...labelOptions, tag];
    setLabelOptions([...tags]);
    setSelectedLabels([...rightTags]);
    let allLabel = [...allLabels];
    allLabel.push(tag);
    setAllLabels([...allLabel]);
  };
  // 标签搜索
  const onSearch = (value: string) => {
    let arr = [];
    if (value) {
      arr = labelOptions.filter((t) => t.labelName.includes(value));
    } else {
      arr = allLabels.filter((e) => selectedLabels.every((v) => e.id != v.id));
    }
    setLabelOptions([...arr]);
  };
  // 绑定标签请求
  const handleTagOk = () => {
    let labelList = [...selectedLabels].map((item) => item.id);
    let data = {
      roleId: roleId,
      labelList: labelList,
    };
  };
  const handleTagCancel = () => {
    setIsTagModalVisible(false);
    setRoleId('');
    tagSearchForm.resetFields();
  };
  // 人员维护弹窗开
  const personAuth = (row) => {
    setRoleId(row.id);
    setIsPersonModalVisible(true);
    personSearch();
  };
  // 人员维护绑定
  const handlePersonOk = () => {
    // if (selectedRowKeys.length == 0) {
    //   message.destroy();
    //   message.error('请选择人员');
    //   return;
    // }
    let data = {
      roleId: roleId,
      userList: [...selectedRowKeys],
    };
  };
  const handlePersonCancel = () => {
    setIsPersonModalVisible(false);
    userForm.resetFields();
    setRoleId('');
  };
  // 人员维护搜索
  const personSearch = () => {
    let params = userForm.getFieldsValue();
    params.pageSize = personPageParams.pageSize;
    params.pageIndex = 1;
    setPersonPageParams(params);
    personPageSearch(params);
  };
  const personPageSearch = (params) => {};
  const onPersonPageChange = (pagination) => {
    let params = JSON.parse(JSON.stringify(personPageParams));
    params.pageSize = pagination.pageSize;
    params.pageIndex = pagination.current;
    setPersonPageParams(params);
    personPageSearch(params);
  };
  const onRowSelectChange = (record, selected, selectedRows, nativeEvent) => {
    let keys = selectedRowKeys.slice();
    keys = selected ? keys.concat([record.id]) : keys.filter((i) => i != record.id);
    setSelectedRowKeys(keys);
  };

  useEffect(() => {
    pageSearch();
  }, [pageParams]);

  useEffect(() => {
    let _authBtns = JSON.parse(sessionStorage.getItem('authBtns')) || [];
    setAuthBtns(_authBtns);
  }, []);

  return {
    serachForm,
    form,
    pageData,
    pageParams,
    userData,
    personPageParams,
    userForm,
    isModalVisible,
    modalTitle,
    isFunModalVisible,
    checkedFunKeys,
    isTagModalVisible,
    checkedTagKeys,
    isPersonModalVisible,
    selectedRowKeys,
    labelOptions,
    selectedLabels,
    isAdminFlag,
    funData,
    tagSearchForm,
    authBtns,
    formSearch,
    onPageChange,
    onSwitchChange,
    addRole,
    editRole,
    handleOk,
    handleCancel,
    handleFunOk,
    handleFunCancel,
    onFunCheck,
    onFunSelect,
    funAuth,
    onTagCheck,
    onTagSelect,
    handleTagOk,
    handleTagCancel,
    tagAuth,
    handlePersonOk,
    handlePersonCancel,
    personAuth,
    personSearch,
    onPersonPageChange,
    onRowSelectChange,
    handleTags,
    handleClickDelete,
    onSearch,
  };
};
