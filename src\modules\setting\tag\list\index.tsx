import React, { useEffect } from 'react';
import {
  Form,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  Space,
  Modal,
  Checkbox,
  Tree,
  Tooltip,
} from 'antd';
import {
  QuestionCircleOutlined,
  PlusOutlined,
  PlusSquareOutlined,
} from '@ant-design/icons';
import {
  ContainerDiv,
  WrapperDiv,
  SearchDiv,
  TableWrapDiv,
  TableTopDiv,
  TableTitleDiv,
  TaleTitleIconDiv,
  TableTitleSpanDiv,
  TableBtnDiv,
  BtnBlaWrap,
  BtnOrgWrap,
  OperDiv,
} from '@/assets/style/list';
import { HeaderDiv } from '@/components/Layout/style';
import moment from 'moment';
import FormTable from '@/components/FormTable/formTable';
import useService from './useService';
export default (props: any) => {
  const { TextArea } = Input;

  const layout: any = {
    requiredMark: true,
    // labelCol: { flex: '80px' },
    wrapperCol: { flex: 'auto' },
  };
  const {
    serachForm,
    form,
    pageData,
    pageParams,
    isModalVisible,
    modalTitle,
    modalType,
    children,
    labelType,
    checked,
    optionList,
    selectedRowKeys,
    selectRows,
    authBtns,
    formSearch,
    onPageChange,
    addTag,
    editTag,
    handleOk,
    handleCancel,
    delAction,
    handleChange,
    onCheckChange,
    onSelectChange,
    setOptionList,
    onRowSelectChange,
    addOption,
  } = useService(props);

  enum LabelTypeEnum {
    'Selection' = '选项',
    'Text' = '文本',
    'Number' = '数字',
    'DateTime' = '日期',
  }

  const columns: any = [
    {
      title: '标签名称',
      dataIndex: 'labelName',
      key: 'labelName',
      align: 'center',
    },
    {
      title: '增值方式',
      dataIndex: 'labelType',
      key: 'labelType',
      align: 'center',
      render: (text, record) => <span>{LabelTypeEnum[text]}</span>,
    },
    {
      title: '标签说明',
      dataIndex: 'labelRemark',
      key: 'labelRemark',
      align: 'center',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '选项列表',
      dataIndex: 'labelOptions',
      key: 'labelOptions',
      align: 'center',
    },
    {
      title: '是否多选',
      dataIndex: 'optionsType',
      key: 'optionsType',
      align: 'center',
      render: (text, record) => <span>{text == '0' ? '否' : '是'}</span>,
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created',
      key: 'created',
      align: 'center',
      render: (text, record) =>
        text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '',
    },
    {
      title: '更新人',
      dataIndex: 'modifiedBy',
      key: 'modifiedBy',
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'modified',
      key: 'modified',
      align: 'center',
      render: (text, record) =>
        text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '',
    },
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      width: 180,
      fixed: 'right',
      render: (text, record) => {
        const _btnJsx = {
          LabelEdit: (
            <Button
              key="edit"
              type="link"
              onClick={() => {
                editTag(record);
              }}
            >
              修改
            </Button>
          ),
          LabelDel: (
            <Popconfirm
              key="delete"
              title="确定删除该标签？"
              icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() => delAction(record.id)}
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          ),
        };
        return <OperDiv>{authBtns.map((item) => _btnJsx[item])}</OperDiv>;
      },
    },
  ];

  const optionColumns: any = [
    {
      title: (
        <div>
          选项名称{' '}
          <Tooltip title="新增选项">
            <PlusOutlined
              style={{ float: 'right', cursor: 'pointer', marginTop: 4 }}
              onClick={addOption}
            />
          </Tooltip>
        </div>
      ),
      dataIndex: 'optionName',
      align: 'center',
      editable: true,
      renderT: (inputRef, save, record, dataIndex) => {
        return (
          <Form.Item style={{ margin: 0 }} name="optionName">
            <Input
              ref={inputRef}
              onPressEnter={save}
              onBlur={save}
              maxLength={50}
            />
          </Form.Item>
        );
      },
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectRows.map((x) => x.key),
    hideSelectAll: true,
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      onRowSelectChange(selectedRows);
    },
  };

  return (
    <ContainerDiv>
      <HeaderDiv>标签管理</HeaderDiv>
      <WrapperDiv>
        <Modal
          title={modalTitle}
          visible={isModalVisible}
          width={650}
          onOk={handleOk}
          onCancel={handleCancel}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
        >
          <Form
            labelCol={{ span: 5 }}
            form={form}
            initialValues={{ optionsType: 0 }}
          >
            <Row>
              <Col span={22}>
                <Form.Item
                  name="labelName"
                  label="标签名称"
                  rules={[{ required: true }]}
                >
                  <Input maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={22}>
                <Form.Item
                  name="labelType"
                  label="填值方式"
                  rules={[{ required: true }]}
                >
                  <Select
                    allowClear
                    placeholder="填值方式"
                    onChange={onSelectChange}
                    disabled={modalType == 'edit'}
                  >
                    <Select.Option value="Selection">选项</Select.Option>
                    <Select.Option value="Text">文本</Select.Option>
                    <Select.Option value="Number">数字</Select.Option>
                    <Select.Option value="DateTime">日期</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={22}>
                <Form.Item name="labelRemark" label="标签说明">
                  <TextArea rows={2} maxLength={200} />
                </Form.Item>
              </Col>
              {labelType == 'Selection' ? (
                <>
                  <Col span={22}>
                    <Form.Item
                      name="labelOptions"
                      label="选项列表"
                      rules={[
                        {
                          required:
                            labelType == 'Selection' && modalType == 'add',
                        },
                      ]}
                    >
                      {modalType == 'add' ? (
                        <Select
                          mode="tags"
                          onChange={handleChange}
                          style={{ width: '100%' }}
                        >
                          {children.map((item) => {
                            return (
                              <Select.Option key={item} value={item}>
                                {item}
                              </Select.Option>
                            );
                          })}
                        </Select>
                      ) : (
                        <FormTable
                          dataSource={optionList.map((e, i) => ({
                            ...e,
                            key: i,
                          }))}
                          setDataSource={setOptionList}
                          pagination={false}
                          columns={optionColumns}
                          rowSelection={rowSelection}
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={22}>
                    <Form.Item name="optionsType" label="允许多选">
                      <Checkbox
                        checked={checked}
                        onChange={onCheckChange}
                        disabled={modalType == 'edit'}
                      ></Checkbox>
                    </Form.Item>
                  </Col>
                </>
              ) : (
                ''
              )}
            </Row>
          </Form>
        </Modal>
        <SearchDiv>
          <Form form={serachForm} {...layout}>
            <Row gutter={30}>
              <Col span={8}>
                <Form.Item name="labelType" label="填值方式">
                  <Select allowClear placeholder="填值方式">
                    <Select.Option value="Selection">选项</Select.Option>
                    <Select.Option value="Text">文本</Select.Option>
                    <Select.Option value="Number">数字</Select.Option>
                    <Select.Option value="DateTime">日期</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="labelName" label="标签名称：">
                  <Input placeholder="标签名称" maxLength={50} />
                </Form.Item>
              </Col>
              <Col span={8} style={{ textAlign: 'right' }}>
                <Button type="primary" htmlType="submit" onClick={formSearch}>
                  搜索
                </Button>
              </Col>
            </Row>
          </Form>
        </SearchDiv>

        <TableWrapDiv>
          <TableTopDiv>
            <TableTitleDiv style={{ float: 'left' }}>
              <span style={{ verticalAlign: 'middle', paddingRight: 12 }}>
                标签管理列表
              </span>
              <TableTitleSpanDiv></TableTitleSpanDiv>
            </TableTitleDiv>
            <TableBtnDiv style={{ float: 'right' }}>
              <Space>
                {authBtns && authBtns.includes('LabelAdd') ? (
                  <Button
                    type="primary"
                    icon={<PlusSquareOutlined />}
                    className="iconBtns"
                    onClick={addTag}
                  >
                    新增标签
                  </Button>
                ) : (
                  ''
                )}
              </Space>
            </TableBtnDiv>
          </TableTopDiv>
          <div>
            <Table
              size="middle"
              dataSource={pageData.data}
              pagination={{
                current: pageParams.pageIndex,
                total: pageData.totalCount,
              }}
              rowKey="id"
              columns={columns}
              onChange={(pagination, filters, sorter) => {
                onPageChange(pagination);
              }}
              rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
              scroll={{ x: 1500 }}
            />
          </div>
        </TableWrapDiv>
      </WrapperDiv>
    </ContainerDiv>
  );
};
