import { useRef, useState, useEffect } from 'react';
import { Form } from 'antd';
import { history } from 'umi';
import { message } from 'antd';
import moment from 'moment';
export default (props: any) => {
  const [serachForm] = Form.useForm();
  const [form] = Form.useForm();
  const [pageData, setPageData] = useState({
    data: Array<any>(),
    totalCount: 0,
  });
  //分页请求参数
  const [pageParams, setPageParams] = useState({
    labelType: '',
    labelName: '',
    pageIndex: 1,
    pageSize: 10,
  });

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalType, setModalType] = useState('add');
  const [id, setId] = useState('');
  const [children, setChildren] = useState(Array<any>());
  const [labelType, setLableType] = useState('');
  const [checked, setChecked] = useState(false);
  const [optionList, setOptionList] = useState(Array<any>());
  const [selectedRowKeys, setSelectedRowKeys] = useState(Array<any>());
  const [selectRows, setSelectRows] = useState(Array<any>());
  const [authBtns, setAuthBtns] = useState(Array<any>([]));

  //搜索
  const formSearch = () => {
    var params = serachForm.getFieldsValue();
    params.pageSize = pageParams.pageSize;
    params.pageIndex = 1;
    setPageParams(params);
  };
  // 分页请求
  const pageSearch = () => {};
  const onPageChange = (pagination) => {
    let params = JSON.parse(JSON.stringify(pageParams));
    params.pageSize = pagination.pageSize;
    params.pageIndex = pagination.current;
    setPageParams(params);
  };
  // 新增动态标签
  const addTag = () => {
    setModalTitle('新增动态标签');
    setModalType('add');
    setIsModalVisible(true);
  };
  // 修改动态标签
  const editTag = (row) => {
    setModalTitle('修改动态标签');
    setModalType('edit');
    setId(row.id);
  };
  // 是否多选
  const onCheckChange = (e) => {
    const { target } = e;
    setChecked(target.checked);
    form.setFieldsValue({
      optionsType: target.checked ? 1 : 0,
    });
  };
  // 填值方式
  const onSelectChange = (e) => {
    setLableType(e);
  };
  const isRepeat = (hash, arr) => {
    for (var i in arr) {
      if (hash[arr[i]]) return true;
      hash[arr[i]] = true;
    }
    return false;
  };
  // 新增/修改请求
  const handleOk = () => {
    form.validateFields().then(() => {
      if (id) {
        let arr = [];
        if (form.getFieldValue('labelType') == 'Selection') {
          let selectName = [];
          let selectKeys = selectRows.map((x) => x.key);
          optionList.forEach((item) => {
            if (selectKeys.includes(item.key)) {
              selectName.push(item.optionName);
            }
          });
          let hash = {};
          for (var i = 0; i < selectName.length; i++) {
            if (!selectName[i]) {
              message.destroy();
              message.error(`请输入选项名称`);
              return;
            }
          }
          if (isRepeat(hash, selectName)) {
            message.destroy();
            message.error(`选项名称不能重复`);
            return;
          }
          arr = optionList
            .filter((e) => e.optionName)
            .map((item) => {
              return {
                optionName: item.optionName,
                deleFlag: selectKeys.includes(item.key) ? 0 : 1,
              };
            });
        }
        let data = {
          id: id,
          labelName: form.getFieldValue('labelName'),
          options: arr,
          labelRemark: form.getFieldValue('labelRemark'),
        };
      } else {
        let labelOptions = form.getFieldValue('labelOptions');
        let data = {
          ...form.getFieldsValue(),
          labelOptions: labelOptions && labelOptions.length != 0 ? labelOptions.join(',') : '',
        };
      }
    });
  };
  // 关闭弹窗
  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setId('');
    setLableType('');
  };
  // 删除标签请求
  const delAction = (id: string) => {};

  const handleChange = (value) => {
    // console.log(value);
  };

  const onRowSelectChange = (selectedRows) => {
    setSelectRows(selectedRows);
  };
  // 新增选项
  const addOption = () => {
    let optionObj = {
      optionName: '',
      key: optionList.length,
    };
    let list = [...optionList, optionObj];
    setOptionList(list);
    setSelectRows([...selectRows, optionObj]);
  };

  useEffect(() => {
    pageSearch();
  }, [pageParams]);

  useEffect(() => {
    let _authBtns = JSON.parse(sessionStorage.getItem('authBtns'));
    setAuthBtns(_authBtns);
  }, []);

  return {
    serachForm,
    form,
    pageData,
    pageParams,
    isModalVisible,
    modalTitle,
    modalType,
    children,
    labelType,
    checked,
    optionList,
    selectedRowKeys,
    selectRows,
    authBtns,
    formSearch,
    onPageChange,
    addTag,
    editTag,
    handleOk,
    handleCancel,
    delAction,
    handleChange,
    onCheckChange,
    onSelectChange,
    setOptionList,
    onRowSelectChange,
    addOption,
  };
};
