import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv, OperDiv } from '@/assets/style/list';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  DatePicker,
  InputNumber,
  message,
} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { history } from 'umi';
import { queryEntryDetail } from '@/app/request/requestApi';
import useServices from './useServices';
import { momenyFormat } from './momenyFormat';
import AuthorityComponent from '@/components/AuthorityComponent';

export default function (props) {
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    productCategory,
    btns,
    form,
    orderType,
    scrollY,
    getTable,
    exportReport,
    onEditEntry,
    onDeleteEntry,
    queryProductData,
    sourceSiteInfo,
    customerInfo,
    taxRate,
    onBatchDeletion,
  } = useServices(props);
  //操作
  const butt = ['Delete', 'Edit'];

  const mode = [
    { name: '自提', code: 'self' },
    { name: '送货', code: 'delivery' },
  ];
  //单位数据
  const Company = [{ name: 'TO', code: 'TO' }];
  //车牌号 正则校验
  // const pmEndValid = (rule, value, callback) => {
  //   let express =
  //     /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;
  //   let result = express.test(value);
  //   if (value) {
  //     if (result) {
  //       callback('');
  //     } else {
  //       callback('请输入正确格式车牌号');
  //     }
  //   } else {
  //     callback();
  //   }
  // };
  //数量校验
  const quantity = (rule, value, callback) => {
    if (outlineForm.getFieldValue('unit') == 'TO' || outlineForm.getFieldValue('unit') == 'M3') {
      if (!new RegExp('(^$)|^[0-9]+(.?[0-9]{1,3})?$').test(value)) {
        callback('单位最多可填写小数点后三位');
      } else {
        callback();
      }
    }
    callback();
  };

  //定义InputNumber的参数校验
  const NumberProps = {
    min: '0', //最小值
    // stringMode: true, //字符值模式，开启后支持高精度小数
    step: '0.0001', //小数位数
    formatter: (value: any) => {
      //指定输入框展示值的格式
      const reg1 = `${value}`.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3');
      return momenyFormat(reg1);
      //如果不需要添加千位分隔符可以直接返回return reg1
    },
  };
  const waterCapacityM3 = (rule, value, callback) => {
    if (!value && value != 0) {
      callback();
    } else if (value <= 0) {
      callback('请输入大于0的数字');
    } else {
      outlineForm.setFieldsValue({ waterCapacityM3: Math.floor(value * 100) / 100 });
    }
    callback();
  };
  const aroundPressure = (rule, value, callback) => {
    if (!value && value != 0) {
      callback();
    } else if (value <= 0) {
      callback('请输入大于0的数字');
    } else {
      outlineForm.setFieldsValue({ pressureBeforeBar: Math.floor(value * 10) / 10 });
    }
    callback();
  };
  const pressPressure = (rule, value, callback) => {
    if (!value && value != 0) {
      callback();
    } else if (value <= 0) {
      callback('请输入大于0的数字');
    } else {
      outlineForm.setFieldsValue({ pressureAfterBar: Math.floor(value * 10) / 10 });
    }
    callback();
  };
  const aroundTemperature = (rule, value, callback) => {
    if (!value) {
      callback();
    } else {
      outlineForm.setFieldsValue({ tempratureBefore: Math.floor(value * 10) / 10 });
    }
    callback();
  };
  const pressTemperature = (rule, value, callback) => {
    if (!value) {
      callback();
    } else {
      outlineForm.setFieldsValue({ tempratureAfter: Math.floor(value * 10) / 10 });
    }
    callback();
  };
  const untype = (rule, value, callback) => {
    if (value && value != 0) {
      callback();
    } else {
      callback('请输入含税单价');
      outlineForm.setFieldsValue({ unitPriceIncludingTax: '' });
    }
    callback();
  };
  const unstate = (rule, value, callback) => {
    if (value && value != 0) {
      callback();
    } else {
      callback('请输入不含税单价');
      outlineForm.setFieldsValue({ unitPriceExcludingTax: '' });
    }
    callback();
  };
  // 校验单据
  const verifyDocuments = (rule, value, callback) => {
    let regular = /(?<![0-9a-zA-Z])[0-9a-zA-Z]{4}(?![0-9a-zA-Z])/;
    let result = regular.test(value);
    if (value) {
      if (!result) {
        callback('请输入后四位单据尾号');
      } else {
        callback();
      }
    } else {
      callback();
    }
    callback();
  };
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '70px' },
  };
  const [outlineForm] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [id, setID] = useState();
  //勾选框内容id
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [billingId, setBillingId] = useState(null);
  const [productId, setProductId] = useState(null);
  const supplierType = sessionStorage.getItem('supplierType');
  // const supplierType = 'EJV';
  const { RangePicker } = DatePicker;

  const [state, setState] = useState([]);
  //如果选择长管氢气则展现隐藏部分
  const [show, setShow] = useState(false);
  const [condition, setCondition] = useState(null);

  //判断氢气与其他气体
  const [isGasCalibration, setGascalibration] = useState(null);
  useEffect(() => {}, []);
  //点击对账单详情
  const onDetails = (id: string) => {
    history.push({
      pathname: '/pto/supplier/details',
      state: { id: id },
    });
  };
  // 删除被退回的条目
  const delAction = (id: any) => {
    onDeleteEntry(id);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      // console.log(selected, selectedRows, changeRows); //布尔值 勾选id
    },
    selections: [Table.SELECTION_NONE, Table.SELECTION_ALL],
  };
  //编辑
  const editDetail = (record: any) => {
    setIsModalVisible(true);
    setID(record.id);
    //回 显
    queryEntryDetail(record.id).then((res) => {
      if (res.data) {
        setBillingId(res.data.billingId);
        setProductId(res.data.productId);
        let formatTime = moment(res.data.billingDate, 'YYYY-MM-DD HH:mm:ss');
        res.data.productName == '长管氢气' ? setShow(true) : setShow(false);
        res.data.modeTransport == '自提' ? setState(sourceSiteInfo) : setState(customerInfo);
        outlineForm.setFieldsValue({
          billingDate: formatTime,
          modeTransport: res.data.modeTransport,
          sourcePoint: res.data.sourcePoint,
          licensePlate: res.data.licensePlate,
          productName: res.data.productName,
          productQuantity: res.data.productQuantity,
          unit: res.data.unit,
          carNo: res.data.carNo,
          documentEndNumber: res.data.documentEndNumber,
          unitPriceIncludingTax: new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 4,
            maximumFractionDigits: 4,
          }).format(res.data.unitPriceIncludingTax),
          unitPriceExcludingTax: new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 4,
            maximumFractionDigits: 4,
          }).format(res.data.unitPriceExcludingTax),
          amountIncludingTax: new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 4,
            maximumFractionDigits: 4,
          }).format(res.data.amountIncludingTax),
          amountExcludingTax: new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 4,
            maximumFractionDigits: 4,
          }).format(res.data.amountExcludingTax),
          docTailNo: res.data.docTailNo,
          remarks: res.data.remarks,
          prechargePressure: res.data.prechargePressure,
          chargingPressure: res.data.chargingPressure,
          preChargeTemperature: res.data.preChargeTemperature,
          postChargeTemperature: res.data.postChargeTemperature,
          orderNo: res.data.orderNo,
          waterCapacityM3: res.data.waterCapacityM3,
          tempratureAfter: res.data.tempratureAfter,
          tempratureBefore: res.data.tempratureBefore,
          pressureAfterBar: res.data.pressureAfterBar,
          pressureBeforeBar: res.data.pressureBeforeBar,
        });
      } else {
        message.warning(res.msg);
      }
    });
    outlineForm.getFieldValue('modeTransport') == '自提' ? setState(sourceSiteInfo) : setState(customerInfo);
  };
  // 取消 关闭弹框 form表单清空 重置控制水容积部分显示
  const handleCancel = () => {
    outlineForm.getFieldValue('productName');
    setIsModalVisible(false);
    outlineForm.resetFields();
    setShow(false);
    setState([]);
    setProductId('');
  };

  //提交表单触发回调事件
  const onFinish = (values: any) => {
    onEditEntry(values, id, billingId, productId);
    handleCancel();
    outlineForm.resetFields();
    getTable();
  };
  //去除千分位中的‘，’
  const delcommafy = (num) => {
    if (num && num != 'undefined' && num != 'null') {
      let numS = num;
      numS = numS.toString();
      numS = numS.replace(/,/gi, '');
      return numS;
    } else {
      return num;
    }
  };

  const columns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.billingId)}>
            {text}
          </span>
        );
      },
      width: 150,
    },
    {
      title: '日期',
      dataIndex: 'billingDate',
      key: 'billingDate',
      align: 'center',
      width: 150,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      align: 'center',
      width: 150,
    },
    {
      title: '货源点/客户',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      width: 120,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 3,
              maximumFractionDigits: 3,
            }).format(text)
          : '';
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      width: 120,
    },
    {
      title: '含税单价',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      align: 'center',
      width: 150,
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税金额',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      align: 'center',
      width: 150,
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      width: 120,
    },
    {
      title: '退回原因',
      dataIndex: 'returnReason',
      key: 'returnReason',
      align: 'center',
      width: 180,
    },
    {
      title: '单据尾号',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      align: 'center',
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
      width: 180,
    },
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (text: any, record) => {
        const _btnJsx = {
          Delete: (
            <AuthorityComponent type="Backentry-Delete">
              <Popconfirm
                key="del"
                title="确定删除该条目？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => delAction(record.id)}
              >
                <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
              </Popconfirm>
            </AuthorityComponent>
          ),
          Edit: (
            <AuthorityComponent type="Backentry-Edit">
              <Button key="edit" type="link" onClick={() => editDetail(record)}>
                <EditOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }} />
              </Button>
            </AuthorityComponent>
          ),
        };
        return <OperDiv>{butt.map((item) => _btnJsx[item])}</OperDiv>;
      },
    },
  ];

  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={23}>
            <Col span={6}>
              <Form.Item name="searchDate" label="日期">
                <RangePicker separator="-" />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="productName" label="产品名称">
                <Select placeholder="产品名称">
                  <Select.Option value="">所有</Select.Option>
                  {queryProductData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.productName}>
                        {x.productName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="keywords" label="关键字">
                <Input placeholder="关键字" className="keywordInput" />
              </Form.Item>
            </Col>
            <Col span={2}>
              <AuthorityComponent type="Backentry-Search">
                <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                  搜索
                </Button>
              </AuthorityComponent>
            </Col>
            <Col span={2}>
              <AuthorityComponent type="Backentry-Export">
                <Button onClick={() => exportReport()}>
                  <i className="iconfont icon-export"></i>
                  <span>导出账单</span>
                </Button>
              </AuthorityComponent>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <div>
          <Popconfirm
            key="del"
            title="确定删除所选条目?"
            icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
            onConfirm={() => onBatchDeletion(selectedRowKeys)}
          >
            <Button style={{ margin: '10px 10px' }}>批量删除</Button>
          </Popconfirm>
          <Table
            style={{ width: '100%' }}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            dataSource={data}
            pagination={{
              // total: total,
              // current: current,
              // pageSize: pageSize,
              showSizeChanger: true,
            }}
            rowSelection={rowSelection}
            onChange={onPageChange}
            columns={columns}
            scroll={{ y: scrollY }}
            rowKey="id"
          />
          <Modal
            className="edit_box"
            title="对账单产品信息修改"
            visible={isModalVisible}
            onCancel={handleCancel}
            width={650}
            footer
          >
            <Form {...layout} onFinish={onFinish} form={outlineForm} desc=" ">
              <Row gutter={22}>
                <Col span={11}>
                  <Form.Item label="日期" name="billingDate" labelCol={{ span: 10 }} rules={[{ required: true }]}>
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="运输方式" name="modeTransport" labelCol={{ span: 10 }} rules={[{ required: true }]}>
                    <Select
                      disabled={supplierType == 'EJV'}
                      placeholder="运输方式"
                      onChange={(e) => {
                        //运输方式发生变化时 货源点会清空
                        outlineForm.resetFields(['sourcePoint']);
                        if (e == '自提') {
                          setState(sourceSiteInfo);
                        } else {
                          setState(customerInfo);
                        }
                      }}
                    >
                      {supplierType == 'EJV' ? (
                        <Select.Option value="自提">自提</Select.Option>
                      ) : (
                        mode.map((x, index) => {
                          return (
                            <Select.Option key={index} value={x.name}>
                              {x.name}
                            </Select.Option>
                          );
                        })
                      )}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={22}>
                <Col span={22}>
                  <Form.Item
                    label="货源点/客户"
                    name="sourcePoint"
                    labelCol={{ span: 5 }}
                    rules={[{ required: outlineForm.getFieldValue('modeTransport') == '自提' ? false : true }]}
                  >
                    <Select placeholder="货源点/客户" showSearch allowClear>
                      {/* {state.map((x, index) => {
                        return (
                          <Select.Option key={x.customerId} value={x.customerTitle}>
                            {x.customerTitle}
                          </Select.Option>
                        );
                      })} */}
                      {state.map((x, index) => {
                        return (
                          <Select.Option key={index} value={x.name}>
                            {x.name}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="车牌号" name="carNo" labelCol={{ span: 10 }} rules={[{ required: true }]}>
                    <Input placeholder="车牌号" />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="产品名称" name="productName" labelCol={{ span: 10 }} rules={[{ required: true }]}>
                    <Select
                      placeholder="产品名称"
                      onChange={(e, label) => {
                        setProductId(label.keys);
                        if (e == '长管氢气') {
                          outlineForm.setFieldsValue({ unit: 'M3' });
                          outlineForm.getFieldValue('unit') != 'M3' && outlineForm.resetFields(['unit']);
                          setShow(true);
                        } else {
                          outlineForm.setFieldsValue({ unit: 'TO' });
                          setShow(false);
                        }
                        if (
                          (e == '长管氢气' && outlineForm.getFieldValue('unitPriceIncludingTax') > 50) ||
                          outlineForm.getFieldValue('productQuantity') == 40
                        ) {
                          outlineForm.setFieldsValue({ unitPriceIncludingTax: 0 });
                          outlineForm.setFieldsValue({ unitPriceExcludingTax: 0 });
                          outlineForm.setFieldsValue({ amountIncludingTax: 0 });
                          outlineForm.setFieldsValue({ amountExcludingTax: 0 });
                          outlineForm.setFieldsValue({ productQuantity: 0 });
                        }
                        if (e != '长管氢气' && outlineForm.getFieldValue('productQuantity') > 40) {
                          outlineForm.setFieldsValue({ productQuantity: 0 });
                          outlineForm.setFieldsValue({ unitPriceIncludingTax: 0 });
                          outlineForm.setFieldsValue({ unitPriceExcludingTax: 0 });
                          outlineForm.setFieldsValue({ amountIncludingTax: 0 });
                          outlineForm.setFieldsValue({ amountExcludingTax: 0 });
                        }
                      }}
                    >
                      {queryProductData.map((x, index) => {
                        return (
                          <Select.Option keys={x.id} value={x.productName}>
                            {x.productName}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="单位" name="unit" labelCol={{ span: 10 }} rules={[{ required: true }]}>
                    <Select
                      placeholder="单位"
                      onChange={(e) => {
                        e == 'TO' || e == 'M3' ? setCondition(true) : setCondition(false);
                      }}
                    >
                      {outlineForm.getFieldValue('productName') == '长管氢气' ? (
                        <Select.Option value="M3">M3</Select.Option>
                      ) : (
                        Company.map((x, index) => {
                          return (
                            <Select.Option key={index} value={x.code}>
                              {x.name}
                            </Select.Option>
                          );
                        })
                      )}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item
                    label="数量"
                    name="productQuantity"
                    labelCol={{ span: 10 }}
                    rules={[
                      {
                        validator: quantity,
                      },
                    ]}
                  >
                    <InputNumber
                      max={isGasCalibration ? 6000 : 40}
                      style={{ width: '100%' }}
                      onChange={(e) => {
                        let money = new Intl.NumberFormat('en-US', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 6,
                        }).format(delcommafy(outlineForm.getFieldValue('unitPriceIncludingTax')) / (1 + taxRate));
                        let money2 = new Intl.NumberFormat('en-US', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(e) * delcommafy(outlineForm.getFieldValue('unitPriceIncludingTax')));
                        let money3 = new Intl.NumberFormat('en-US', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(
                          Number(e) * (delcommafy(outlineForm.getFieldValue('unitPriceIncludingTax')) / (1 + taxRate)),
                        );
                        outlineForm.setFieldsValue({
                          unitPriceExcludingTax: money, //不含税单价
                          amountIncludingTax: money2, //含税金额
                          amountExcludingTax: money3, //不含税金额
                        });
                        if (outlineForm.getFieldValue('productName') == '长管氢气') {
                          setGascalibration(true);
                        } else {
                          setGascalibration(false);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                {supplierType != 'EJV' && (
                  <>
                    <Col span={11}>
                      <Form.Item
                        label="含税单价"
                        labelCol={{ span: 10 }}
                        name="unitPriceIncludingTax"
                        rules={[
                          {
                            validator: untype,
                          },
                        ]}
                      >
                        <InputNumber
                          disabled={supplierType == 'EJV'}
                          {...NumberProps}
                          max={outlineForm.getFieldValue('productName') == '长管氢气' ? 50 : 10000}
                          placeholder="含税单价"
                          style={{ width: '100%' }}
                          onChange={(e) => {
                            let money = new Intl.NumberFormat('en-US', {
                              minimumFractionDigits: 4,
                              maximumFractionDigits: 4,
                            }).format(e / (1 + taxRate));
                            let money2 = new Intl.NumberFormat('en-US', {
                              minimumFractionDigits: 4,
                              maximumFractionDigits: 4,
                            }).format(e * outlineForm.getFieldValue('productQuantity'));
                            let money3 = new Intl.NumberFormat('en-US', {
                              minimumFractionDigits: 4,
                              maximumFractionDigits: 4,
                            }).format((e / (1 + taxRate)) * outlineForm.getFieldValue('productQuantity'));
                            outlineForm.setFieldsValue({
                              unitPriceExcludingTax: money,
                              amountIncludingTax: money2,
                              amountExcludingTax: money3, //不含税金额
                            });
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item
                        label="不含税单价"
                        name="unitPriceExcludingTax"
                        labelCol={{ span: 10 }}
                        rules={[
                          {
                            validator: unstate,
                          },
                        ]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          placeholder="不含税单价"
                          disabled={true}
                          onChange={(e) => {
                            let money = new Intl.NumberFormat('en-US', {
                              minimumFractionDigits: 4,
                              maximumFractionDigits: 4,
                            }).format(Number(e) * (1 + taxRate));
                            let money3 = new Intl.NumberFormat('en-US', {
                              minimumFractionDigits: 4,
                              maximumFractionDigits: 4,
                            }).format(Number(e) * outlineForm.getFieldValue('productQuantity'));
                            let money2 = new Intl.NumberFormat('en-US', {
                              minimumFractionDigits: 4,
                              maximumFractionDigits: 4,
                            }).format(Number(e) * (1 + taxRate) * outlineForm.getFieldValue('productQuantity'));
                            outlineForm.setFieldsValue({
                              unitPriceIncludingTax: money,
                              amountExcludingTax: money3,
                              amountIncludingTax: money2, //含税金额
                            });
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item label="含税金额" labelCol={{ span: 10 }} name="amountIncludingTax">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item label="不含税金额" name="amountExcludingTax" labelCol={{ span: 10 }}>
                        <Input disabled />
                      </Form.Item>
                    </Col>
                  </>
                )}
                <Col span={11}>
                  <Form.Item label="货币" labelCol={{ span: 10 }} name="currency">
                    <Input placeholder="货币" defaultValue="CNY" disabled />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item
                    label="单据尾号/DN#"
                    name="documentEndNumber"
                    labelCol={{ span: 10 }}
                    rules={[
                      {
                        validator: outlineForm.getFieldValue('modeTransport') == '送货' && verifyDocuments,
                      },
                    ]}
                  >
                    <Input placeholder="单据尾号" />
                  </Form.Item>
                </Col>
              </Row>
              {show && (
                <div>
                  <Row gutter={20}>
                    <Col span={11}>
                      <Form.Item
                        label="水容积(M3)"
                        name="waterCapacityM3"
                        labelCol={{ span: 10 }}
                        rules={[
                          {
                            validator: waterCapacityM3,
                          },
                        ]}
                      >
                        <InputNumber style={{ width: '100%' }} step="0.01" placeholder="水容积(M3)" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={20}>
                    <Col span={11}>
                      <Form.Item
                        label="充装前压力(ban)"
                        name="pressureBeforeBar"
                        rules={[
                          {
                            validator: aroundPressure,
                          },
                        ]}
                        labelCol={{ span: 10 }}
                      >
                        <InputNumber style={{ width: '100%' }} step="0.1" placeholder="充装前压力" />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item
                        label="充装后压力(ban)"
                        name="pressureAfterBar"
                        rules={[
                          {
                            validator: pressPressure,
                          },
                        ]}
                        labelCol={{ span: 10 }}
                      >
                        <InputNumber style={{ width: '100%' }} step="0.1" placeholder="充装后压力" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={20}>
                    <Col span={11}>
                      <Form.Item
                        label="充装前温度(℃)"
                        name="tempratureBefore"
                        rules={[
                          {
                            validator: aroundTemperature,
                          },
                        ]}
                        labelCol={{ span: 10 }}
                      >
                        <InputNumber style={{ width: '100%' }} step="0.1" placeholder="充装前温度" />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item
                        label="充装后温度(℃)"
                        name="tempratureAfter"
                        rules={[
                          {
                            validator: pressTemperature,
                          },
                        ]}
                        labelCol={{ span: 10 }}
                      >
                        <InputNumber style={{ width: '100%' }} step="0.1" placeholder="充装后温度" />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              )}
              <Row>
                <Col span={22}>
                  <Form.Item label="备注" name="remarks">
                    <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={11} push={5}>
                  <Form.Item>
                    <Button type="primary" htmlType="submit">
                      提交
                    </Button>
                  </Form.Item>
                </Col>
                <Col span={11} push={5}>
                  <Form.Item>
                    <Button onClick={handleCancel}>取消</Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Modal>
        </div>
      </TableWrapDiv>
    </div>
  );
}
