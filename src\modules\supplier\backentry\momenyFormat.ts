export const momenyFormat = (num) => {
  // 数字转字符串
  let result = `${num}`;
  // 校验输入值是否为数字
  const regNumber = /^\d+$|(\d+\.\d+)/g;
  // 校验小数点及右侧部分
  const regRight = /(?<=\d)(\.\d+)/g;
  // 校验小数点左侧的部分
  const regLeft = /(\d)(?=(\d{3})+$)/g;
  // 判断是否是数字
  if (regNumber.test(result)) {
    // 判断是否包含小数点
    if (/\./g.test(result)) {
      // 提取小数点和右侧部分
      const right = regRight.exec(result)[0];
      // 替换小数点和右侧部分为空白，得到小数点左侧部分
      // 对左侧部分进行千分位处理
      const left = result.replace(regRight, '').replace(regLeft, '$1,');
      // 左右两部分拼接
      result = `${left}${right}`;
    } else {
      // 不包含小数点，直接千分位处理
      result = result.replace(regLeft, '$1,');
    }
  } else {
    console.warn(`invalid number ${num}`);
  }
  return result;
};
