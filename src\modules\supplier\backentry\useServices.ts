import { useRef, useState, useEffect } from 'react';
import {
  QueryReturnEntryPageInfo,
  exportPtoProductExcel,
  editReturnEntryInfo,
  delReturnEntryInfo,
  queryProductInfo,
  queryLoginUser,
  deleteReturnEntryList,
} from '@/app/request/requestApi';
import { querySourceSiteOfSupplier, queryCustomerOfCurrentUser } from '@/app/request/apiInternal';
import { history } from 'umi';
import { useLocation } from 'umi';
import { message, Form } from 'antd';
import moment from 'moment';
import { getTableScroll } from '@/tools/utils';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const location = useLocation<any>();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [orderStatus, setOrderStatus] = useState([]);
  const [queryProductData, setQueryProductData] = useState(<any>[]);
  const [sourceSiteInfo, setSourceSiteInfo] = useState([]);
  const [customerInfo, setCustomerInfo] = useState([]);
  const [taxRate, setTaxRate] = useState(null);
  const [search, setSearch] = useState(<any>{
    status: '',
    orderType: '',
    productCategory: '',
    keyword: '',
    searchFromDate: '',
    searchToDate: '',
  });
  const [btns, setBtns] = useState([]);
  // 勾选框
  const [selectedRowKeys, setSelectRowKeys] = useState([]);
  const onSelectChange = (selectedKeys) => {
    setSelectRowKeys(selectedKeys);
  };
  //导出被退回的条目的账单
  const exportReport = () => {
    exportPtoProductExcel({}).then((res: any) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = 'PTO退回条目.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };

  //删除被退回条目
  const onDeleteEntry = (id) => {
    delReturnEntryInfo(id).then((res) => {
      if (res.success) {
        message.success(res.msg);
        getTable();
      } else {
        message.warning(res.msg);
      }
    });
  };
  //批量删除
  const onBatchDeletion = (idList) => {
    deleteReturnEntryList({ idList }).then((res) => {
      if (res.success) {
        getTable();
      } else {
        message.warning(res.msg);
      }
    });
  };
  //被退回条目编辑
  const onEditEntry = (parameters, id, billingId, productId) => {
    const { billingDate } = parameters;
    let time_billingDate = moment(billingDate).format('YYYY-MM-DD');
    editReturnEntryInfo({
      ...parameters,
      billingDate: time_billingDate,
      id,
      billingId,
      productId,
    }).then((res) => {
      if (res.success) {
        message.success('提交成功!');
        getTable();
      } else {
        message.warning(res.msg);
        2;
      }
    });
  };

  const getTable = () => {
    let searchFromDate: string;
    let searchToDate: string;
    if (!form.getFieldValue('searchDate')) {
      searchFromDate = '';
      searchToDate = '';
    } else {
      searchFromDate = form.getFieldValue('searchDate')[0];
      searchToDate = form.getFieldValue('searchDate')[1];
    }
    //调用被退回的条目列表接口
    QueryReturnEntryPageInfo({
      ...form.getFieldsValue(),
      billingStartDate: searchFromDate,
      billingEndDate: searchToDate,
      pageIndex: 1,
      pageSize: 99999,
    }).then((res) => {
      // console.log(res.msg);
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
  };

  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      // 清除订阅
      window.removeEventListener('resize', resize);
    };
  }, []);
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  useEffect(() => {
    //获取产品信息名称
    queryProductInfo('').then((res) => {
      if (res.success) {
        setQueryProductData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
    querySourceSiteOfSupplier('').then((res) => {
      if (res.success) {
        setSourceSiteInfo(res?.data);
      } else {
        message.error(res?.msg);
      }
    });
    queryCustomerOfCurrentUser('').then((res) => {
      if (res.success) {
        const data = res?.data.map((item) => {
          return {
            name: item.customerTitle,
            id: item.id,
          };
        });
        setCustomerInfo(data);
      } else {
        message.error(res?.msg);
      }
    });
    //获取税率
    queryLoginUser().then((res) => {
      if (res.success) {
        setTaxRate(res.data.taxRate);
      }
    });
  }, [location]);
  //翻页：重置current
  const onPageChange = (pagination, filters, sorter) => {
    setSelectRowKeys([]);
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const onValuesChange = (changedValues, allValues) => {
    setSearch({ ...allValues });
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };

  return {
    search,
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onValuesChange,
    onPageChange,
    getTable,
    tableHeight,
    btns,
    form,
    scrollY,
    exportReport,
    selectedRowKeys,
    setSelectRowKeys,
    onSelectChange,
    onEditEntry,
    onDeleteEntry,
    queryProductData,
    sourceSiteInfo,
    customerInfo,
    taxRate,
    onBatchDeletion,
  };
};
