import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv, OperDiv } from '@/assets/style/list';
import { Form, Modal, Select, Input, Row, Col, Button, Table, Popconfirm, DatePicker } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import moment from 'moment';
import { history } from 'umi';
import useServices from './useServices';
import AuthorityComponent from '@/components/AuthorityComponent';

export default memo(function (props) {
  const butt = ['Delete', 'Edit'];
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    productCategory,
    btns,
    form,
    orderType,
    scrollY,
    onDeleteEntry,
    onBatchDeletion,
  } = useServices(props);
  const [state, setState] = useState();

  //勾选框内容id
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const { RangePicker } = DatePicker;

  useEffect(() => {}, []);
  //点击对账单详情
  const onDetails = (id: string) => {
    console.log(id);
    history.push({
      pathname: '/pto/supplier/details',
      state: { id: id },
    });
  };
  const delAction = (id: any) => {
    onDeleteEntry(id);
  };

  //勾选批量删除
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
      console.log(selectedRowKeys);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      // console.log(selected, selectedRows, changeRows); //布尔值 勾选id
    },
    selections: [Table.SELECTION_NONE, Table.SELECTION_ALL],
  };

  const editDetail = (id) => {
    console.log(id);
    history.push({
      pathname: '/pto/supplier/initstatement',
      query: { id: id, check: 'backstatement' },
    });
  };

  const columns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.id)}>
            {text}
          </span>
        );
      },
      width: 150,
    },
    {
      title: '提交日期',
      dataIndex: 'submissionDate',
      key: 'submissionDate',
      align: 'center',
      width: 150,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '账单开始日期',
      dataIndex: 'billingStartDate',
      key: 'billingStartDate',
      align: 'center',
      width: 200,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '账单截止日期',
      dataIndex: 'billingEndDate',
      key: 'billingEndDate',
      align: 'center',
      ellipsis: true,
      width: 150,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '条目数量',
      dataIndex: 'productNumber',
      key: 'productNumber',
      align: 'center',
      ellipsis: true,
      width: 180,
    },
    {
      title: '退回原因',
      dataIndex: 'returnReason',
      key: 'returnReason',
      align: 'center',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      fixed: 'right',
      width: 200,
      render: (text: any, record: { id: any; orderNo: any }) => {
        const _btnJsx = {
          Delete: (
            <AuthorityComponent type="Backstatement-Delete">
              <Popconfirm
                key="del"
                title="确定删除该对账单？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => delAction(record.id)}
              >
                <Button type="link" danger>
                  <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
                </Button>
              </Popconfirm>
            </AuthorityComponent>
          ),
          Edit: (
            <AuthorityComponent type="Backstatement-Edit">
              <Button key="edit" type="link" onClick={() => editDetail(record.id)}>
                <EditOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }} />
              </Button>
            </AuthorityComponent>
          ),
        };
        return <OperDiv>{butt.map((item) => _btnJsx[item])}</OperDiv>;
      },
    },
  ];
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={25}>
            <Col span={6}>
              <Form.Item name="searchDate" label="提交日期">
                <RangePicker separator="-" />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="keywords" label="关键字">
                <Input placeholder="关键字" className="keywordInput" />
              </Form.Item>
            </Col>
            <Col span={4}>
              <AuthorityComponent type="Backstatement-Search">
                <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                  搜索
                </Button>
              </AuthorityComponent>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <div id="webTable">
          <Popconfirm
            key="del"
            title="确定删除所选条目?"
            icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
            onConfirm={() => onBatchDeletion(selectedRowKeys)}
          >
            <Button style={{ margin: '10px 10px' }}>批量删除</Button>
          </Popconfirm>
          <Table
            style={{ width: '100%' }}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            dataSource={data}
            rowSelection={rowSelection}
            pagination={{
              // total: total,
              // current: current,
              // pageSize: pageSize,
              showSizeChanger: true,
            }}
            onChange={onPageChange}
            scroll={{ y: scrollY }}
            columns={columns}
            rowKey="id"
          />
        </div>
      </TableWrapDiv>
    </div>
  );
});
