import { useRef, useState, useEffect } from 'react';
import { QueryBillingReturnPageInfo, delBillingInfoById, delBillingInfoByIdList } from '@/app/request/requestApi';
import { history } from 'umi';
import { message, Form } from 'antd';
import moment from 'moment';
import { getTableScroll } from '@/tools/utils';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const [form] = Form.useForm();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [orderStatus, setOrderStatus] = useState([]);
  const [search, setSearch] = useState(<any>{
    status: '',
    orderType: '',
    productCategory: '',
    keyword: '',
    searchFromDate: '',
    searchToDate: '',
  });
  const [btns, setBtns] = useState([]);
  // 勾选框
  const [selectedRowKeys, setSelectRowKeys] = useState([]);
  const onSelectChange = (selectedKeys) => {
    setSelectRowKeys(selectedKeys);
  };

  const onDeleteEntry = (id) => {
    console.log(id);
    delBillingInfoById(id).then((res) => {
      if (res.success) {
        message.success(res.msg);
        getTable();
      } else {
        message.warning(res.msg);
      }
    });
  };
  //勾选删除
  const onBatchDeletion = (idList) => {
    delBillingInfoByIdList({ idList }).then((res) => {
      if (res.success) {
        getTable();
      } else {
        message.warning(res.msg);
      }
    });
  };

  const getTable = () => {
    let searchFromDate: string;
    let searchToDate: string;
    if (!form.getFieldValue('searchDate')) {
      searchFromDate = '';
      searchToDate = '';
    } else {
      searchFromDate = form.getFieldValue('searchDate')[0];
      searchToDate = form.getFieldValue('searchDate')[1];
    }
    //调用被退回的条目列表接口
    console.log(form.getFieldsValue());
    QueryBillingReturnPageInfo({
      ...form.getFieldsValue(),
      billingStartDate: searchFromDate,
      billingEndDate: searchToDate,
      pageIndex: current,
      pageSize: pageSize,
    }).then((res) => {
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
  };
  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      // 清除订阅
      window.removeEventListener('resize', resize);
    };
  }, []);
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  //翻页：重置current
  const onPageChange = (pagination, filters, sorter) => {
    setSelectRowKeys([]);
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const onValuesChange = (changedValues, allValues) => {
    setSearch({ ...allValues });
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };

  return {
    search,
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onValuesChange,
    onPageChange,
    getTable,
    tableHeight,
    btns,
    form,
    scrollY,
    selectedRowKeys,
    setSelectRowKeys,
    onSelectChange,
    onDeleteEntry,
    onBatchDeletion,
  };
};
