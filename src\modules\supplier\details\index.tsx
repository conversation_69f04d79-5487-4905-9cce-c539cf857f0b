import {
  Button,
  Upload,
  Row,
  Col,
  Space,
  Form,
  Input,
  Select,
  DatePicker,
  Table,
  message,
  InputNumber,
  Popconfirm,
} from 'antd';
import React, { memo, useState, useEffect, useRef } from 'react';
import moment from 'moment';
import { ButtonFooter, CardForm, CardTable, SectionTitle, HeaderTitle } from '../initstatement/style';
import { queryBillingDetailById } from '@/app/request/apiHome';
import { useLocation } from 'umi';
import { queryOrderDetail } from '@/app/request/requestApi';
import TableTitle from '@/components/TableTitle';
import { classList, showOptionLabel } from '../../../components/StateVerification';
import { CopyOutlined, DeleteOutlined, PlusSquareOutlined } from '@ant-design/icons';
const { Option } = Select;

export default memo(function (props) {
  const [form] = Form.useForm();
  const tableRef = useRef<any>();
  const location = useLocation<any>();
  // const [fileList, setFile] = useState<any>();
  const [data, setdata] = useState();
  const [orderData, setOrderData] = useState();
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 8 },
    wrapperCol: { span: 24 },
  };
  const objectToFormData = (obj, form) => {
    const fd = form || new FormData();
    for (let property in obj) {
      if (obj.hasOwnProperty(property)) {
        if (typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
          objectToFormData(obj[property], fd);
        } else if (obj[property] instanceof File) {
          fd.append('file', obj[property]);
        }
      }
    }
    return fd;
  };

  const columns: any = [
    {
      title: '序号',
      align: 'center',
      key: 'order',
      width: 80,
      render: (data, record, index) => {
        return index + 1;
      },
    },
    {
      title: '日期', //必填项
      align: 'center',
      dataIndex: 'billingDate',
      key: 'billingDate',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式', //必填项
      align: 'center',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      width: 100,
    },
    {
      title: '货源点/客户', //必填/非必填-根据运输方式
      align: 'center',
      ellipsis: true,
      key: 'sourcePoint',
      dataIndex: 'sourcePoint',
      width: 150,
    },
    {
      title: '车牌号', //必填项
      align: 'center',
      dataIndex: 'carNo',
      key: 'carNo',
      width: 150,
    },
    {
      title: '产品名称', //必填/非必填？
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
      width: 100,
    },
    {
      title: '数量', //必填项,选择了TO或M3则最多3位小数，否则必须为整数
      align: 'center',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 3,
              maximumFractionDigits: 3,
            }).format(text)
          : '';
      },
    },
    {
      title: '单位', //必填项
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
    },
    {
      title: '含税单价', //必填项
      align: 'center',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4,
            }).format(text)
          : '';
      },
    },
    {
      title: '不含税单价', //计算得出
      align: 'center',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 6,
              maximumFractionDigits: 6,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税金额', //计算得出
      align: 'center',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '不含税金额',
      align: 'center',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币', //不可选择，自动带出，默认CNY
      align: 'center',
      dataIndex: 'currency',
      key: 'currency',
      width: 100,
      render: (data, record) => {
        return data ? <div>{data}</div> : <div>CNY</div>;
      },
    },
    {
      title: '单据尾号', //非必填，4位数字
      align: 'center',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      width: 100,
    },
    {
      title: '备注', //非必填
      align: 'center',
      dataIndex: 'remarks',
      width: 100,
      ellipsis: true,
      key: 'remarks',
    },
    {
      title: '水容积(M3)',
      align: 'center',
      dataIndex: 'waterCapacityM3',
      key: 'waterCapacityM3',
      width: 100,
    },
    {
      title: '充装前压力(bar)',
      align: 'center',
      dataIndex: 'pressureAfterBar',
      key: 'pressureAfterBar',
      width: 100,
    },
    {
      title: '充装后压力(bar)',
      align: 'center',
      dataIndex: 'pressureBeforeBar',
      key: 'pressureBeforeBar',
      width: 100,
    },
    {
      title: '充装前温度(℃)',
      align: 'center',
      dataIndex: 'tempratureBefore',
      key: 'tempratureBefore',
      width: 100,
    },
    {
      title: '充装后温度(℃)',
      align: 'center',
      dataIndex: 'tempratureAfter',
      key: 'tempratureAfter',
      width: 100,
    },
  ];
  const ordercolumns: any = [
    {
      title: '对账单号',
      align: 'center',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      width: 100,
    },
    {
      title: '日期', //必填项
      align: 'center',
      dataIndex: 'billingDate',
      key: 'billingDate',
      width: 100,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式', //必填项
      align: 'center',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      width: 100,
    },
    {
      title: '货源点/客户', //必填/非必填-根据运输方式
      align: 'center',
      key: 'sourcePoint',
      dataIndex: 'sourcePoint',
      ellipsis: true,
      width: 100,
    },
    {
      title: '车牌号', //必填项
      align: 'center',
      dataIndex: 'carNo',
      key: 'carNo',
      width: 100,
    },
    {
      title: '产品名称', //必填/非必填？
      align: 'center',
      dataIndex: 'productName',
      ellipsis: true,
      key: 'productName',
      width: 150,
    },
    {
      title: '数量', //必填项,选择了TO或M3则最多3位小数，否则必须为整数
      align: 'center',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 3,
              maximumFractionDigits: 3,
            }).format(text)
          : '';
      },
    },
    {
      title: '单位', //必填项
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
    },
    {
      title: '不含税单价', //必填项
      align: 'center',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      width: 100,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 6,
              maximumFractionDigits: 6,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税单价', //计算得出
      align: 'center',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      width: 100,
      render: (data, record, index) => {
        return <div>{data && Number(data)?.toLocaleString()}</div>;
      },
    },
    {
      title: '含税金额', //计算得出
      align: 'center',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      width: 100,
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '不含税金额',
      align: 'center',
      dataIndex: 'amountExcludingTax',
      key: 'amountExcludingTax',
      width: 100,
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币', //不可选择，自动带出，默认CNY
      align: 'center',
      dataIndex: 'currency',
      key: 'currency',
      width: 100,
      render: (data, record) => {
        return data ? <div>{data}</div> : <div>CNY</div>;
      },
    },
    {
      title: '单据尾号', //非必填，4位数字
      align: 'center',
      ellipsis: true,
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      width: 150,
    },
    {
      title: '备注', //非必填
      align: 'center',
      dataIndex: 'remarks',
      width: 150,
      ellipsis: true,
      key: 'remarks',
    },
  ];
  const Upperlevel = () => {
    window.history.back();
  };

  const getDataById = () => {
    queryBillingDetailById(location?.state?.id).then((res) => {
      if (res.data) {
        setdata(res.data.entryList);
        form.setFieldsValue({
          lindeClearingCompany: res.data.lindeClearingCompany,
          region: res.data.region,
          statementNumber: res.data.statementNumber,
          phone: res.data.phone,
          contacts: res.data.contacts,
          billingEndDate: moment(res.data.billingEndDate).format('YYYY-MM-DD'),
          billingStartDate: moment(res.data.billingStartDate).format('YYYY-MM-DD'),
          submissionDate: moment(res.data.submissionDate).format('YYYY-MM-DD'),
        });
      } else {
        // message.error();
      }
    });
    queryOrderDetail(location?.state?.id).then((res) => {
      if (res.data) {
        console.log(res.data);
        setOrderData(res.data.dtls);
        form.setFieldsValue({
          poNo: res.data.poNo,
          poItem: res.data.poItem,
          clearingCompanyName: res.data.clearingCompanyName,
          clearingCompanyCode: res.data.clearingCompanyCode,
          quantity: res.data.quantity,
          totalAmountWithTax: res.data.totalAmountWithTax,
        });
      }
    });
  };
  useEffect(() => {
    if (location?.state?.id) {
      getDataById();
    }
  }, [location]);

  return (
    <>
      {location?.state?.order == 'order' ? <HeaderTitle>查看订单</HeaderTitle> : <HeaderTitle>查看对账单</HeaderTitle>}
      <CardForm>
        <TableTitle title={location?.state?.order == 'order' ? '订单基本信息' : '账单基本信息'}></TableTitle>
        <Form form={form} {...layout} layout="vertical">
          {location?.state?.order == 'order' ? (
            <Row gutter={20}>
              <Col span={6}>
                <Form.Item name="poNo" label="订单号">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="poItem" label="订单条目">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="clearingCompanyName" label="林德结算公司">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="clearingCompanyCode" label="公司代码">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="quantity" label="结算数量">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="totalAmountWithTax" label="含税金额">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
            </Row>
          ) : (
            <Row gutter={20}>
              <Col span={6}>
                <Form.Item name="statementNumber" label="对账单号">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="submissionDate" label="提交日期">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="billingStartDate" label="账单开始日期">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="billingEndDate" label="账单截至日期">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="lindeClearingCompany" label="林德结算公司">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="region" label="区域">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="contacts" label="联系人">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="phone" label="联系电话">
                  <Input allowClear disabled />
                </Form.Item>
              </Col>
            </Row>
          )}
        </Form>
      </CardForm>
      <CardTable>
        <TableTitle title={location?.state?.order == 'order' ? '对应账单' : '账单明细信息'}></TableTitle>
        <div ref={tableRef}>
          <Table
            columns={location?.state?.order == 'order' ? ordercolumns : columns}
            dataSource={location?.state?.order == 'order' ? orderData : data}
            rowKey={(record) => record?.key}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            scroll={{ x: columns?.length * 190 }}
          />
        </div>
      </CardTable>
      <ButtonFooter>
        <Space size="large">
          <Button type="primary" onClick={() => Upperlevel()}>
            返回
          </Button>
        </Space>
      </ButtonFooter>
    </>
  );
});
