import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv, OperDiv, BtnGreenWrap } from '@/assets/style/list';
import { Form, Modal, Select, Input, Row, Col, Button, Table, Popconfirm, DatePicker, Upload } from 'antd';
import { QuestionCircleOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import moment from 'moment';
import { history } from 'umi';
import useServices from './useServices';
import AuthorityComponent from '@/components/AuthorityComponent';

export default memo(function (props) {
  const butt = ['Delete', 'Edit'];
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    productCategory,
    btns,
    form,
    orderType,
    exportReport,
    scrollY,
    onDeleteDraft,
    onBatchDeletion,
  } = useServices(props);
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '70px' },
  };
  const [outlineForm] = Form.useForm();

  //勾选框内容id
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { RangePicker } = DatePicker;

  const [state, setState] = useState();

  useEffect(() => {}, []);
  //删除
  const delAction = (id) => {
    onDeleteDraft(id);
  };
  //勾选批量删除
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
      console.log(selectedRowKeys);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      // console.log(selected, selectedRows, changeRows); //布尔值 勾选id
    },
    selections: [Table.SELECTION_NONE, Table.SELECTION_ALL],
  };
  //编辑
  const editDetail = (id) => {
    history.push({
      pathname: '/pto/supplier/initstatement',
      query: { id: id, check: 'draft' },
    });
  };
  const datasource = [
    {
      orderNo: '2021-11-29T15:20:46',
      orderTypeName: '2021-11-29T15:20:46',
      productCategoryName: '2021-11-29T15:20:46',
      products: 4321,
      id: 'few21r21v121',
    },
  ];
  const columns: any = [
    {
      title: '保存日期',
      dataIndex: 'createDate',
      key: 'createDate',
      align: 'center',
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
      width: 200,
    },
    {
      title: '账单开始日期',
      dataIndex: 'billingStartDate',
      key: 'billingStartDate',
      align: 'center',
      width: 200,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '账单截止日期',
      dataIndex: 'billingEndDate',
      key: 'billingEndDate',
      align: 'center',
      width: 200,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '条目数量',
      dataIndex: 'productNumber',
      key: 'productNumber',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      fixed: 'right',
      width: 200,
      render: (text, record) => {
        const _btnJsx = {
          Delete: (
            <AuthorityComponent type="Draft-Delete">
              <Popconfirm
                key="del"
                title="确定删除该草稿？"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => delAction(record.id)}
              >
                <Button type="link" danger>
                  <AuthorityComponent type="Draft">
                    <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
                  </AuthorityComponent>
                </Button>
              </Popconfirm>
            </AuthorityComponent>
          ),
          Edit: (
            <AuthorityComponent type="Draft-Edit">
              <Button key="edit" type="link" onClick={() => editDetail(record.id)}>
                <AuthorityComponent type="Draft">
                  <EditOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }} />
                </AuthorityComponent>
              </Button>
            </AuthorityComponent>
          ),
        };
        return <OperDiv>{butt.map((item) => _btnJsx[item])}</OperDiv>;
      },
    },
  ];
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={22}>
            <Col span={6}>
              <Form.Item name="searchDate" label="保存日期">
                <RangePicker separator="-" />
              </Form.Item>
            </Col>
            <Col span={2}>
              <AuthorityComponent type="Draft-Search">
                <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                  搜索
                </Button>
              </AuthorityComponent>
            </Col>
            <Col span={4}></Col>
            <Col span={7}></Col>
            <Col span={2} push={2}>
              <Button onClick={() => exportReport()}>
                <i className="iconfont icon-export"></i>
                <span>导出账单</span>
              </Button>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <div id="webTable">
          <Popconfirm
            key="del"
            title="确定删除所选条目?"
            icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
            onConfirm={() => onBatchDeletion(selectedRowKeys)}
          >
            <Button style={{ margin: '10px 10px' }}>批量删除</Button>
          </Popconfirm>
          <Table
            style={{ width: '100%' }}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            dataSource={data}
            rowSelection={rowSelection}
            pagination={{
              // total: total,
              // current: current,
              // pageSize: pageSize,
              showSizeChanger: true,
            }}
            onChange={onPageChange}
            scroll={{ y: scrollY }}
            columns={columns}
            rowKey="id"
          />
        </div>
      </TableWrapDiv>
    </div>
  );
});
