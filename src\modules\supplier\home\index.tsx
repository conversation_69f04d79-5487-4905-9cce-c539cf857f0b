import TableTitle from '@/components/TableTitle';
import { CaretLeftOutlined, CaretRightOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, DatePicker, Row } from 'antd';
import moment from 'moment';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import styled from 'styled-components';
import { Column } from '@ant-design/plots';
import { each, groupBy } from '@antv/util';
import { data1, data2 } from './mockData';
import { getCurrentSupplierHomeSummary } from '@/app/request/apiHome';

const HomeWrapper = styled.div`
  /* margin: 50px 10px; */
  /* height: 850px; */
  /* overflow: hidden; */
`;
const UpDiv = styled.div`
  display: flex;
  justify-content: space-between;
  .month {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: white;
    color: #999;
    width: 250px;
    padding: 10px 5px;
    border-radius: 10px;
    margin-bottom: 16px;
    .button {
      width: 30px;
      height: 30px;
      border-radius: 15px;
      background: #eee;
      text-align: center;
      line-height: 30px;
    }
  }
  .legend {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: white;
    color: #999;
    width: 500px;
    padding: 10px 5px;
    border-radius: 10px;
    li {
      padding: 0;
      margin: 0;
      list-style: none;
      display: flex;
    }
    .legendColor {
      margin: 0px 5px;
      width: 20px;
      height: 20px;
      border-radius: 5px;
    }
    .legendText {
      margin: 0px 5px;
    }
  }
`;
const DownDiv = styled.div`
  /* margin: 50px 20px; */
`;

const ChartCard = (props) => {
  const { title, data } = props;
  const annotations = [];
  each(groupBy(data, 'year'), (values, k) => {
    const value = values.reduce((a, b) => a + b.value, 0);
    annotations.push({
      type: 'text',
      position: [k, value],
      // content: `${value}`,
      content: `${value == 0 ? '' : value}`,
      style: {
        textAlign: 'center',
        fontSize: 14,
        fill: 'rgba(0,0,0,0.85)',
      },
      offsetY: -10,
    });
  });
  const config = {
    data: data,
    height: 280,
    padding: [32, 32, 32, 32],
    isStack: true,
    xField: 'year',
    yField: 'value',
    seriesField: 'type',
    legend: false,
    label: {
      // 可手动配置 label 数据标签位置
      position: 'middle',
      // 'top', 'bottom', 'middle'
      // 可配置附加的布局方法
      layout: [
        // 柱形图数据标签位置自动调整
        {
          type: 'interval-adjust-position',
        }, // 数据标签防遮挡
        {
          type: 'interval-hide-overlap',
        }, // 数据标签文颜色自动调整
        {
          type: 'adjust-color',
        },
      ],
    },
    colorField: 'type', // 部分图表使用 seriesField
    color: ({ type }) => {
      if (type === '已结算') {
        return '#5071c8';
      } else if (type === '待结算') {
        return '#f9c958';
      } else if (type === '待比对') {
        return '#91cd76';
      } else if (type === '被退回') {
        return '#ef6666';
      }
      return 'red';
    },
    // 使用 annotation （图形标注）来展示：总数的 label
    annotations,
  };
  return (
    <Card>
      <TableTitle title={title}></TableTitle>
      <Column {...config} />
    </Card>
  );
};

const SupplierHome = () => {
  const [data, setData] = useState([]);
  const [month, setMonth] = useState(moment().format('YYYY-MM'));

  const legendArr = useMemo(
    () => [
      {
        color: '#5071c8',
        name: '已结算',
      },
      {
        color: '#f9c958',
        name: '待结算',
      },
      {
        color: '#91cd76',
        name: '待比对',
      },
      {
        color: '#ef6666',
        name: '被退回',
      },
    ],
    [],
  );
  const handlePrevStep = useCallback(() => {
    setMonth(moment(month).subtract(1, 'month').format('YYYY-MM'));
  }, [month]);
  const handleNextStep = useCallback(() => {
    setMonth(moment(month).add(1, 'month').format('YYYY-MM'));
  }, [month]);
  const getData = useCallback(() => {
    getCurrentSupplierHomeSummary(month).then((res) => {
      if (res.success) {
        const result = res?.data?.map((item) => {
          const temp = [];
          item.items?.reverse()?.map((i) => {
            ['已结算', '待结算', '待比对', '被退回'].map((type) => {
              if (i.type == type) {
                i.value = i.value == 0 ? null : i.value;
                temp.push(i);
              }
            });
          });
          item.items = temp;
          return item;
        });

        setData(result);
      }
    });
  }, [data, month]);

  useEffect(() => {
    getData();
  }, [month]);
  return (
    <HomeWrapper>
      <UpDiv>
        <div className="month">
          <div className="button" onClick={handlePrevStep}>
            <CaretLeftOutlined />
          </div>
          <DatePicker style={{ textAlign: 'center' }} picker="month" value={moment(month)} />
          <div className="button" onClick={handleNextStep}>
            <CaretRightOutlined />
          </div>
        </div>
        <ul className="legend">
          {legendArr?.map((item, index) => {
            return (
              <li key={index}>
                <div className="legendColor" style={{ backgroundColor: item.color }} />
                <div className="legendText">{item.name}</div>
              </li>
            );
          })}
        </ul>
      </UpDiv>
      <DownDiv>
        <Row gutter={[20, 20]}>
          {data?.map((item) => {
            return (
              <Col span={12}>
                <ChartCard title={`${item?.productGroup} 月度销售走势`} data={item?.items} />
              </Col>
            );
          })}
        </Row>
      </DownDiv>
    </HomeWrapper>
  );
};
export default SupplierHome;
