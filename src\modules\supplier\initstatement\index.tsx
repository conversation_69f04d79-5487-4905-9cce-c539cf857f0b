import React, { memo } from 'react';
import { history } from 'umi';
import { <PERSON><PERSON><PERSON>eader, ButtonFooter, CardForm, CardTable, Picker } from './style';
import TableTitle from '@/components/TableTitle';
import { submitBillingInfo, checkSubmitValid } from '@/app/request/apiHome';
import {
  CopyOutlined,
  DeleteOutlined,
  PlusSquareOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import AuthorityComponent from '@/components/AuthorityComponent';
import useService from './useService';
import DebounceSelect from '../../../components/useDebounceSelect';
import { toThousands } from '@/tools/utils';
import moment from 'moment';
import {
  Button,
  Upload,
  Row,
  Col,
  Space,
  Form,
  Input,
  Select,
  DatePicker,
  Table,
  message,
  InputNumber,
  AutoComplete,
  Modal,
  Popconfirm,
} from 'antd';
const { Option } = Select;
const { confirm } = Modal;

export default memo((props) => {
  const {
    handleCustomSearch,
    supplierType,
    form,
    formRef,
    layout,
    inputRef,
    pageParams,
    clearingList,
    setFile,
    template,
    setTemplate,
    originID,
    setOriginID,
    clearingCompanyId,
    projectList,
    customerInfo,
    sourceSiteInfo,
    dataSource,
    setDataSource,
    importExcel,
    templateFile,
    disabledDate,
    handleSave,
    handleDel,
    handleAdd,
    handleAddSingle,
    handleCop,
    handleDraft,
    uniqueValidator,
    requiredValidator,
    getDataById,
    onPageChange,
    getSelectFields,
    rowSelection,
    handleBatchDelete,
    handleSelectClearing,
  } = useService();
  const columns: any = [
    {
      title: '序号',
      align: 'center',
      width: 50,
      fixed: 'left',
      render: (data, record, index) => {
        return <div>{index + 1}</div>;
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>日期</span>
        </>
      ), //必填项
      align: 'center',
      dataIndex: 'billingDate',
      key: 'billingDate',
      width: 150,
      render: (data, record, index) => {
        return (
          <Picker>
            <DatePicker
              defaultValue={data && moment(data)}
              value={data && moment(data)}
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              placeholder=""
              className={data > moment().format('YYYY-MM-DD') ? 'WrongTime' : ''}
              onChange={(date, dateString) => handleSave(dateString, 'billingDate', record, index)}
            />
          </Picker>
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>运输方式</span>
        </>
      ), //必填项
      align: 'center',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      width: 100,
      render: (data, record, index) => {
        return (
          <Select
            style={{ width: '100%' }}
            disabled={supplierType == 'EJV'}
            defaultValue={supplierType == 'EJV' ? '自提' : data}
            onChange={(value) => handleSave(value, 'modeTransport', record, index)}
          >
            <Option value="自提">自提</Option>
            <Option value="送货">送货</Option>
          </Select>
        );
      },
    },
    {
      title: '货源点/客户', //必填/非必填-根据运输方式
      align: 'center',
      key: 'sourcePoint',
      dataIndex: 'sourcePoint',
      width: 250,
      render: (data, record, index) => {
        if (record?.modeTransport === '自提') {
          return (
            <AutoComplete
              style={{ width: '100%', textAlign: 'left' }}
              defaultValue={data}
              placeholder="请选择货源点"
              onBlur={(e) => handleSave(e.target.value, 'sourcePoint', record, index)}
            >
              {sourceSiteInfo?.map((item, index) => {
                return (
                  <Option key={index} value={item.name}>
                    {item.name}
                  </Option>
                );
              })}
            </AutoComplete>
          );
        } else if (record?.modeTransport === '送货') {
          return (
            <DebounceSelect
              showSearch
              style={{ width: '100%' }}
              value={{ label: data, value: '' }}
              placeholder="请输入客户"
              initOptions={customerInfo}
              fetchOptions={handleCustomSearch}
              onChange={(newValue) => handleSave(newValue, 'customer', record, index)}
            />
          );
        }
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>车牌号</span>
        </>
      ), //必填项
      align: 'center',
      dataIndex: 'carNo',
      key: 'carNo',
      width: 110,
      render: (data, record, index) => {
        return (
          <Input
            defaultValue={data}
            onBlur={(e) => handleSave(e.target.value, 'carNo', record, index)}
            onPressEnter={(e) => handleSave(e.target.value, 'carNo', record, index)}
          />
        );
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>产品名称</span>
        </>
      ), //必填项
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
      width: 180,
      render: (data, record, index) => {
        return (
          <Select
            style={{ width: '100%' }}
            defaultValue={data}
            onChange={(value) => handleSave(value, 'productName', record, index)}
          >
            {projectList?.map((item, index) => {
              return (
                <Option key={index} value={item.productName}>
                  {item.productName}
                </Option>
              );
            })}
          </Select>
        );
      },
    },
    {
      title: '产品代码',
      align: 'center',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 100,
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>*</span>
          <span>数量</span>
        </>
      ), //必填项,选择了TO或M3则最多3位小数，否则必须为整数
      align: 'center',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      width: 150,
      render: (data, record, index) => {
        let count = null;
        if (record.unit == 'TO' || record.unit == 'M3') {
          count = Number(data).toFixed(3);
        } else {
          count = parseInt(data);
        }
        return (
          <InputNumber
            {...NumberProps}
            style={{ width: '100%' }}
            max={
              !record.productName.includes('长管氢气') && record.unit.includes('TO')
                ? 40
                : record.productName.includes('长管氢气') && record.unit.includes('M3')
                ? 6000
                : ''
            }
            defaultValue={Number(count) > 0 && count}
            onBlur={(e) => handleSave(e.target.value, 'productQuantity', record, index)}
            onPressEnter={(e) => handleSave(e.target.value, 'productQuantity', record, index)}
            step="0.001"
          />
        );
      },
    },
    {
      title: '单位', //必填项
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      width: 80,
      render: (data, record, index) => {
        let temp = data;
        if (record?.productName && record?.productName?.includes('长管氢气')) {
          temp = 'M3';
        } else if (record?.productName && !record?.productName?.includes('长管氢气')) {
          temp = 'TO';
        }
        return <div>{temp}</div>;
      },
    },
    supplierType != 'EJV'
      ? {
          title: (
            <>
              <span style={{ color: 'red' }}>*</span>
              <span>含税单价</span>
            </>
          ), //必填项
          align: 'center',
          dataIndex: 'unitPriceIncludingTax',
          key: 'unitPriceIncludingTax',
          width: 150,
          render: (data, record, index) => {
            return (
              <InputNumber
                disabled={supplierType === 'EJV'}
                style={{ width: '100%' }}
                max={
                  !record.productName.includes('长管氢气') && record.unit.includes('TO')
                    ? '10000'
                    : record.productName.includes('长管氢气') && record.unit.includes('M3')
                    ? '50'
                    : ''
                }
                defaultValue={Number(data) > 0 && String(toThousands(Number(data).toFixed(4)))}
                value={Number(data) > 0 && String(toThousands(Number(data).toFixed(4)))}
                onPressEnter={(e) => handleSave(e.target.value, 'unitPriceIncludingTax', record, index)}
                onBlur={(e) => handleSave(e.target.value, 'unitPriceIncludingTax', record, index)}
                stringMode={true}
                step="0.01"
              />
            );
          },
        }
      : {
          width: 0,
        },
    supplierType != 'EJV'
      ? {
          title: (
            <>
              <span style={{ color: 'red' }}>*</span>
              <span>不含税单价</span>
            </>
          ), //必填项
          align: 'center',
          dataIndex: 'unitPriceExcludingTax',
          key: 'unitPriceExcludingTax',
          width: 150,
          render: (data, record, index) => {
            return (
              <InputNumber
                disabled={true}
                style={{ width: '100%' }}
                defaultValue={Number(data) > 0 && String(toThousands(Number(data).toFixed(6)))}
                value={Number(data) > 0 && String(toThousands(Number(data).toFixed(6)))}
                onPressEnter={(e) => handleSave(e.target.value, 'unitPriceExcludingTax', record, index)}
                onBlur={(e) => handleSave(e.target.value, 'unitPriceExcludingTax', record, index)}
                stringMode={true}
                step="0.000001"
              />
            );
          },
        }
      : { width: 0 },
    supplierType != 'EJV'
      ? {
          title: '含税金额', //计算得出
          align: 'center',
          dataIndex: 'amountIncludingTax',
          key: 'amountIncludingTax',
          width: 100,
          render: (data, record, index) => {
            return <div>{data && toThousands(Number(data).toFixed(2))}</div>;
          },
        }
      : {
          width: 0,
        },
    supplierType != 'EJV'
      ? {
          title: '货币', //不可选择，自动带出，默认CNY
          align: 'center',
          dataIndex: 'currency',
          key: 'currency',
          width: 100,
          render: (data, record) => {
            return data ? <div>{data}</div> : <div>CNY</div>;
          },
        }
      : { width: 0 },
    {
      title: '单据尾号/DN#', //非必填，4位数字
      align: 'center',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      width: 150,
      render: (data, record, index) => {
        return (
          <Input
            style={{ textAlign: 'center' }}
            defaultValue={data}
            onPressEnter={(e) => handleSave(e.target.value, 'documentEndNumber', record, index)}
            onBlur={(e) => handleSave(e.target.value, 'documentEndNumber', record, index)}
          />
        );
      },
    },
    {
      title: '备注', //非必填
      align: 'center',
      dataIndex: 'remarks',
      width: 200,
      key: 'remarks',
      render: (data, record, index) => {
        return (
          <Input
            defaultValue={data}
            onPressEnter={(e) => handleSave(e.target.value, 'remarks', record, index)}
            onBlur={(e) => handleSave(e.target.value, 'remarks', record, index)}
          />
        );
      },
    },
    {
      title: '水容积(M3)',
      align: 'center',
      dataIndex: 'waterCapacityM3',
      key: 'waterCapacityM3',
      width: 150,
      render: (data, record, index) => {
        return (
          record?.productName?.includes('长管氢气') && (
            <InputNumber
              defaultValue={Number(data) > 0 && data}
              style={{ width: '100%' }}
              onPressEnter={(e) => handleSave(e.target.value, 'waterCapacityM3', record, index)}
              onBlur={(e) => handleSave(e.target.value, 'waterCapacityM3', record, index)}
              min="0"
              max="999999999999999.99"
              stringMode={true}
              step="0.01"
            />
          )
        );
      },
    },
    {
      title: '充装前压力(bar)',
      align: 'center',
      dataIndex: 'pressureBeforeBar',
      key: 'pressureBeforeBar',
      width: 150,
      render: (data, record, index) => {
        return (
          record?.productName?.includes('长管氢气') && (
            <InputNumber
              defaultValue={Number(data) > 0 && data}
              style={{ width: '100%' }}
              onPressEnter={(e) => handleSave(e.target.value, 'pressureBeforeBar', record, index)}
              onBlur={(e) => handleSave(e.target.value, 'pressureBeforeBar', record, index)}
              min="0"
              max="999999999999999.99"
              stringMode={true}
              step="0.1"
            />
          )
        );
      },
    },
    {
      title: '充装后压力(bar)',
      align: 'center',
      dataIndex: 'pressureAfterBar',
      key: 'pressureAfterBar',
      width: 150,
      render: (data, record, index) => {
        return (
          record?.productName?.includes('长管氢气') && (
            <InputNumber
              defaultValue={Number(data) > 0 && data}
              style={{ width: '100%' }}
              onPressEnter={(e) => handleSave(e.target.value, 'pressureAfterBar', record, index)}
              onBlur={(e) => handleSave(e.target.value, 'pressureAfterBar', record, index)}
              min="0"
              max="999999999999999.99"
              stringMode={true}
              step="0.1"
            />
          )
        );
      },
    },
    {
      title: '充装前温度(℃)',
      align: 'center',
      dataIndex: 'tempratureBefore',
      key: 'tempratureBefore',
      width: 150,
      render: (data, record, index) => {
        return (
          record?.productName?.includes('长管氢气') && (
            <InputNumber
              defaultValue={null}
              value={data || null}
              style={{ width: '100%' }}
              onPressEnter={(e) => handleSave(e.target.value, 'tempratureBefore', record, index)}
              onBlur={(e) => handleSave(e.target.value, 'tempratureBefore', record, index)}
              stringMode={true}
              step="0.1"
            />
          )
        );
      },
    },
    {
      title: '充装后温度(℃)',
      align: 'center',
      dataIndex: 'tempratureAfter',
      key: 'tempratureAfter',
      width: 150,
      render: (data, record, index) => {
        return (
          record?.productName?.includes('长管氢气') && (
            <InputNumber
              defaultValue={null}
              value={data || null}
              style={{ width: '100%' }}
              onPressEnter={(e) => handleSave(e.target.value, 'tempratureAfter', record, index)}
              onBlur={(e) => handleSave(e.target.value, 'tempratureAfter', record, index)}
              stringMode={true}
              step="0.1"
            />
          )
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: 180,
      render: (_, record, index) => {
        return (
          <Space>
            <Popconfirm
              key="del"
              title="确定删除该条记录？"
              icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() => handleDel(record)}
            >
              <DeleteOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: 'red' }} />
            </Popconfirm>
            <PlusSquareOutlined
              onClick={() => handleAdd(record, index)}
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
            />
            <CopyOutlined
              onClick={() => handleCop(record, index)}
              style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }}
            />
          </Space>
        );
      },
    },
  ];
  const NumberProps = {
    min: '0', //最小值
    // stringMode: true, //字符值模式，开启后支持高精度小数
    step: '0.001', //小数位数
    formatter: (value: any) => {
      //指定输入框展示值的格式
      const reg1 = `${value}`.replace(/^(\-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3');
      return reg1;
      //如果不需要添加千位分隔符可以直接返回return reg1
    },
  };
  const handleSubmit = async () => {
    // 添加必填校验
    const flag1 = requiredValidator(dataSource);
    // 添加重复行校验
    const flag2 = uniqueValidator(dataSource);
    if (flag1) {
      confirm({
        title: '是否确定当前账期内的所有条目都已录入？',
        icon: <ExclamationCircleOutlined />,
        onOk() {
          checkSubmitValid({
            ...form.getFieldsValue(),
            lindeClearingCompany: form.getFieldValue('lindeClearingCompany')?.label,
            lindeClearingCompanyId: form.getFieldValue('lindeClearingCompany')?.value,
            template,
            clearingCompanyId,
            id: originID,
            entryList: dataSource,
          }).then((res) => {
            if (res.success) {
              confirm({
                title: res.msg ? `${res.msg}是否提交` : '校验正确',
                icon: <ExclamationCircleOutlined />,
                onOk() {
                  submitBillingInfo({
                    ...form.getFieldsValue(),
                    lindeClearingCompany: form.getFieldValue('lindeClearingCompany')?.label,
                    lindeClearingCompanyId: form.getFieldValue('lindeClearingCompany')?.value,
                    template,
                    clearingCompanyId,
                    id: originID,
                    entryList: dataSource,
                  })
                    .then((res) => {
                      if (res.success) {
                        message.success({
                          content: '账单提交成功',
                          style: {
                            marginTop: '40vh',
                            marginLeft: '260px',
                          },
                        });
                        form?.resetFields();
                        setDataSource([]);
                        setTemplate('');
                        setOriginID('');
                        history.push('/pto/supplier/initstatement');
                      } else {
                        //提示信息
                        message.error({
                          content: res.msg,
                          style: {
                            marginTop: '40vh',
                            marginLeft: '260px',
                          },
                        });
                      }
                    })
                    .catch((e) => {
                      message.error(e);
                    });
                },
              });
            } else {
              message.error(res.msg);
            }
          });
        },
      });
    }
  };

  return (
    <>
      <ButtonHeader>
        <Space size="large">
          <span style={{ fontWeight: 800, color: '#fff' }}>
            {sessionStorage.getItem('supplierName')}({sessionStorage.getItem('supplierType')})
          </span>
          <AuthorityComponent type="Initstatement-Download">
            <Button href={`${process.env.WEB_URL}${templateFile}`}>新模板下载</Button>
          </AuthorityComponent>
          <AuthorityComponent type="Initstatement-Import">
            <Upload
              accept=".xls,.xlsx"
              customRequest={() => importExcel()}
              showUploadList={false}
              name="file"
              onChange={({ file: newFileList }) => setFile(newFileList)}
            >
              <Button>导入</Button>
            </Upload>
          </AuthorityComponent>
        </Space>
      </ButtonHeader>
      <CardForm>
        <TableTitle title="账单基本信息"></TableTitle>
        <Form form={form} {...layout} ref={formRef} layout="vertical">
          <Row gutter={20}>
            <Col span={6}>
              <Form.Item name="statementNumber" label="对账单号">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="submissionDate" label="提交日期">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="billingStartDate" label="账单开始日期">
                <DatePicker style={{ width: '100%' }} disabled placeholder="" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="billingEndDate" label="账单截至日期">
                <DatePicker style={{ width: '100%' }} disabled placeholder="" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="lindeClearingCompany"
                label={
                  <>
                    <span style={{ color: 'red' }}>*</span>
                    <span>林德结算公司</span>
                  </>
                }
              >
                <Select onChange={handleSelectClearing} labelInValue allowClear>
                  {clearingList?.map((item, index) => {
                    return (
                      <Option key={index} value={item.value}>
                        {item.label}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="region"
                label={
                  <>
                    <span style={{ color: 'red' }}>*</span>
                    <span>区域</span>
                  </>
                }
              >
                <Select>
                  <Option value="东区">东区</Option>
                  <Option value="北区">北区</Option>
                  <Option value="西南区">西南区</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="contacts"
                label={
                  <>
                    <span style={{ color: 'red' }}>*</span>
                    <span>联系人</span>
                  </>
                }
              >
                <Input ref={inputRef} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="phone"
                label={
                  <>
                    <span style={{ color: 'red' }}>*</span>
                    <span>联系电话</span>
                  </>
                }
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </CardForm>
      <CardTable>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <TableTitle title="账单明细信息"></TableTitle>
          <Space>
            {dataSource?.length <= 0 && (
              <AuthorityComponent type="InitStatement">
                <Button type="primary" onClick={handleAddSingle}>
                  新增账单
                </Button>
              </AuthorityComponent>
            )}
            <Popconfirm
              key="del"
              title="确定批量删除所选记录？"
              icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() => handleBatchDelete()}
            >
              <Button>批量删除</Button>
            </Popconfirm>
          </Space>
        </div>
        <div>
          <Table
            columns={columns}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            dataSource={dataSource}
            rowKey="order"
            rowSelection={rowSelection}
            pagination={{
              current: pageParams.pageIndex,
              pageSize: pageParams.pageSize,
              total: dataSource?.length,
              position: ['bottomCenter'],
              size: 'default',
              showQuickJumper: true,
              showSizeChanger: false,
            }}
            onChange={(pagination, filters, sorter) => {
              onPageChange(pagination);
            }}
            scroll={{ x: columns?.length * 200, y: 400 }}
          />
        </div>
      </CardTable>
      <ButtonFooter>
        <Space size="large">
          <AuthorityComponent type="Initstatement-Draft">
            <Button onClick={handleDraft} disabled={dataSource?.length == 0}>
              暂存草稿
            </Button>
          </AuthorityComponent>
          <AuthorityComponent type="Initstatement-Submit">
            <Button type="primary" onClick={handleSubmit} disabled={dataSource?.length == 0}>
              提交
            </Button>
          </AuthorityComponent>
        </Space>
      </ButtonFooter>
    </>
  );
});
