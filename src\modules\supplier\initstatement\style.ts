import { Card } from 'antd';
import styled from 'styled-components';

export const ButtonHeader = styled.div`
  display: flex;
  justify-content: right;
  margin-bottom: 20px;
`;
export const ButtonFooter = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 10px;
`;
export const CardForm = styled(Card)`
  margin-bottom: 20px;
`;
export const CardTable = styled(Card)`
  /* display: flex;
  justify-content: right;
  margin-bottom: 20px; */
  .stripe,
  .stripe .ant-table-cell-fix-right,
  .stripe .ant-table-cell-fix-left {
    background-color: #f9f9f9;
  }
  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: #beced4;
  }
`;
export const SectionTitle = styled.div`
  color: #0281bd;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0px;
  /* &::before {
    content: ' ';
  } */
`;
export const HeaderTitle = styled.div`
  color: #005293;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
`;
export const Picker = styled.div`
  .WrongTime {
    border: 1px solid red;
  }
`;
