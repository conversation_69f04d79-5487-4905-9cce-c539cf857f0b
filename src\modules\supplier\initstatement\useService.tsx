import { history } from 'umi';
import { Form, message, Table } from 'antd';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'umi';
import { getArrayIndex } from '../../internal/ReportForm/reportDataMaintenance/method';
import { importBilling, saveBillingDraft, queryBillingDetailById } from '@/app/request/apiHome';
import {
  queryClearCompanyOfSupplier,
  queryCustomerOfCurrentUser,
  queryParamList,
  queryProductInfo,
  querySourceSiteOfSupplier,
} from '@/app/request/apiInternal';
import { queryLoginUser } from '@/app/request/requestApi';
const newData = {
  id: '',
  order: null,
  key: '',
  billingDate: '',
  modeTransport: '',
  sourcePoint: '',
  carNo: '',
  productName: '',
  productQuantity: '',
  unit: '',
  unitPriceIncludingTax: '',
  unitPriceExcludingTax: '',
  amountIncludingTax: '',
  currency: '',
  documentEndNumber: '',
  remarks: '',
  productCode: '',
  waterCapacityM3: '',
  pressureBeforeBar: '',
  pressureAfterBar: '',
  tempratureBefore: '',
  tempratureAfter: '',
  region: '',
  lindeClearingCompany: '',
  totalVolume: '',
  totalAmountCNY: '',
  isTemp: '',
};

export default function useService() {
  const formRef = useRef<any>();
  const inputRef = useRef<any>(null);
  const [form] = Form.useForm();
  const location = useLocation<any>();
  const [clearingList, setClearingList] = useState([]);
  const [clearingInfo, setClearingInfo] = useState([]);
  const [fileList, setFile] = useState<any>();
  const [template, setTemplate] = useState('');
  const [originID, setOriginID] = useState('');
  const [clearingCompanyId, setClearingCompanyId] = useState('');
  const [taxRate, setTaxRate] = useState();
  const [isTaxRate, setIsTaxRate] = useState(null);
  const [projectList, setProjectList] = useState([]);
  const [customerInfo, setCustomerInfo] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [sourceSiteInfo, setSourceSiteInfo] = useState([]);
  const [originData, setOriginData] = useState([]);
  const [dataSource, setDataSource] = useState([]); //tem
  const supplierType = sessionStorage.getItem('supplierType');
  const isSap = sessionStorage.getItem('isSap');
  const [pageParams, setPageParams] = useState({ pageIndex: 1, pageSize: 30 });
  const layout: any = {
    requiredMark: true,
    labelCol: { span: 24 },
    wrapperCol: { span: 24 },
  };
  const templateFile = useMemo(() => {
    if (supplierType === '3rd Party') {
      return '/template/PTO Platform OS.xlsx';
    } else if (supplierType === 'EJV' && isSap == '1') {
      return '/template/PTO Platform EJV SAP.xlsx';
    } else if (supplierType === 'EJV' && isSap == '0') {
      return '/template/PTO Platform(EJV).xlsx';
    }
  }, []);
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys) => {
      setSelectedRowKeys([...newSelectedRowKeys]);
    },
    selections: [Table.SELECTION_NONE, Table.SELECTION_ALL],
  };

  const objectToFormData = (obj, form) => {
    const fd = form || new FormData();
    for (let property in obj) {
      if (obj.hasOwnProperty(property)) {
        // if the property is an object, but not a File, use recursivity.
        if (typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
          objectToFormData(obj[property], fd);
        } else if (obj[property] instanceof File) {
          // if it's a string or a File object
          // fd.append('file', obj[property]);
          fd.append('file', obj[property]);
        }
      }
    }
    return fd;
  };
  const importExcel = () => {
    const formData = new FormData();
    objectToFormData(fileList, formData);
    importBilling(formData)
      .then((res) => {
        if (res.success) {
          console.log(res?.data[0]?.lindeClearingCompany);
          res?.data[0]?.lindeClearingCompany != '' && form.setFieldsValue({ lindeClearingCompany: null });
          res?.data[0]?.lindeClearingCompany != ''
            ? form.setFieldsValue({
                lindeClearingCompany: res?.data?.length > 0 && {
                  label: res?.data[0]?.lindeClearingCompany,
                  value: res?.data[0]?.lindeClearingCompanyId,
                },
                region: res?.data?.length > 0 && res?.data[0].region,
              })
            : null;
          let formatData = [];
          const template = res?.data?.length > 0 && res?.data[0].template;
          formatData = res?.data?.map((item, index) => {
            if (supplierType === 'EJV') {
              item.modeTransport = '自提';
            }
            item.order = Math.random();
            item.unitPriceExcludingTax = (Number(item.unitPriceIncludingTax) / (1 + isTaxRate)).toFixed(6);
            return item;
          });
          setDataSource([...formatData]);
          setOriginData([...formatData]);
          setTemplate(template);
          setSelectedRowKeys([]);
          setClearingCompanyId(res?.data?.length > 0 ? res?.data[0]?.lindeClearingCompanyId : '');
          setPageParams({ pageIndex: 1, pageSize: 30 });
          message.success(res.msg);
        } else {
          message.error(res.msg);
        }
      })
      .catch((e) => {
        message.error(e);
      });
  };
  const disabledDate = (current) => {
    return current && current > moment();
  };
  const handleCustomSearch = useCallback(
    (value) => {
      return queryCustomerOfCurrentUser(value).then((res) => {
        if (res.success) {
          return res?.data?.map((item) => ({ label: item.customerTitle, value: item.customerId }));
        } else {
          message.error(res?.msg);
        }
      });
    },
    [customerInfo],
  );
  const handleSave = (value, field, record, index) => {
    const temp = [...dataSource];
    temp.map((item) => {
      if (item?.order === record?.order) {
        item[field] = value;
        // 运输方式
        if (field === 'modeTransport') {
          item.sourcePoint = '';
          // if (value === '自提') {
          //   message.warning('请填写货源点');
          // } else {
          //   message.warning('请填写客户名称');
          // }
          if (value === '自提' && true) {
            // item.sourcePoint === xxx   //如果选择自提并且货源点只有一项时，默认选中
          }
        }
        // 货源点/客户
        if (field === 'customer') {
          item.sourcePoint = value?.label;
        }
        // 产品名称
        if (field === 'productName') {
          if (value?.includes('长管氢气')) {
            item.unit = 'M3';
          } else {
            item.unit = 'TO';
          }
          item.productCode = projectList?.find((v) => v.productName === value)?.productCode;
        }
        // 数量
        if (field === 'productQuantity') {
          if ((value && value <= 0) || isNaN(value)) {
            message.warning({
              content: '数量，请输入正数',
              style: {
                marginTop: '40vh',
                marginLeft: '260px',
              },
            });
            item.productQuantity = null;
            item.amountIncludingTax = null;
            return false;
          }
          if (record.unit == 'TO' || record.unit == 'M3') {
            item.productQuantity = Number(value).toFixed(3);
          } else {
            item.productQuantity = Math.floor(value);
          }
          if (record?.unitPriceIncludingTax) {
            item.amountIncludingTax = item?.productQuantity * record?.unitPriceIncludingTax;
          }
        }
        // 单位
        if (field === 'unit') {
          if (value == 'TO' || value == 'M3') {
            item.productQuantity = Number(record.productQuantity).toFixed(3);
          } else {
            item.productQuantity = Math.floor(record.productQuantity);
          }
          if (record?.unitPriceIncludingTax && item?.productQuantity) {
            item.amountIncludingTax = item?.productQuantity * record?.unitPriceIncludingTax;
          }
        }
        // 含税单价
        if (field === 'unitPriceIncludingTax') {
          const formatValue = value?.replace(/,/gi, '');
          if ((formatValue && formatValue <= 0) || isNaN(formatValue)) {
            item.unitPriceIncludingTax = null;
            item.unitPriceExcludingTax = null;
            item.amountIncludingTax = null;
            message.warning({
              content: '含税单价，请输入正数',
              style: {
                marginTop: '40vh',
                marginLeft: '260px',
              },
            });
            return false;
          } else {
            item.unitPriceIncludingTax = Number(formatValue).toFixed(4);
            item.unitPriceExcludingTax = (Number(formatValue) / (1 + isTaxRate)).toFixed(4);
            if (record?.productQuantity) {
              item.amountIncludingTax = record?.productQuantity * formatValue;
            }
          }
        }
        // 不含税单价
        if (field === 'unitPriceExcludingTax') {
          const formatValue = value?.replace(/,/gi, '');
          if ((formatValue && formatValue <= 0) || isNaN(formatValue)) {
            item.unitPriceIncludingTax = null;
            item.unitPriceExcludingTax = null;
            item.amountIncludingTax = null;
            message.warning({
              content: '不含税单价，请输入正数',
              style: {
                marginTop: '40vh',
                marginLeft: '260px',
              },
            });
            return false;
          } else {
            if (formatValue) {
              item.unitPriceExcludingTax = Number(formatValue).toFixed(6);
              item.unitPriceIncludingTax = (Number(formatValue) * (1 + isTaxRate)).toFixed(2);
              if (record?.productQuantity) {
                const includingTax = (Number(formatValue) * (1 + isTaxRate)).toFixed(2);
                item.amountIncludingTax = record?.productQuantity * Number(includingTax);
              }
            }
          }
        }
        // 单据尾号
        if (field === 'documentEndNumber') {
          if (record.modeTransport && record.modeTransport !== '自提') {
            if (value && /^[a-zA-Z0-9]{4}$/.test(value)) {
              item.documentEndNumber = value;
            } else if (value) {
              item.documentEndNumber = '';
              message.warning({
                content: '单据尾号必须是4位数字或4位数字与字母的组合!',
                style: {
                  marginTop: '40vh',
                  marginLeft: '260px',
                },
              });
              return false;
            }
          }
        }
        // 水容积
        if (field === 'waterCapacityM3') {
          const afterValue = Number(value);
          if (isNaN(afterValue)) {
            message.warning({
              content: '请输入数字!',
              style: {
                marginTop: '40vh',
                marginLeft: '260px',
              },
            });
            item[field] = '';
            return false;
          } else if (afterValue < 0) {
            message.warning({
              content: '请输入大于0的数字',
              style: {
                marginTop: '40vh',
                marginLeft: '260px',
              },
            });
            item[field] = '';
            return false;
          } else {
            item[field] = afterValue.toFixed(2);
          }
        }
        // 充装前压力/充装后压力
        if (field === 'pressureBeforeBar' || field === 'pressureAfterBar') {
          const afterValue = Number(value);
          if (isNaN(afterValue)) {
            message.warning({
              content: '请输入数字',
              style: {
                marginTop: '40vh',
                marginLeft: '260px',
              },
            });
            item[field] = '';
            return false;
          } else if (afterValue < 0) {
            message.warning({
              content: '请输入大于0的数字',
              style: {
                marginTop: '40vh',
                marginLeft: '260px',
              },
            });
            item[field] = '';
            return false;
          } else {
            item[field] = afterValue.toFixed(1);
          }
        }
        // 充装前温度/充装后温度
        if (field === 'tempratureBefore' || field === 'tempratureAfter') {
          const afterValue = Number(value);
          if (isNaN(afterValue)) {
            message.warning({
              content: '请输入数字',
              style: {
                marginTop: '40vh',
                marginLeft: '260px',
              },
            });
            item[field] = '';
            return false;
          } else if (value) {
            item[field] = afterValue.toFixed(1);
          }
        }
      }
    });
    setDataSource([...temp]);
  };
  const handleDel = (record) => {
    let temp = [...dataSource].filter((item) => record?.order != item.order);
    let result = temp.map((item) => {
      item.order = Math.random();
      return item;
    });
    setDataSource([...result]);
  };
  const handleAdd = (record, index) => {
    let temp = [...dataSource];
    temp.splice(getArrayIndex(dataSource, record) + 1, 0, {
      ...newData,
      id: '',
      key: '',
      modeTransport: supplierType == 'EJV' ? '自提' : '',
    });
    let result = temp.map((item) => {
      item.order = Math.random();
      return item;
    });
    setDataSource([...result]);
  };
  const handleAddSingle = () => {
    const temp = [{ ...newData }].map((item, index) => {
      if (supplierType === 'EJV') {
        item.modeTransport = '自提';
      }
      item.order = Math.random();
      return item;
    });
    setDataSource([...temp]);
    setSelectedRowKeys([]);
  };
  const handleCop = (record, index) => {
    let temp = [...dataSource];
    let copyRow = temp?.find((item) => item?.order == record?.order);
    temp.splice(getArrayIndex(dataSource, record) + 1, 0, { ...copyRow, id: '', key: '' });
    let result = temp.map((item) => {
      item.order = Math.random();
      return item;
    });
    setDataSource([...result]);
  };
  const handleDraft = async () => {
    try {
      dataSource.forEach((item) => {
        item.unitPriceIncludingTax =
          item.unitPriceIncludingTax && String(item.unitPriceIncludingTax)?.replace(/,/gi, '');
      });
      saveBillingDraft({
        ...form.getFieldsValue(),
        lindeClearingCompany: form.getFieldValue('lindeClearingCompany')?.label,
        template,
        id: originID,
        entryList: dataSource,
      })
        .then((res) => {
          if (res.success) {
            message.success('已暂存草稿成功');
            form?.resetFields();
            setDataSource([]);
            setTemplate('');
            setOriginID('');
            history.push('/pto/supplier/initstatement');
          } else {
            message.error(res.msg);
          }
        })
        .catch((e) => {
          message.error(e);
        });
    } catch (errInfo) {
      message.error('请输入必填项');
    }
  };
  const uniqueValidator = (arr) => {
    console.log('uniqueValidator');
    for (var i = 0; i < arr.length; i++) {
      for (var j = i + 1; j < arr.length; j++) {
        if (
          arr[i].billingDate == arr[j].billingDate &&
          arr[i].modeTransport == arr[j].modeTransport &&
          arr[i].sourcePoint == arr[j].sourcePoint &&
          arr[i].carNo == arr[j].carNo &&
          arr[i].productName == arr[j].productName &&
          arr[i].productQuantity == arr[j].productQuantity &&
          arr[i].unit == arr[j].unit &&
          arr[i].unitPriceIncludingTax == arr[j].unitPriceIncludingTax &&
          arr[i].unitPriceExcludingTax == arr[j].unitPriceExcludingTax &&
          arr[i].amountIncludingTax == arr[j].amountIncludingTax &&
          arr[i].currency == arr[j].currency &&
          arr[i].documentEndNumber == arr[j].documentEndNumber &&
          arr[i].remarks == arr[j].remarks &&
          arr[i].waterCapacityM3 == arr[j].waterCapacityM3 &&
          arr[i].pressureBeforeBar == arr[j].pressureBeforeBar &&
          arr[i].pressureAfterBar == arr[j].pressureAfterBar &&
          arr[i].tempratureBefore == arr[j].tempratureBefore &&
          arr[i].tempratureAfter == arr[j].tempratureAfter
        ) {
          // 接口已有此校验
          // message.warning({
          //   content: `序号${i + 1} 和 序号${j + 1} 内容完全相同，请检查`,
          // });
          return false;
        }
      }
    }
    return true;
  };
  const requiredValidator = (dataSource) => {
    const { lindeClearingCompany, phone, region, contacts } = form.getFieldsValue();
    console.log('ppppppppp', lindeClearingCompany);
    //校验时间
    const time = dataSource.filter((item) => {
      return item?.billingDate == '';
    });
    //校验运输方式
    const transport = dataSource.filter((item) => {
      return item?.modeTransport == '';
    });
    //校验货源点
    const Source = dataSource.filter((item) => {
      return item?.sourcePoint == '';
    });
    //校验车牌号
    const NoCar = dataSource.filter((item) => {
      return item?.carNo == '';
    });
    //校验产品名称
    const productName = dataSource.filter((item) => {
      return item?.productName == '';
    });
    // 校验数量
    const quantity = dataSource.filter((item) => {
      return item?.productQuantity == '' || item.productQuantity == 0;
    });
    // 校验含税单价
    const unitPrice = dataSource.filter((item) => {
      return item?.unitPriceIncludingTax == '';
    });
    const isHydrogen = dataSource.filter((item) => {
      return item?.productName != '长管氢气' && item.productQuantity > 40;
    });
    const onHydrogen = dataSource.filter((item) => {
      return (item?.productName == '长管氢气' && item?.unitPriceIncludingTax > 50) || item?.unitPriceIncludingTax <= 0;
    });
    const isunitPrice = dataSource.filter((item) => {
      return item?.productName != '长管氢气' && item.unitPriceIncludingTax > 10000;
    });
    if (!lindeClearingCompany || !lindeClearingCompany?.label) {
      formRef.current.getFieldInstance('lindeClearingCompany').focus();
      message.warning({
        duration: 2,
        content: '请选择林德结算公司',
        style: {
          marginLeft: '260px',
        },
      });
      return false;
    } else if (!region) {
      formRef.current.getFieldInstance('region').focus();
      message.warning({
        maxCount: 2,
        content: '请选择区域',
        style: {
          marginLeft: '260px',
        },
      });
      return false;
    } else if (!contacts) {
      formRef.current.getFieldInstance('contacts').focus();
      message.warning({
        maxCount: 1,
        content: '请输入联系人',
        style: {
          marginLeft: '260px',
        },
      });
      return false;
    } else if (!phone) {
      formRef.current.getFieldInstance('phone').focus();
      message.warning({
        duration: 1,
        content: '请输入联系电话',
        style: {
          marginLeft: '260px',
        },
      });
      return false;
    } else if (time.length > 0) {
      message.warning({
        maxCount: 1,
        content: '请选择日期',
        style: {
          marginTop: '260px',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (transport.length > 0) {
      message.warning({
        content: '请选择运输方式',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (Source.length > 0) {
      message.warning({
        maxCount: 1,
        content: '请填写货源点/客户',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (NoCar.length > 0) {
      message.warning({
        content: '请输入车牌号',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (productName.length > 0) {
      message.warning({
        content: '请选择产品名称',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (quantity.length > 0) {
      message.warning({
        content: '数量不能为空',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (unitPrice.length > 0 && supplierType != 'EJV') {
      message.warning({
        content: '含税单价不能为空',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (isHydrogen.length > 0) {
      message.warning({
        content: '除氢气其他气体数量不会超过40',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (onHydrogen.length > 0 && supplierType != 'EJV') {
      message.warning({
        content: '氢气单价不会超过50且单价不能为0',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    } else if (isunitPrice.length > 0 && supplierType != 'EJV') {
      message.warning({
        content: '其他气体单价不会超过10000',
        style: {
          marginTop: '40vh',
          marginLeft: '260px',
        },
      });
      return false;
    }
    return true;
  };
  const getDataById = () => {
    queryBillingDetailById(location?.query?.id)
      .then((res) => {
        if (res.success) {
          form.setFieldsValue({
            ...res?.data,
            lindeClearingCompany: { label: res?.data?.lindeClearingCompany, value: '' },
            submissionDate: location?.query?.check === 'draft' ? null : res?.data?.submissionDate?.substring(0, 10),
            billingStartDate: moment(res?.data?.billingStartDate?.substring(0, 10)),
            billingEndDate: moment(res?.data?.billingEndDate?.substring(0, 10)),
          });
          setOriginID(res?.data?.id);
          setDataSource(
            res?.data?.entryList?.map((item, index) => {
              item.order = Math.random();
              return item;
            }),
          );
        } else {
          message.error(res.msg);
        }
      })
      .catch((e) => {
        message.error(e);
      });
  };
  const onPageChange = (pagination) => {
    let params = { ...pageParams };
    params.pageSize = pagination.pageSize;
    params.pageIndex = pagination.current;
    setPageParams(params);
  };
  const getSelectFields = useCallback(() => {
    queryClearCompanyOfSupplier('')
      .then((res) => {
        if (res.success) {
          setClearingList(
            res?.data?.map((item) => {
              return { label: item.name, value: item.code };
            }),
          );
          setClearingInfo(res.data);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    queryParamList()
      .then((res) => {
        if (res.success) {
          setTaxRate(res.data[0]?.systemClosingDate);
          form.setFieldsValue({
            ...res.data[0],
            systemClosingDate: res.data[0]?.systemClosingDate && moment(res.data[0].systemClosingDate),
          });
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    queryProductInfo('')
      .then((res) => {
        if (res.success) {
          setProjectList(res?.data); //{id,productName,productCode}
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    querySourceSiteOfSupplier('')
      .then((res) => {
        if (res.success) {
          setSourceSiteInfo(res?.data);
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
    queryCustomerOfCurrentUser('')
      .then((res) => {
        if (res.success) {
          setCustomerInfo(res?.data?.map((item) => ({ label: item.customerTitle, value: item.customerId })));
        } else {
          message.error(res?.msg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [clearingList, taxRate, projectList, sourceSiteInfo, customerInfo]);
  const handleBatchDelete = useCallback(() => {
    if (selectedRowKeys?.length <= 0) {
      message.warning('请先勾选账单');
    } else {
      const temp = dataSource?.filter((item1) => {
        return !selectedRowKeys?.includes(item1.order);
      });
      temp.forEach((item, index) => (item.order = Math.random()));
      console.log('temp', temp);
      setDataSource(temp);
    }
  }, [selectedRowKeys, dataSource]);
  const handleSelectClearing = (record) => {
    form.setFieldsValue({
      lindeClearingCompany: { label: record?.label, value: record?.value },
      region: clearingInfo?.find((item) => item.name == record?.label)?.region,
    });
  };
  useEffect(() => {
    if (location?.query?.id) {
      getDataById();
    } else {
      form?.resetFields();
      setDataSource([]);
      setTemplate('');
      setOriginID('');
    }
    //获取税率
    queryLoginUser().then((res) => {
      if (res.success) {
        setIsTaxRate(res.data.taxRate);
      }
    });
  }, [location]);
  useEffect(() => {
    getSelectFields();
  }, [1]);

  return {
    handleCustomSearch,
    importExcel,
    supplierType,
    form,
    formRef,
    layout,
    inputRef,
    pageParams,
    clearingList,
    setFile,
    template,
    setTemplate,
    originID,
    setOriginID,
    clearingCompanyId,
    projectList,
    customerInfo,
    sourceSiteInfo,
    dataSource,
    setDataSource,
    templateFile,
    disabledDate,
    handleSave,
    handleDel,
    handleAdd,
    handleAddSingle,
    handleCop,
    handleDraft,
    uniqueValidator,
    requiredValidator,
    getDataById,
    onPageChange,
    getSelectFields,
    rowSelection,
    handleBatchDelete,
    handleSelectClearing,
  };
}
