import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv, ButtonFooter } from '@/assets/style/list';
import {
  Form,
  Modal,
  Select,
  Input,
  Row,
  Col,
  Button,
  Table,
  Popconfirm,
  Card,
  DatePicker,
  Upload,
  message,
} from 'antd';
import { FileExclamationOutlined, ProfileOutlined } from '@ant-design/icons';
import moment from 'moment';
import { history } from 'umi';
import useServices from './useServices';
import TableTitle from '@/components/TableTitle';
import { classList, showOptionLabel } from '../../../components/StateVerification';
import AuthorityComponent from '@/components/AuthorityComponent';

export default memo(function (props) {
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    productCategory,
    btns,
    form,
    orderType,
    exportReport,
    scrollY,
    getTable,
    queryProductData,
    onInvoicing,
    information,
  } = useServices(props);
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '70px' },
  };
  const [outlineForm] = Form.useForm();
  const [show, setShow] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { RangePicker } = DatePicker;

  const [state, setState] = useState();
  const [title, setTitle] = useState('');
  const [isInstructions, setInstructions] = useState(false);
  useEffect(() => {}, []);

  //点击订单号
  const onDetails = (id: string) => {
    console.log(id);
    history.push({
      pathname: '/pto/supplier/details',
      state: { id: id, order: 'order' },
    });
  };
  //开票须知
  const onInvoicingNotice = (value) => {
    setInstructions(true);
    onInvoicing(value);
  };
  //关闭开票须知
  const onInstructions = () => {
    setInstructions(false);
  };
  const handleCancel = () => {
    outlineForm.resetFields();
    setIsModalVisible(false);
    setShow(false);
  };
  const columns: any = [
    {
      title: '订单号',
      dataIndex: 'poNo',
      key: 'poNo',
      align: 'center',
      render: (text, record) => {
        return (
          <AuthorityComponent type="Ordernumber-Details">
            <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.formatPoId)}>
              {text}
            </span>
          </AuthorityComponent>
        );
      },
      width: 150,
    },
    {
      title: '订单条目',
      dataIndex: 'poItem',
      key: 'poItem',
      align: 'center',
      width: 150,
    },
    {
      title: '结算日期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      align: 'center',
      width: 130,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '结算公司',
      dataIndex: 'clearingCompanyName',
      key: 'clearingCompanyName',
      align: 'center',
      ellipsis: true,
      width: 180,
      render: (text, record) => {
        return (
          <AuthorityComponent type="Ordernumber-Company">
            <span
              style={{ color: 'blue', cursor: 'pointer' }}
              onClick={() => onInvoicingNotice(record.clearingCompanyCode)}
            >
              {text}
            </span>
          </AuthorityComponent>
        );
      },
    },
    {
      title: '公司代码',
      dataIndex: 'clearingCompanyCode',
      key: 'clearingCompanyCode',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      align: 'center',
      width: 120,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      width: 120,
    },
    {
      title: '不含税单价',
      dataIndex: 'valnPrice',
      key: 'valnPrice',
      align: 'center',
      width: 140,
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      width: 120,
    },
  ];
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={23}>
            <Col span={6}>
              <Form.Item name="searchDate" label="结算月份">
                <DatePicker picker="month" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="productName" label="产品名称">
                <Select placeholder="产品名称" style={{ width: '100%' }} allowClear>
                  <Select.Option value="">所有</Select.Option>
                  {queryProductData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.productName}>
                        {x.productName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="keywords" label="关键字">
                <Input placeholder="关键字" className="keywordInput" />
              </Form.Item>
            </Col>
            <Col span={2}>
              <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                搜索
              </Button>
            </Col>
            <Col span={2}>
              <AuthorityComponent type="Tobesettle">
                <Button onClick={() => exportReport()}>
                  <i className="iconfont icon-export"></i>
                  <span>导出账单</span>
                </Button>
              </AuthorityComponent>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <div id="webTable">
          <Table
            style={{ width: '100%' }}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            dataSource={data}
            pagination={{
              total: total,
              current: current,
              pageSize: pageSize,
              showSizeChanger: true,
            }}
            onChange={onPageChange}
            scroll={{ y: scrollY }}
            columns={columns}
            rowKey="id"
          />
        </div>
      </TableWrapDiv>
      <Modal
        title={<div style={{ textAlign: 'center', fontWeight: 700 }}>开票须知</div>}
        width={'80%'}
        visible={isInstructions}
        onCancel={onInstructions}
        footer
        style={{ display: 'flex', justifyContent: 'space-between' }}
      >
        <Row gutter={24}>
          <Col span={12}>
            <Card
              title={
                <div>
                  <ProfileOutlined style={{ marginRight: '10px', color: '#005293' }} />
                  开票信息
                </div>
              }
              style={{ width: 400 }}
            >
              <p>
                <b style={{ color: '#005293', paddingRight: '10px' }}>结算公司:</b>
                {information.clearingCompanyName}
              </p>
              <p>
                <b style={{ color: '#005293', paddingRight: '10px' }}>工厂代码:</b>
                {information.companyCode}
              </p>
              {/* <p>
                <b style={{ color: '#005293', paddingRight: '10px' }}>公司代码:</b>
                {information.clearingCompanyCode}
              </p> */}
              <p>
                <b style={{ color: '#005293', paddingRight: '10px' }}>开票信息:</b>
                <br />
                {information.invoicingInfo}
              </p>
            </Card>
          </Col>
          <Col span={12}>
            <Card
              title={
                <div>
                  <FileExclamationOutlined style={{ marginRight: '10px', color: 'red' }} />
                  注意事项
                </div>
              }
              style={{ width: 400 }}
            >
              {information.notice}
            </Card>
          </Col>
        </Row>
        <ButtonFooter>
          <Button type="primary" onClick={() => onInstructions()}>
            关闭
          </Button>
        </ButtonFooter>
      </Modal>
    </div>
  );
});
