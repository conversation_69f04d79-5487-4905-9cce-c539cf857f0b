import { useRef, useState, useEffect } from 'react';
import { QuerySettlEntryPageInfo, exportSettlEntryList, queryProductInfo } from '@/app/request/requestApi';
import { history } from 'umi';
import { message, Form } from 'antd';
import { useLocation } from 'umi';
import moment from 'moment';
import { getTableScroll } from '@/tools/utils';

export default (props: any) => {
  const [scrollY, setScrollY] = useState<any>(0);
  const location = useLocation<any>();
  const [form] = Form.useForm();
  const [data, setTableData] = useState([]);
  const [total, setTotal] = useState(2);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableHeight, setHeight] = useState(0);
  const [orderType, setOrderType] = useState([]);
  const [productCategory, setProCategory] = useState([]);
  const [queryProductData, setQueryProductData] = useState(<any>[]);
  const [orderStatus, setOrderStatus] = useState([]);
  const [search, setSearch] = useState(<any>{
    status: '',
    orderType: '',
    productCategory: '',
    keyword: '',
    searchFromDate: '',
    searchToDate: '',
  });
  const [btns, setBtns] = useState([]);
  // 勾选框
  const [selectedRowKeys, setSelectRowKeys] = useState([]);
  const onSelectChange = (selectedKeys) => {
    setSelectRowKeys(selectedKeys);
  };

  //导出待结算的条目
  const exportReport = () => {
    exportSettlEntryList({}).then((res: any) => {
      if (res.response.status == 200) {
        let elink = document.createElement('a');
        // 设置下载文件名
        elink.download = '导出已结算.xlsx';
        elink.href = window.URL.createObjectURL(new Blob([res.response?.data]));
        elink.click();
        window.URL.revokeObjectURL(elink.href);
      } else {
        message.error(res.response.statusText);
      }
    });
  };

  const getTable = () => {
    let searchFromDate: string;
    let searchToDate: string;
    if (!form.getFieldValue('searchDate')) {
      searchFromDate = '';
      searchToDate = '';
    } else {
      searchFromDate = form.getFieldValue('searchDate')[0];
      searchToDate = form.getFieldValue('searchDate')[1];
    }
    //调用已结算的条目
    QuerySettlEntryPageInfo({
      ...form.getFieldsValue(),
      billingStartDate: searchFromDate,
      billingEndDate: searchToDate,
      pageIndex: current,
      pageSize: pageSize,
    }).then((res) => {
      if (res.success) {
        setTableData(res.data);
        setTotal(res.totalCount);
      } else {
        message.warning(res.msg);
      }
      resize(res.data);
    });
  };
  const dataRef = useRef<any>();
  const resize = (_data: any) => {
    let res = [];
    if (_data instanceof Array) {
      res = _data;
      dataRef.current = _data;
    } else {
      res = dataRef.current;
    }
    if (!res || res.length == 0) {
      setScrollY(getTableScroll(35));
    } else {
      setScrollY(getTableScroll());
    }
  };

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      // 清除订阅
      window.removeEventListener('resize', resize);
    };
  }, []);
  useEffect(() => {
    getTable();
  }, [current, pageSize]);
  //翻页：重置current
  const onPageChange = (pagination, filters, sorter) => {
    setSelectRowKeys([]);
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
  };
  useEffect(() => {
    //获取产品信息名称
    queryProductInfo('').then((res) => {
      if (res.success) {
        setQueryProductData(res.data);
      } else {
        message.warning(res.msg);
      }
    });
  }, [location]);
  const onValuesChange = (changedValues, allValues) => {
    setSearch({ ...allValues });
  };
  const onSearch = () => {
    if (current != 1) {
      setCurrent(1);
    } else {
      getTable();
    }
  };
  return {
    exportReport,
    search,
    onSearch,
    data,
    total,
    current,
    pageSize,
    orderType,
    productCategory,
    orderStatus,
    onValuesChange,
    onPageChange,
    getTable,
    tableHeight,
    form,
    scrollY,
    selectedRowKeys,
    setSelectRowKeys,
    onSelectChange,
    queryProductData,
  };
};
