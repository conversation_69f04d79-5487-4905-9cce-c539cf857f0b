import React, { memo, useState, useEffect } from 'react';
import { SearchDiv, TableWrapDiv, OperDiv, BtnGreenWrap } from '@/assets/style/list';
import { Form, Modal, Select, Input, Row, Col, Button, Table, Popconfirm, DatePicker, Upload, message } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { EyeOutlined } from '@ant-design/icons';
import { queryEntryDetail } from '@/app/request/requestApi';
import { history } from 'umi';
import useServices from './useServices';
import { classList, showOptionLabel } from '../../../components/StateVerification';
import AuthorityComponent from '@/components/AuthorityComponent';

export default memo(function (props) {
  const {
    data,
    current,
    pageSize,
    total,
    onPageChange,
    onSearch,
    productCategory,
    btns,
    form,
    orderType,
    exportReport,
    scrollY,
    getTable,
    queryProductData,
  } = useServices(props);
  const layout: any = {
    requiredMark: true,
    labelCol: { flex: '70px' },
  };
  const [outlineForm] = Form.useForm();
  const [show, setShow] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { RangePicker } = DatePicker;

  const [state, setState] = useState();
  const [title, setTitle] = useState('');
  const supplierType = sessionStorage.getItem('supplierType');
  // const supplierType = 'EJV';
  useEffect(() => {}, []);

  //点击对账单详情
  const onDetails = (id: string) => {
    console.log(id);
    history.push({
      pathname: '/pto/supplier/details',
      state: { id: id },
    });
  };

  const onParticulars = (record: any) => {
    queryEntryDetail(record.id).then((res) => {
      if (res.data) {
        res.data.productName == '长管氢气' ? setShow(true) : setShow(false);
        setTitle(res.data.sourcePoint);
        outlineForm.setFieldsValue({
          billingDate: moment(res.data.billingDate).format('YYYY-MM-DD'),
          modeTransport: res.data.modeTransport,
          sourcePoint: res.data.sourcePoint,
          licensePlate: res.data.licensePlate,
          productName: res.data.productName,
          productQuantity: res.data.productQuantity,
          unit: res.data.unit,
          carNo: res.data.carNo,
          status: showOptionLabel(classList, res.data.status),
          documentEndNumber: res.data.documentEndNumber,
          unitPriceIncludingTax: new Intl.NumberFormat('en-US', {}).format(res.data.unitPriceIncludingTax),
          priceExcludingTax: new Intl.NumberFormat('en-US', {}).format(res.data.priceExcludingTax),
          amountIncludingTax: new Intl.NumberFormat('en-US', {}).format(res.data.amountIncludingTax),
          unitPriceExcludingTax: new Intl.NumberFormat('en-US', {}).format(res.data.unitPriceExcludingTax),
          docTailNo: res.data.docTailNo,
          remarks: res.data.remarks,
          waterCapacity: res.data.waterCapacity,
          prechargePressure: res.data.prechargePressure,
          chargingPressure: res.data.chargingPressure,
          preChargeTemperature: res.data.preChargeTemperature,
          postChargeTemperature: res.data.postChargeTemperature,
          orderNo: res.data.orderNo,
          waterCapacityM3: res.data.waterCapacityM3,
          tempratureAfter: res.data.tempratureAfter,
          tempratureBefore: res.data.tempratureBefore,
          pressureAfterBar: res.data.pressureAfterBar,
          pressureBeforeBar: res.data.pressureBeforeBar,
        });
      } else {
        message.warning(res.msg);
      }
    });
    setIsModalVisible(true);
    if (record.productName === '长管氢气') {
      setShow(true);
    } else {
      setShow(false);
    }
  };
  const handleCancel = () => {
    outlineForm.resetFields();
    setIsModalVisible(false);
    setShow(false);
  };

  const columns: any = [
    {
      title: '对账单号',
      dataIndex: 'statementNumber',
      key: 'statementNumber',
      align: 'center',
      render: (text, record) => {
        return (
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => onDetails(record.billingId)}>
            {text}
          </span>
        );
      },
      width: 150,
    },
    {
      title: '日期',
      dataIndex: 'billingDate',
      key: 'billingDate',
      align: 'center',
      width: 150,
      render: (text, record) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '运输方式',
      dataIndex: 'modeTransport',
      key: 'modeTransport',
      align: 'center',
      width: 130,
    },
    {
      title: '车牌号',
      dataIndex: 'carNo',
      key: 'carNo',
      align: 'center',
      width: 100,
    },
    {
      title: '货源点/客户',
      dataIndex: 'sourcePoint',
      key: 'sourcePoint',
      align: 'center',
      ellipsis: true,
      width: 180,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '产品代码',
      align: 'center',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 100,
    },
    {
      title: '数量',
      dataIndex: 'productQuantity',
      key: 'productQuantity',
      align: 'center',
      width: 120,
      render: (text, _record) => {
        return text
          ? new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 3,
              maximumFractionDigits: 3,
            }).format(text)
          : '';
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center',
      width: 120,
    },
    {
      title: '含税单价',
      dataIndex: 'unitPriceIncludingTax',
      key: 'unitPriceIncludingTax',
      align: 'center',
      width: 120,
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4,
            }).format(text)
          : '';
      },
    },
    {
      title: '含税金额',
      dataIndex: 'amountIncludingTax',
      key: 'amountIncludingTax',
      align: 'center',
      width: 140,
      render: (text, record) => {
        return text
          ? new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(text)
          : '';
      },
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      align: 'center',
      width: 120,
    },
    {
      title: '单据尾号',
      dataIndex: 'documentEndNumber',
      key: 'documentEndNumber',
      align: 'center',
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
      fixed: 'right',
      width: 100,
      render: (text, record) => {
        return (
          <AuthorityComponent type="Tobesettle-Details">
            <Button key="edit" type="link" onClick={() => onParticulars(record)}>
              <EyeOutlined style={{ margin: '0 10px', cursor: 'pointer', fontSize: 20, color: '#005293' }} />
            </Button>
          </AuthorityComponent>
        );
      },
    },
  ];
  return (
    <div>
      <SearchDiv>
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Row gutter={23}>
            <Col span={6}>
              <Form.Item name="searchDate" label="日期">
                <RangePicker separator="-" />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name="productName" label="产品名称">
                <Select placeholder="产品名称">
                  <Select.Option value="">所有</Select.Option>
                  {queryProductData.map((x, index) => {
                    return (
                      <Select.Option key={index} value={x.productName}>
                        {x.productName}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="keywords" label="关键字">
                <Input placeholder="关键字" className="keywordInput" />
              </Form.Item>
            </Col>
            <Col span={2}>
              <AuthorityComponent type="Tobesettle-Search">
                <Button type="primary" className="searchBtn" htmlType="submit" onClick={onSearch}>
                  搜索
                </Button>
              </AuthorityComponent>
            </Col>
            <Col span={2}>
              <AuthorityComponent type="Tobesettle-Export">
                <Button onClick={() => exportReport()}>
                  <i className="iconfont icon-export"></i>
                  <span>导出账单</span>
                </Button>
              </AuthorityComponent>
            </Col>
          </Row>
        </Form>
      </SearchDiv>
      <TableWrapDiv>
        <div id="webTable">
          <Table
            style={{ width: '100%' }}
            rowClassName={(record, index) => (index % 2 == 0 ? '' : 'stripe')}
            dataSource={data}
            pagination={{
              total: total,
              current: current,
              pageSize: pageSize,
              showSizeChanger: true,
            }}
            onChange={onPageChange}
            scroll={{ y: scrollY }}
            columns={columns}
            rowKey="id"
          />
          <Modal
            className="edit_box"
            title="对账单产品信息查看"
            visible={isModalVisible}
            onCancel={handleCancel}
            width={650}
            footer
          >
            <Form {...layout} form={outlineForm}>
              <Row gutter={20}>
                <Col span={11}>
                  <Form.Item label="日期" labelCol={{ span: 10 }} name="billingDate">
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="状态" labelCol={{ span: 10 }} name="status">
                    <Input placeholder="状态" disabled />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={20}>
                <Col span={11}>
                  <Form.Item label="运输方式" name="modeTransport" labelCol={{ span: 10 }}>
                    <Input placeholder="运输方式" disabled />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="货源点/客户" name="sourcePoint" labelCol={{ span: 10 }}>
                    <Input placeholder="货源点/客户" title={title} disabled />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="车牌号" name="carNo" labelCol={{ span: 10 }}>
                    <Input placeholder="车牌号" disabled />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="产品名称" labelCol={{ span: 10 }} name="productName">
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="数量" name="productQuantity" labelCol={{ span: 10 }}>
                    <Input placeholder="数量" disabled />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="单位" name="unit" labelCol={{ span: 10 }}>
                    <Input disabled />
                  </Form.Item>
                </Col>
                {supplierType != 'EJV' && (
                  <>
                    <Col span={11}>
                      <Form.Item label="含税单价" name="unitPriceIncludingTax" labelCol={{ span: 10 }}>
                        <Input placeholder="含税单价" disabled />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item label="不含税单价" name="unitPriceExcludingTax" labelCol={{ span: 10 }}>
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item label="含税金额" name="amountIncludingTax" labelCol={{ span: 10 }}>
                        <Input disabled />
                      </Form.Item>
                    </Col>
                  </>
                )}
                <Col span={11}>
                  <Form.Item label="货币" name="currency" labelCol={{ span: 10 }}>
                    <Input placeholder="货币" defaultValue="CNY" disabled />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="单据尾号/DN#" labelCol={{ span: 10 }} name="documentEndNumber">
                    <Input placeholder="单据尾号/DN#" disabled />
                  </Form.Item>
                </Col>
              </Row>
              {show && (
                <div>
                  <Row gutter={20}>
                    <Col span={11}>
                      <Form.Item label="水容积(M3)" name="waterCapacityM3" labelCol={{ span: 10 }}>
                        <Input placeholder="水容积" disabled />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={20}>
                    <Col span={11}>
                      <Form.Item label="充装前压力(ban)" name="pressureBeforeBar" labelCol={{ span: 10 }}>
                        <Input placeholder="充装前压力" disabled />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item label="充装后压力(ban)" name="pressureAfterBar" labelCol={{ span: 10 }}>
                        <Input placeholder="充装后压力" disabled />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={20}>
                    <Col span={11}>
                      <Form.Item label="充装前温度(℃)" name="tempratureBefore" labelCol={{ span: 10 }}>
                        <Input placeholder="充装前温度" disabled />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item label="充装后温度(℃)" name="tempratureAfter" labelCol={{ span: 10 }}>
                        <Input placeholder="充装后温度" disabled />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              )}
              <Row>
                <Col span={22}>
                  <Form.Item label="备注" name="remarks">
                    <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} disabled />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={12} push={10}>
                  <Form.Item>
                    <Button type="primary" onClick={() => handleCancel()}>
                      关闭
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Modal>
        </div>
      </TableWrapDiv>
    </div>
  );
});
