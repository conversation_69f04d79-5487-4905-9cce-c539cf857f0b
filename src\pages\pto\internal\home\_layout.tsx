import MedalsoftLayout from '@/components/Layout';
import PersonalDropdown from '@/components/PersonalDropdown';
import useFormatLanguageService from '@/tools/formatLanguage';
import { useLocalStorageState } from 'ahooks';
import { Dropdown, Menu, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

export const DropdownWrapper = styled(Dropdown)`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
`;
export default function (props) {
  // const { formatMessage } = useFormatLanguageService();
  const [mode, setMode] = useLocalStorageState<'side' | 'top' | 'mix'>('layout-mode', 'mix');

  return (
    <MedalsoftLayout
      // layout={mode}
      navTheme="light"
      headerTheme="light"
    >
      {props.children}
    </MedalsoftLayout>
  );
}
