import Config from '@/app/config';
import MedalsoftLayout from '@/components/Layout';
import AppDownload, { ISource } from '@/components/AppDownload';
import { defaultRoute } from '@/components/Layout/defaultprop';
import Helper from '@/components/Helper';
import Language, { MenuItemWrapper } from '@/components/Language';
import PersonalDropdown from '@/components/PersonalDropdown';
import NoticeIcon from '@/components/NoticeIcon';
import { AuthService } from '@/modules/User/useAuthService';
import useFormatLanguageService from '@/tools/formatLanguage';
import { LayoutOutlined } from '@ant-design/icons';
import { useLocalStorageState } from 'ahooks';
import { Dropdown, Menu } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import styled from 'styled-components';
const Logo = require('@/assets/images/mso-logo-white.png');
import moment from 'moment';

export const DropdownWrapper = styled(Dropdown)`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
`;
export default function (props) {
  const { formatMessage } = useFormatLanguageService();
  const [mode, setMode] = useLocalStorageState<'side' | 'top' | 'mix'>('layout-mode', 'mix');
  const sources: ISource[] = [
    {
      url: 'https://play.google.com/store/apps/details?id=com.mindray.mylearning',
      agent: 'Android',
      description: 'Scan to download',
      icon: Logo,
    },
    {
      url: 'https://apps.apple.com/cn/app/m-academy/id1560551584',
      agent: 'iOS',
      description: 'Scan to download',
      icon: Logo,
    },
  ];
  const layoutModeMenu = (
    <Menu
      onClick={(menuItem: any) => {
        setMode(menuItem.key);
      }}
    >
      {['side', 'top', 'mix'].map((item) => (
        <MenuItemWrapper isSelected={item === mode} key={item}>
          <img src={require(`@/assets/images/layout_images/${item}.png`)} />
          {formatMessage(item)}
        </MenuItemWrapper>
      ))}
    </Menu>
  );
  const [userName, setUserName] = useState('');
  useEffect(() => {}, []);

  return (
    <MedalsoftLayout
      route={defaultRoute}
      search={true}
      layout={mode}
      navTheme="light"
      headerTheme="light"
      rightContentRender={() => [<PersonalDropdown userName={userName} />]}
    >
      {props.children}
    </MedalsoftLayout>
  );
}
