/* eslint-disable */
import Oidc from 'oidc-client';
import 'babel-polyfill';

var mgr = new Oidc.UserManager({
  userStore: new Oidc.WebStorageStateStore({
    store: window.sessionStorage,
  }),
  // authority:
  //   'https://distributor-test.shdchina.siemens-healthineers.cn/Identity',
  // redirect_uri:
  //   'https://distributor-test.shdchina.siemens-healthineers.cn/web/callback.html',
  // authority: process.env.IDENTITY,
  authority: process.env.IDENTITY,
  client_id: 'mvc',
  redirect_uri: process.env.REDIRECT_URL,
  // redirect_uri:  'http://localhost:8005/callback.html',
  // redirect_uri: process.env.REDIRECT_URL,
  response_type: 'code',
  scope: 'openid',
  post_logout_redirect_uri: window.location.origin + '/index.html',
  silent_redirect_uri: window.location.origin + '/silent-renew.html',
  accessTokenExpiringNotificationTime: 10,
  automaticSilentRenew: true,
  filterProtocolClaims: true,
  loadUserInfo: true,
});
console.log("%c [ process.env.REDIRECT_URL ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", process.env.REDIRECT_URL)

Oidc.Log.logger = console;
Oidc.Log.level = Oidc.Log.INFO;

mgr.events.addUserLoaded(function (user) {
  console.log('New User Loaded：', arguments);
  console.log('Acess_token: ', user.access_token);
});

mgr.events.addAccessTokenExpiring(function () {
  console.log('AccessToken Expiring：', arguments);
});

mgr.events.addAccessTokenExpired(function () {
  console.log('AccessToken Expired：', arguments);
  alert('Session expired. Going out!');
  mgr
    .signoutRedirect()
    .then(function (resp) {
      console.log('signed out', resp);
    })
    .catch(function (err) {
      console.log(err);
    });
});

mgr.events.addSilentRenewError(function () {
  console.error('Silent Renew Error：', arguments);
});

export default class SecurityService {
  // Renew the token manually 手动续订token
  renewToken() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .signinSilent()
        .then(function (user) {
          if (user == null) {
            self.signIn(null);
          } else {
            return resolve(user);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }

  // Get the user who is logged in. 获取登录的用户
  getUser() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .getUser()
        .then(function (user) {
          if (user == null) {
            self.signIn();
            console.log(1111)
            return resolve(null);
          } else {
            return resolve(user);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }

  // Check if there is any user logged in 检查是否有任何用户登录
  getSignedIn() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .getUser()
        .then(function (user) {
          if (user == null) {
            self.signIn();
            console.log(2222)

            return resolve(false);
          } else {
            return resolve(true);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }

  // Redirect of the current window to the authorization endpoint. 将当前窗口重定向到授权端点。
  signIn() {
    mgr.signinRedirect().catch(function (err) {
      console.log(err);
    });
  }

  // Redirect of the current window to the end session endpoint. 将当前窗口重定向到结束会话端点
  signOut() {
    mgr
      .signoutRedirect()
      .then(function (resp) {
        console.log('signed out', resp);
      })
      .catch(function (err) {
        console.log(err);
      });
  }

  // Get the profile of the user logged in. 获取登录的用户的个人资料
  getProfile() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .getUser()
        .then(function (user) {
          if (user == null) {
            self.signIn();
            return resolve(null);
          } else {
            return resolve(user.profile);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }

  // Get the token id. 获取tokenID
  getIdToken() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .getUser()
        .then(function (user) {
          if (user == null) {
            console.log(3333)

            self.signIn();
            return resolve(null);
          } else {
            return resolve(user.id_token);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }

  // Get the session state. 获取会话状态
  getSessionState() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .getUser()
        .then(function (user) {
          if (user == null) {
            console.log(4444)

            self.signIn();
            return resolve(null);
          } else {
            return resolve(user.session_state);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }

  // Get the access token of the logged in user. 获取登录用户的访问token
  getAcessToken() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .getUser()
        .then(function (user) {
          if (user == null) {
            console.log(5555)

            self.signIn();
            return resolve(null);
          } else {
            return resolve(user.access_token);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }

  // Takes the scopes of the logged in user. 获取登录用户登录的范围
  getScopes() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .getUser()
        .then(function (user) {
          if (user == null) {
            console.log(6666)

            self.signIn();
            return resolve(null);
          } else {
            return resolve(user.scopes);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }

  // Get the user roles logged in. 登录用户角色
  getRole() {
    let self = this;
    return new Promise((resolve, reject) => {
      mgr
        .getUser()
        .then(function (user) {
          if (user == null) {
            console.log(7777)

            self.signIn();
            return resolve(null);
          } else {
            return resolve(user.profile.role);
          }
        })
        .catch(function (err) {
          console.log(err);
          return reject(err);
        });
    });
  }
}
