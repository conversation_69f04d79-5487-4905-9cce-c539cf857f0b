import { keyframes } from 'styled-components';
import { useIntl } from 'umi';
import getServiceContext from './getServiceContext';
export const FormatLanguageService = getServiceContext(
  useFormatLanguageService,
);

export type IFormatLanguageService = ReturnType<
  typeof useFormatLanguageService
>;

export default function useFormatLanguageService(enable = true) {
  const intl = useIntl();

  const formatMessage = (key: string, attr = undefined) => {
    if (enable) {
      return intl.formatMessage({ id: key }, attr);
    } else {
      let str = key ?? '';
      Object.keys(attr ?? {}).forEach((k) => {
        str = str.replaceAll(`{${k}}`, attr[k]);
      });
      return str;
    }
  };

  return { formatMessage };
}
