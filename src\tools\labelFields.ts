export default {
  dataLakeField: {
    CustomerZH: '中文名称',
    CustomerEN: '英文名称',
    Province: '省份',
    City: '城市',
    Address: '地址',
    OrgCode: '组织机构代码',
    Street: '街道',
    PostalCode: '邮编号码',
    Area: '区域',
    Partition: '分区',
    ClassGrade: '分类等级',
    ClassProp: '分类属性',
    CNOCCode: 'CNOC号',
    Modified: '最后修改时间',
    ModifiedBy: '最后修改人',
    CustomerCode: '客户编号',
    CreditRating: '信用评级',
    ContactPhone: '联系方式',
  },
  qccField: {
    Partners: '投资人及出资信息',
    Employees: '主要人员',
    Branches: '分支机构',
    ChangeRecords: '变更信息',
    ContactInfo: '联系信息',
    Industry: '行业分类',
    Area: '所属地区',
    RevokeInfo: '注销吊销信息',
    InsuredCount: '参保人数',
    EnglishName: 'Q英文名',
    PersonScope: '人员规模',
    IXCode: '进出口企业代码',
    TagList: '标签列表',
    ARContactList: '最新企业年报中的联系方式',
    EconKindCodeList: '企业类型数组',
    Name: 'Q公司名称',
    No: '注册号',
    BelongOrg: '登记机关',
    OperName: '法定代表人',
    StartDate: '成立日期',
    EndDate: '吊销日期',
    Status: '状态',
    Province: '省份',
    UpdatedDate: '更新日期',
    CreditCode: '信用代码',
    RegistCapi: '注册资本',
    EconKind: '类型',
    Address: '注册地址',
    Scope: '营业范围',
    TermStart: '营业期限始',
    TeamEnd: '营业期限至',
    CheckDate: '发照日期',
    OrgNo: 'Q组织机构代码',
    IsOnStock: '是否上市',
    StockNumber: '股票代码',
    StockType: '上市类型',
    OriginalName: '曾用名',
    ImageUrl: 'Logo 地址',
    EntType: '企业类型',
    RecCap: '实缴资本',
  },
  specialField: {
    CNOCCode: 'cnocCode',
    IXCode: 'ixCode',
    ARContactList: 'arContactList',
  },
  qccListField: {
    changeRecords: '变更信息',
    arContactList: '最新企业年报中的联系方式',
    branches: '分支机构',
    employees: '主要人员',
    originalName: '曾用名',
    partners: '投资人及出资信息',
    tagList: '标签列表',
  },
  qccRevokeInfoField: {
    CancelDate: '注销日期',
    RevokeReason: '吊销原因',
    RevokeDate: '吊销日期',
    CancelReason: '注销原因',
  },
  qccChangeRecordsField: {
    BeforeContent: '变更前内容',
    AfterContent: '变更后内容',
    ProjectName: '变更事项',
    ChangeDate: '变更日期',
  },
  qccIndustryField: {
    Industry: '行业门类描述',
    SmallCategoryCode: '行业小类 code',
    MiddleCategory: '行业中类描述',
    SubIndustryCode: '行业大类 code',
    MiddleCategoryCode: '行业中类 code',
    IndustryCode: '行业门类 code',
    SubIndustry: '行业大类描述',
    SmallCategory: '行业小类描述',
  },
  qccAreaField: {
    County: '区域',
    Province: '省份',
    City: '城市',
  },
  qccOriginalNameField: {
    Name: '名称',
    ChangeDate: '变更日期',
  },
  qccBranchesField: {
    CompanyId: '公司KeyNo',
    RegNo: '统一社会信用代码/注册号',
    Name: '名称',
    BelongOrg: '登记机关',
    CreditCode: '统一社会信用代码',
    OperName: '法定代表人姓名',
  },
  qccPartnersField: {
    KeyNo: 'KeyNo',
    StockName: '投资人/注册号',
    StockType: '投资人类型',
    StockPercent: '出资比例',
    ShouldCapi: '认缴出资额',
    ShoudDate: '认缴出资时间',
    InvestType: '认出资方式',
    InvestName: '实出资方式',
    RealCapi: '实缴出资额',
    CapiDate: '实缴时间',
    TagsList: '股东标签数组',
    FinalBenefitPercent: '最终受益股份',
    RelatedProduct: '关联产品',
    RelatedOrg: '关联机构',
  },
  qccRelatedProductField: {
    Id: 'Id',
    Name: '产品名称',
    Round: '融资轮次',
    FinancingCount: '融资次数',
  },
  qccRelatedOrgField: {
    Id: 'Id',
    Name: '机构名称',
  },
  qccEmployeesField: {
    KeyNo: 'KeyNo',
    Name: '名称',
    Job: '职位',
  },
  qccTagListField: {
    Type: '类型',
    Name: '类型说明',
  },
  qccContactInfoField: {
    WebSite: '网址',
    PhoneNumber: '联系电话',
    Email: '邮箱',
  },
  qccWebSiteField: {
    Name: '网址名称',
    Url: '网址地址',
  },
  qccARContactListField: {
    ContactNo: '企业联系电话',
    EmailAddress: '电子邮箱',
    Address: '企业通讯地址',
  },
};
