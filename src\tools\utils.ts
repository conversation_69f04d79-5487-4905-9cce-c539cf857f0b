export function getClientHeight() {
  var clientHeight = 0;
  if (document.body.clientHeight && document.documentElement.clientHeight) {
    var clientHeight =
      document.body.clientHeight < document.documentElement.clientHeight
        ? document.body.clientHeight
        : document.documentElement.clientHeight;
  } else {
    var clientHeight =
      document.body.clientHeight > document.documentElement.clientHeight
        ? document.body.clientHeight
        : document.documentElement.clientHeight;
  }
  return clientHeight;
}

export function getTableScroll(extraHeight?, id?) {
  if (typeof extraHeight == 'undefined') {
    //  默认底部分页72 + 边距20
    extraHeight = 92;
  }
  let tHeader = null;
  if (id) {
    tHeader = document.getElementById(id)
      ? document.getElementById(id).getElementsByClassName('ant-table-thead')[0]
      : null;
  } else {
    tHeader = document.getElementsByClassName('ant-table-thead')[0];
  }
  //表格内容距离顶部的距离
  let tHeaderBottom = 0;
  let tHeaderTop = 0;
  if (tHeader) {
    tHeaderBottom = tHeader.getBoundingClientRect().bottom;
    tHeaderTop = tHeader.getBoundingClientRect().top;
  }
  //窗体高度-表格内容顶部的高度-表格内容底部的高度
  let height = getClientHeight() - tHeaderBottom - extraHeight;
  // let height = `calc(100vh - ${tHeaderBottom + extraHeight}px)`;
  let _table: any = document.getElementsByClassName('ant-table')[0];
  if (_table) {
    _table.style.minHeight = getClientHeight() - tHeaderTop - extraHeight + 'px';
  }
  let _tables: any = document.querySelector('.ant-table .ant-table-body');
  if (_tables) {
    if (height < 100) {
      height = 100;
    }
    _tables.style.height = height + 'px';
    //     getClientHeight() - tHeaderBottom - extraHeight + 'px';
  }
  checkScrollBar(height);
  return height;
}
export const checkScrollBar = (height: any) => {
  let antTableBody = document.querySelector('.ant-table-body');

  let antTableBodyScrollHeight = antTableBody && antTableBody.scrollHeight;

  let antTableFixHeader = document.querySelector('.ant-table-container');

  if (antTableBodyScrollHeight < height) {
    antTableFixHeader && antTableFixHeader.classList.add('change-scrollBal');
  } else {
    antTableFixHeader && antTableFixHeader.classList.remove('change-scrollBal');
  }
};

// 数字转千分符  '4435435435435.77878787878'  =>  '4,435,435,435,435.77,878,787,878'
export function toThou(str) {
  var reg = /(\d)(?=(?:\d{3})+\b)/g;
  return str.replace(reg, '$1,');
}

// 数字转千分符   '4435435435435.77878787878'  =>  '4,435,435,435,435.77878787878'
// export function toThousands(num) {
export function toThousands(num) {
  num = num.toString().split('.'); // 分隔小数点
  var arr = num[0].split('').reverse(); // 转换成字符数组并且倒序排列
  var res = [];
  for (var i = 0, len = arr.length; i < len; i++) {
    if (i % 3 === 0 && i !== 0) {
      res.push(','); // 添加分隔符
    }
    res.push(arr[i]);
  }
  res.reverse(); // 再次倒序成为正确的顺序
  if (num[1]) {
    // 如果有小数的话添加小数部分
    res = res.join('').concat('.' + num[1]);
  } else {
    res = res.join('');
  }
  return res;
}
toThousands(4444.5555);

// 千分符转数字
export function toFigure(str) {
  var reg = str?.replace(/,/g, '');
  return reg;
}
//获取当前时间往前的日期
// getNewDate("before", 3) //今天向前3个月
// getNewDate("after", 2)  //今天向后2个月
export function getNewDate(flag, many) {
  const thirtyDays = [4, 6, 9, 11]; // 30天的月份
  const thirtyOneDays = [1, 3, 5, 7, 8, 10, 12]; // 31天的月份
  const currDate = new Date(); // 今天日期
  const year = currDate.getFullYear();
  let month = currDate.getMonth() + 1;
  let targetDateMilli = 0;
  let GMTDate = ''; // 中国标准时间
  let targetYear = ''; // 年
  let targetMonth = ''; // 月
  let targetDate = ''; // 日
  let dealTargetDays = ''; // 目标日期
  const isLeapYear = !!((year % 4 == 0 && year % 100 != 0) || year % 400 == 0); // 是否是闰年
  // console.log(isLeapYear, "isLeapYear");
  let countDays = 0; // 累计天数
  for (let i = 0; i < many; i++) {
    if (flag === 'before') {
      month = month - 1 <= 0 ? 12 : month - 1;
    } else {
      month = month + 1 > 12 ? 1 : month + 1;
    }
    thirtyDays.includes(month)
      ? (countDays += 30)
      : thirtyOneDays.includes(month)
      ? (countDays += 31)
      : isLeapYear
      ? (countDays += 29)
      : (countDays += 28);
  }
  targetDateMilli = currDate.setDate(currDate.getDate() - (flag === 'before' ? countDays : countDays * -1));
  GMTDate = new Date(targetDateMilli);
  targetYear = GMTDate.getFullYear();
  targetMonth = GMTDate.getMonth() + 1;
  targetDate = GMTDate.getDate();
  targetMonth = targetMonth.toString().padStart(2, '0');
  targetDate = targetDate.toString().padStart(2, '0');
  dealTargetDays = `${targetYear}-${targetMonth}-${targetDate}`;
  // console.log(dealTargetDays, '处理的日期啊');
  return dealTargetDays;
}
