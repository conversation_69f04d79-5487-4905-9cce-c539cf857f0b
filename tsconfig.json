{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "allowJs": true, "jsx": "react-jsx", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": false, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"]}, "allowSyntheticDefaultImports": true, "outDir": "dist"}, "include": ["mock/**/*", "src/**/*", "src/app/config/**/*", ".umirc.ts", "typings.d.ts", "global.d.ts", "services"]}